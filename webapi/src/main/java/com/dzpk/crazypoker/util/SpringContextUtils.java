package com.dzpk.crazypoker.util;


import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;


/**
 * SpringContextUtils
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Component
public class SpringContextUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    public static class ContextNotReadyException extends RuntimeException {
        public ContextNotReadyException(String message) {
            super(message);
        }
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    /**
     * 上下文状态检查
     */
    private static void checkContextState() {
        if (applicationContext == null) {
            throw new ContextNotReadyException("Spring上下文未初始化，请确保容器已启动");
        }
    }

    /**
     * 获取Bean（按类型）
     * @param clazz Bean类型
     * @return 匹配的Bean实例
     * @throws ContextNotReadyException 上下文未初始化时抛出
     */
    public static <T> T getBean(Class<T> clazz) {
        checkContextState();
        return applicationContext.getBean(clazz);
    }

    /**
     * 获取Bean（按名称和类型）
     * @param name Bean名称
     * @param clazz Bean类型
     * @return 匹配的Bean实例
     * @throws BeansException 类型不匹配时抛出
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        checkContextState();
        return applicationContext.getBean(name, clazz);
    }

    /**
     * 获取所有实现指定接口的Bean
     * @param interfaceType 接口类型
     * @return Bean名称与实例的映射
     */
    public static <T> Map<String, T> getBeansOfType(Class<T> interfaceType) {
        checkContextState();
        return applicationContext.getBeansOfType(interfaceType);
    }

    /**
     * 动态注册单例Bean（线程安全）
     * @param beanName Bean名称
     * @param bean Bean实例
     * @throws UnsupportedOperationException 当上下文不可配置时抛出
     */
    public static synchronized void registerSingleton(String beanName, Object bean) {
        checkContextState();
        if (applicationContext instanceof ConfigurableApplicationContext) {
            ((ConfigurableApplicationContext) applicationContext).getBeanFactory()
                    .registerSingleton(beanName, bean);
        } else {
            throw new UnsupportedOperationException("当前ApplicationContext不支持动态注册");
        }
    }

    /**
     * 动态注册原型Bean
     * @param beanClass Bean类型
     * @param constructorArgs 构造参数
     */
    public static synchronized <T> void registerPrototype(String beanName, Class<T> beanClass, Object... constructorArgs) {
        checkContextState();
        if (applicationContext instanceof ConfigurableApplicationContext) {
            BeanDefinitionBuilder builder = BeanDefinitionBuilder
                    .genericBeanDefinition(beanClass)
                    .setScope(BeanDefinition.SCOPE_PROTOTYPE);

            for (Object arg : constructorArgs) {
                builder.addConstructorArgValue(arg);
            }

            BeanDefinitionRegistry registry = (BeanDefinitionRegistry)
                    ((ConfigurableApplicationContext) applicationContext).getBeanFactory();
            registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
        } else {
            throw new UnsupportedOperationException("当前ApplicationContext不支持动态注册");
        }
    }

    /**
     * 获取当前环境配置
     */
    public static Environment getEnvironment() {
        checkContextState();
        return applicationContext.getEnvironment();
    }

    /**
     * 判断当前是否指定环境
     * @param profile 环境名称（如："dev", "prod"）
     */
    public static boolean isProfileActive(String profile) {
        return Optional.of(getEnvironment())
                .map(env -> Arrays.asList(env.getActiveProfiles()))
                .orElse(Collections.emptyList())
                .contains(profile);
    }

    /**
     * 安全获取Bean（当Bean不存在时返回Optional）
     */
    public static <T> Optional<T> getBeanOptional(Class<T> clazz) {
        try {
            return Optional.of(getBean(clazz));
        } catch (BeansException e) {
            return Optional.empty();
        }
    }

    /**
     * 安全获取Bean（带默认值）
     */
    public static <T> T getBeanOrDefault(Class<T> clazz, T defaultValue) {
        return getBeanOptional(clazz).orElse(defaultValue);
    }

    /**
     * 获取应用名称
     */
    public static String getApplicationName() {
        return getEnvironment().getProperty("spring.application.name", "");
    }

    /**
     * 打印所有Bean信息（调试用）
     */
    public static void printAllBeans() {
        checkContextState();
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        Arrays.stream(beanNames)
                .sorted()
                .forEach(name -> {
                    Class<?> type = applicationContext.getType(name);
                    System.out.printf("Bean: %-40s Type: %s\n", name, type != null ? type.getSimpleName() : "Unknown");
                });
    }
}
