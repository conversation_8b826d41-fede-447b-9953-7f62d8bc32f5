package com.dzpk.crazypoker.room.service.transaction.impl;

import com.dzpk.crazypoker.appmessage.dao.AppBusinessMessageDao;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.room.repositories.mysql.IRoomPumpDao;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.mapper.*;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.model.*;
import com.dzpk.crazypoker.room.service.transaction.IRoomTransation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class RoomTransactionImpl implements IRoomTransation {
    @Autowired
    private RoomSearchPoMapper roomSearchDao;

    @Autowired
    private DzRoomPoMapper roomDao;

    @Autowired
    private SngRoomPoMapper sngRoomDao;

    @Autowired
    private MttRoomPoMapper mttRoomDao;

    @Autowired
    private BpRoomPoMapper bpRoomDao;

    @Autowired
    private RoomJackpotSettionPoMapper jpSettingDao;

    @Autowired
    private IRoomPumpDao iRoomPumpDao;

    @Autowired
    private AppBusinessMessageDao appBusinessMessageDao;

    /**
     * 保存牌局数据
     * <p>
     * 往数据表中写入数据
     *
     * @param roomPo
     * @param jpSettingPo
     * @param roomSearchPo
     * @return
     */
    @Override
    @Transactional
    public void createRoom(DzRoomPo roomPo, RoomJackpotSettionPo jpSettingPo, RoomSearchPo roomSearchPo) {

        if (roomPo.getTierId() == null) {
            // 判断牌局类型
            if (roomPo.getClubRoomType() == 1 && roomPo.getTribeRoomType() == 1) {
                // 保存前查询最大层级
                Integer tribeMaxTierId = appBusinessMessageDao.getTribeMaxTier();
                roomPo.setTierId(tribeMaxTierId);
            } else {
                // 获取默认的最小层级
                Integer defaultMinTie = appBusinessMessageDao.getDefaultMinTier();
                roomPo.setTierId(defaultMinTie);
            }
        }

        log.info("保存牌局:roomPo:{}", JsonUtil.toJson(roomPo, true));
        log.info("保存牌局:jpSettingPo:{}", JsonUtil.toJson(jpSettingPo, true));
        log.info("保存牌局:roomSearchPo:{}", JsonUtil.toJson(roomSearchPo, true));
        Integer clubId = roomPo.getClubId();
        Integer tribeId = 0;
        if (clubId != 0) {
            log.debug("执行findTribeIdByClubId....");
            tribeId = iRoomPumpDao.findTribeIdByClubId(clubId);
            roomPo.setTribeId(tribeId);
        }
        this.roomDao.insertSelective(roomPo);
        log.info("插入roomPo数据成功");
        if (null != jpSettingPo) {
            this.jpSettingDao.insertSelective(jpSettingPo);
            log.info("插入jpSettingDao数据成功");
        }
        if (roomPo.getRoomPath() == 21 || roomPo.getRoomPath() == 23) {
            roomSearchPo.setSbChip(roomSearchPo.getQianzhu());
        }

        roomSearchPo.setZhuatou(Integer.valueOf(roomPo.getStraddle()));
        log.info("执行roomSearchPo.....");
        this.roomSearchDao.insertSelective(roomSearchPo);

        if (tribeId != null && tribeId != 0) {
            log.info("执行roomSearchPo.....");
            iRoomPumpDao.insertClubRoomExtend(roomPo.getRoomId(), clubId, tribeId);
        }
    }

    /**
     * 保存牌局数据
     * <p>
     * 往数据表中写入数据
     *
     * @param roomPo
     * @param roomSearchPo
     * @return
     */
    @Override
    @Transactional
    public void createBpRoom(BpRoomPo roomPo, RoomSearchPo roomSearchPo) {
        this.bpRoomDao.insertSelective(roomPo);
        this.roomSearchDao.insertSelective(roomSearchPo);
    }

    /**
     * 保存牌局数据
     * <p>
     * 往数据表中写入数据
     *
     * @param roomPo
     * @param roomSearchPo
     * @return
     */
    @Override
    @Transactional
    public void createSngRoom(SngRoomPo roomPo, RoomSearchPo roomSearchPo) {
        this.sngRoomDao.insertSelective(roomPo);
        this.roomSearchDao.insertSelective(roomSearchPo);
    }

    @Override
    @Transactional
    public void createMttRoom(MttRoomPo roomPo, RoomSearchPo roomSearchPo) {
        this.mttRoomDao.insertSelective(roomPo);
        this.roomSearchDao.insertSelective(roomSearchPo);
    }
}
