package com.dzpk.crazypoker.room.repositories.mysql.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 禁言
 */
@Data
@ApiModel(value = "禁言")
public class TribeMemberBanChatPo {

    @ApiModelProperty(name = "id",
            position = 0,
            notes = "id")
    private Integer id;

    @ApiModelProperty(name = "联盟id",
            position = 1,
            notes = "联盟id")
    private Integer tribeId;

    @ApiModelProperty(name = "联盟随机id",
            position = 2,
            notes = "联盟随机id")
    private Integer tribeRandomId;

    @ApiModelProperty(name = "联盟随机id",
            position = 3,
            notes = "联盟随机id")
    private Integer userId;

    @ApiModelProperty(name = "联盟随机id",
            position = 4,
            notes = "联盟随机id")
    private String userRandomNum;

    @ApiModelProperty(name = "时长/小时",
            position = 5,
            notes = "时长/小时")
    private Integer duration;

    @ApiModelProperty(name = "开始时间",
            position = 6,
            notes = "开始时间")
    private Timestamp banStartTime;

    @ApiModelProperty(name = "结束时间",
            position = 7,
            notes = "结束时间")
    private Timestamp banEndTime;

}
