package com.dzpk.crazypoker.room.repositories.mysql;

import com.dzpk.crazypoker.room.repositories.mysql.model.GroupRoomPo;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface GroupRoomPoDao {

    @Select("SELECT id, room_id roomId, tribe_id tribeId, club_id clubId, tribe_room_type tribeRoomType " +
            "FROM crazy_poker.group_room " +
            "WHERE room_id = #{roomId}")
    GroupRoomPo getRoomById(Integer roomId);
}