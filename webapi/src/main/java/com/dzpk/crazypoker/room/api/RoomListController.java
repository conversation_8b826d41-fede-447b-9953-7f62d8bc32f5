package com.dzpk.crazypoker.room.api;

import com.dzpk.crazypoker.appmessage.bean.UserRoomTierBean;
import com.dzpk.crazypoker.appmessage.dao.AppBusinessMessageDao;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.club.service.bean.ClubRecordBo;
import com.dzpk.crazypoker.common.constant.EDirectionPage;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.web.controller.AbstractController;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import com.dzpk.crazypoker.room.api.bean.*;
import com.dzpk.crazypoker.room.api.req.MttRoomListReq;
import com.dzpk.crazypoker.room.api.req.RoomEnterReq;
import com.dzpk.crazypoker.room.api.req.RoomListReq;
import com.dzpk.crazypoker.room.cache.IRoomCache;
import com.dzpk.crazypoker.room.cache.RoomType;
import com.dzpk.crazypoker.room.constant.ERoomListMZ;
import com.dzpk.crazypoker.room.constant.ERoomPath;
import com.dzpk.crazypoker.room.service.IRoomService;
import com.dzpk.crazypoker.room.service.bean.MttRoomListItemBo;
import com.dzpk.crazypoker.room.service.bean.RoomListBo;
import com.dzpk.crazypoker.tribe.service.ITribeService;
import com.dzpk.crazypoker.tribe.service.bean.TribeStatusBo;
import com.dzpk.crazypoker.user.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping(
        method = RequestMethod.POST,
        consumes = "application/json;charset=UTF-8",
        produces = "application/json;charset=UTF-8")
@Api(tags = {"游戏大厅-牌局列表API"})
public class RoomListController extends AbstractController {

    @Autowired
    private IRoomService roomService;

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private IUserService userService;

    @Autowired
    private IClubService clubService;

    @Autowired
    private ITribeService tribeService;
    @Autowired
    private IRoomCache roomCache;

    // 业务配置,以后加入动态配置
    private int pageSize = 999;

    @ApiOperation(value = "牌局列表",
            notes = "根据查询条件，翻页查询各个类型的当前有效的牌局。<br/>" +
                    "排序规则：按牌局创建时间倒序，每页20条<br/>" +
                    "status 业务操作状态码<br/>" +
                    "  * 0    : 操作成功<br/>" +
                    "  * 101  ：参数必须指定！<br/>" +
                    "  * 102  ：参数长度超出允许范围！<br/>" +
                    "  * 103  ：参数值未满足最小值约束！<br/>" +
                    "  * 104  ：参数值无效！<br/>" +
                    "  * 199  ：未知参数错误！")
    @ResponseBody
    @RequestMapping("/room/list")
    public CommonResponse<List<RoomListVo>> queryByPage(@ApiIgnore @RequestAttribute("user_id") int userId,
                                                        @Valid @RequestBody RoomListReq req, BindingResult validedResult) {
        //常规校验
        handleValidatedError(validedResult);


        // 接口层数据转换
        List<ERoomPath> roomPathLst = ERoomPath.fromApi(req.getRoomPath());
        if (null != roomPathLst && roomPathLst.isEmpty())
            roomPathLst = null;

        ERoomListMZ manzhu = ERoomListMZ.of(req.getMangzhu());
        EDirectionPage direction = EDirectionPage.of(req.getDirection());
        if (req.getHasQianzhu() <= -1)
            req.setHasQianzhu(null);
        if (req.getHasInsurance() <= -1)
            req.setHasInsurance(null);
        if (req.getHasSeat() <= -1)
            req.setHasSeat(null);


        CommonResponse<List<RoomListVo>> response = new CommonResponse<>();
        /**
         * 显示条件
         * 1. 用户未被冻结
         * 2. 用户未加入俱乐部，满足，则直接检索，否则进入下一步检查
         * 2.1. 用户加入俱乐部,且状态正常（非关闭）
         * 2.2. 所属俱乐部已加入联盟
         *      联盟状态正常
         * 2.3. 所属俱乐部对应联盟的状态正常（非踢出和转移中）
         *
         * 满足以上条件,才进行数据检索
         */
        // true : 冻结，false ：未冻结
        boolean isUserFreeze = this.userService.isFreezeByUserId(userId);
        RespCode status = RespCode.SUCCEED;
        if (!isUserFreeze) { // 未冻结
            ClubRecordBo clubBo = this.clubService.getUserClub(userId);
            //未加入俱乐部，则可以查看牌局列表
            if (null != clubBo && clubBo.getClubStatus() != 0) { // 加入俱乐部 俱乐部状态关闭
                status = RespCode.ROOM_ENTER_CLUBCLOSED;
            }
        } else {
            status = RespCode.ROOM_ENTER_USERFREEZE;
        }
        if (status != RespCode.SUCCEED) {// 不符合条件
            response.setStatus(status.getCode());
            response.setMsg(status.getDesc());
            return response;
        }

        // 检索数据
        InvokedResult<List<RoomListBo>> invokedResult = this.roomService.listRoomBy(
                userId, roomPathLst, req.getClubRoomType(), manzhu, req.getMzlist(), direction);
        if (invokedResult.getCode() != 0) {
            response.setStatus(invokedResult.getCode());
            response.setMsg("0");
            return response;
        }
        List<RoomType> bringinRoomOfUser = roomCache.getBringinRoomOfUser(userId);
        Set<String> roomSettlement = roomCache.getRoomSettlement(userId);
        List<Integer> bringinRoomId = new ArrayList<>();
        if (bringinRoomOfUser != null) {
            for (RoomType type : bringinRoomOfUser) {
                bringinRoomId.add(type.getRoomId());
            }
        }
        List<Integer> settlementRoomId = new ArrayList<>();
        if (roomSettlement != null) {
            for (String roomId : roomSettlement) {
                settlementRoomId.add(Integer.valueOf(roomId));
            }
        }
        LinkedList<RoomListBo> room = new LinkedList<>();
        for (RoomListBo roomListBo : invokedResult.getData()) {
            if (bringinRoomId.contains(roomListBo.getRoomId())) {
                room.addFirst(roomListBo);
            } else {
                room.addLast(roomListBo);
            }
        }
        LinkedList<RoomListBo> room1 = new LinkedList<>();
        for (RoomListBo roomListBo : room) {
            if (settlementRoomId.contains(roomListBo.getRoomId())) {
                roomListBo.setStatus(6);
                room1.addLast(roomListBo);
            } else {
                room1.addFirst(roomListBo);
            }
        }
        //上拉刷新
        if (req.getListSize() == null || direction.desc().equals(EDirectionPage.first.desc()))
            req.setListSize(0);

        int maxLen = req.getListSize() + this.pageSize;
        List<RoomListBo> roomListBos = room1.subList(req.getListSize(), Math.min(maxLen, room1.size()));
        response.setData(this.convert(roomListBos));
        response.setMsg(String.valueOf(roomListBos.size() + req.getListSize()));

        return response;
    }

    @ApiOperation(value = "盲注列表",
            notes = "返回所有盲注对应的抽水和封顶。")
    @ResponseBody
    @RequestMapping("/room/pumplist")
    public CommonResponse<List<PumpVo>> findPump() {
        CommonResponse<List<PumpVo>> response = new CommonResponse<>();
        List<PumpVo> allPump = roomService.findAllPump();
        Collections.sort(allPump);
        response.setData(allPump);
        return response;
    }


    @ApiOperation(value = "我的牌局列表",
            notes = "返回所有玩家带入过的牌局。")
    @ResponseBody
    @RequestMapping("/room/mylist")
    public CommonResponse<List<RoomListVo>> queryMyRoom(@RequestAttribute("user_id") int userId) {
        CommonResponse<List<RoomListVo>> response = new CommonResponse<>();

        /**
         * 显示条件
         * 1. 用户未被冻结
         * 2. 用户加入俱乐部，满足则检索数据,否则进入下一步检查
         * 2.1. 所属俱乐部已加入联盟
         * 2.2. 所属俱乐部对应联盟的状态正常（非踢出和转移中）
         *
         * 满足以上条件,才进行数据检索
         */
        boolean isUserFreeze = this.userService.isFreezeByUserId(userId);
        boolean continueChk = !isUserFreeze; // 未冻结
        if (continueChk) {
            ClubRecordBo clubBo = this.clubService.getUserClub(userId);
            if (null == clubBo) {
                continueChk = false; //未加入俱乐部，则不存在带入过的牌局列表
            } else { // 加入俱乐部
                TribeStatusBo tribeBo = this.tribeService.findTribeStatusByClubId(clubBo.getId());
                if (null == tribeBo)
                    continueChk = false; // 所属俱乐部未加入联盟
                else
                    continueChk = tribeBo.getStatus() == 1; // 所属俱乐部非【转移中或被联盟踢出】,则可以查看带入的列表
            }
        }
        if (!continueChk) {// 不符合条件
            return response;
        }

        List<RoomListBo> dataLst = this.roomService.listMyRoomBy(userId);
        response.setData(this.convert(dataLst));

        return response;
    }

    @ApiOperation(value = "MTT比赛列表",
            notes = "mtt比赛列表")
    @ResponseBody
    @RequestMapping("/room/mtt/list")
    public CommonResponse<MttRoomListVo> queryMttRoom(@RequestAttribute("user_id") int userId,
                                                      @RequestAttribute("user_ip") String userIp,
                                                      @Valid @RequestBody MttRoomListReq req,
                                                      HttpServletRequest httpReq,
                                                      BindingResult validedResult) {
        handleValidatedError(validedResult);

        CommonResponse<MttRoomListVo> response = new CommonResponse<>();

        /**
         * 显示条件
         * 1. 用户未被冻结
         * 2. 用户加入俱乐部，满足则检索数据,否则进入下一步检查
         * 2.1. 所属俱乐部已加入联盟
         * 2.2. 所属俱乐部对应联盟的状态正常（非踢出和转移中）
         *
         * 满足以上条件,才进行数据检索
         */
        boolean isUserFreeze = this.userService.isFreezeByUserId(userId);
        boolean continueChk = !isUserFreeze; // 未冻结
        if (continueChk) {
            ClubRecordBo clubBo = this.clubService.getUserClub(userId);
            if (null == clubBo) {
                continueChk = false; //未加入俱乐部，则不存在带入过的牌局列表
            } else { // 加入俱乐部
                TribeStatusBo tribeBo = this.tribeService.findTribeStatusByClubId(clubBo.getId());
                if (null == tribeBo)
                    continueChk = false; // 所属俱乐部未加入联盟
                else
                    continueChk = tribeBo.getStatus() == 1; // 所属俱乐部非【转移中或被联盟踢出】,则可以查看带入的列表
            }
        }
        if (!continueChk) {// 不符合条件
            return response;
        }

        MttRoomListVo data = new MttRoomListVo();
        List<MttRoomListItemBo> dataLst = this.roomService.listMttRoomBy(userId, req.getMatchType(), req.getPageNum(), req.getPageSize());
        List<MttRoomListItemVo> resultList = new ArrayList<>();
        for (MttRoomListItemBo bo : dataLst) {
            resultList.add(this.beanUtil.map(bo, MttRoomListItemVo.class));
        }

        data.setClientIp(userIp);
        data.setRoomList(resultList);

        response.setData(data);

        return response;
    }

    @ApiOperation(value = "俱乐部牌局列表",
            notes = "根据查询条件，翻页查询各个类型的当前有效的牌局。<br/>" +
                    "排序规则：按牌局创建时间倒序，每页20条<br/>" +
                    "status 业务操作状态码<br/>" +
                    "  * 0    : 操作成功<br/>" +
                    "  * 101  ：参数必须指定！<br/>" +
                    "  * 102  ：参数长度超出允许范围！<br/>" +
                    "  * 103  ：参数值未满足最小值约束！<br/>" +
                    "  * 104  ：参数值无效！<br/>" +
                    "  * 199  ：未知参数错误！")
    @ResponseBody
    @RequestMapping("club/room/list")
    public CommonResponse<List<RoomListVo>> queryClubRoomByPage(@ApiIgnore @RequestAttribute("user_id") int userId,
                                                                @Valid @RequestBody RoomListReq req, BindingResult validedResult) {
        //常规校验
        handleValidatedError(validedResult);
        // todo 处理俱乐部数据
        log.info("俱乐部牌局列表.......,userId:{},clubId:{}", userId, req.getClubId());
        // 接口层数据转换
        List<ERoomPath> roomPathLst = ERoomPath.fromApi(req.getRoomPath());
        if (null != roomPathLst && roomPathLst.isEmpty()) {
            roomPathLst = null;
        }
        EDirectionPage direction = EDirectionPage.of(req.getDirection());
        CommonResponse<List<RoomListVo>> response = new CommonResponse<>();
        /**
         * 显示条件
         * 1. 用户未被冻结
         * 2. 用户未加入俱乐部，满足，则直接检索，否则进入下一步检查
         * 2.1. 用户加入俱乐部,且状态正常（非关闭）
         * 2.2. 所属俱乐部已加入联盟
         *      联盟状态正常
         * 2.3. 所属俱乐部对应联盟的状态正常（非踢出和转移中）
         *
         * 满足以上条件,才进行数据检索
         */
        // true : 冻结，false ：未冻结
        boolean isUserFreeze = this.userService.isFreezeByUserId(userId);
        // 未冻结
        boolean continueChk = !isUserFreeze;
        int clubId = 0;
        int tribeId = 0;
        if (continueChk) {
            long checkInClub = this.clubService.checkUserInClub(userId, req.getClubId());
            if (checkInClub <= 0) {
                response.setStatus(RespCode.ROOM_ENTER_USERNOCLUB.getCode());
                response.setMsg(RespCode.ROOM_ENTER_USERNOCLUB.getDesc());
                return response;
            }
            ClubRecordBo clubBo = this.clubService.getClub(req.getClubId());
            if (clubBo == null) {
                log.info("当前用户没有查询到俱乐部相关数据,userId:{}", userId);
                //未加入俱乐部，则可以查看牌局列表
            } else {
                // 根据传递过来的俱乐部id选择一个
                log.info("查询到俱乐部数据:{},", clubBo.getId());
                // 加入俱乐部
                continueChk = clubBo.getClubStatus() == 0;
                // 俱乐部状态正常（非关闭）
                clubId = clubBo.getId();
                //俱乐部就可以看到牌局
                if (continueChk) {
                    TribeStatusBo tribeBo = this.tribeService.findTribeStatusByClubId(clubBo.getId());
                    // 所属俱乐部已加入联盟
                    if (tribeBo != null) {
                        tribeId = tribeBo.getTribeId();
                        // 1. 联盟状态正常(未关闭)
                        // 2. 所属俱乐部是否转移中或被联盟踢出
                        continueChk = tribeBo.getTribeStatus() == 0 && tribeBo.getStatus() == 1;
                    }
                }
            }
        }
        if (!continueChk) {// 不符合条件
            response.setMsg("0");
            return response;
        }

        log.info("俱乐部Id{},tribeId:{}", clubId, tribeId);
        // 检索数据
        InvokedResult<List<RoomListBo>> invokedResult = this.roomService.listClubRoomBy(userId, tribeId, clubId);
        if (invokedResult.getCode() != 0) {
            response.setStatus(invokedResult.getCode());
            response.setMsg("0");
            return response;
        }
        /*
        List<RoomType> bringinRoomOfUser = roomCache.getBringinRoomOfUser(userId);
        Set<String> roomSettlement = roomCache.getRoomSettlement(userId);
        List<Integer> bringinRoomId = new ArrayList<>();
        if (bringinRoomOfUser != null) {
            for (RoomType type : bringinRoomOfUser) {
                bringinRoomId.add(type.getRoomId());
            }
        }
        List<Integer> settlementRoomId = new ArrayList<>();
        if (roomSettlement != null) {
            for (String roomId : roomSettlement) {
                settlementRoomId.add(Integer.valueOf(roomId));
            }
        }
        LinkedList<RoomListBo> room = new LinkedList<>();
        for (RoomListBo roomListBo : invokedResult.getData()) {
            if (bringinRoomId.contains(roomListBo.getRoomId())) {
                room.addFirst(roomListBo);
            } else {
                room.addLast(roomListBo);
            }
        }
        LinkedList<RoomListBo> room1 = new LinkedList<>();
        for (RoomListBo roomListBo : room) {
            if (settlementRoomId.contains(roomListBo.getRoomId())) {
                roomListBo.setStatus(6);
                room1.addLast(roomListBo);
            } else {
                room1.addFirst(roomListBo);
            }
        }
         */
        //上拉刷新
        if (req.getListSize() == null || direction.desc().equals(EDirectionPage.first.desc()))
            req.setListSize(0);

//        int maxLen = req.getListSize() + this.pageSize;
//        List<RoomListBo> roomListBos = room1.subList(req.getListSize(), Math.min(maxLen, room1.size()));

        // 重新排序
        List<RoomListBo> roomListBos = reorder(invokedResult.getData(), userId);

        // 过滤层级房间
        List<RoomListBo> rooms = filterTierRooms(roomListBos, userId, clubId);

        response.setData(this.convert(rooms));
        response.setMsg(String.valueOf(rooms.size() + req.getListSize()));

        return response;
    }

    @Resource
    AppBusinessMessageDao appBusinessMessageDao;

    /**
     * 过滤分层房间
     * @param roomList
     * @param userId
     * @param clubId
     * @return
     */
    private List<RoomListBo> filterTierRooms(List<RoomListBo> roomList, int userId, int clubId) {
        Integer tribeId = appBusinessMessageDao.getTribeIdByClubId(clubId);
        List<UserRoomTierBean> userRoomTiers = appBusinessMessageDao.findUserRoomTier(userId, tribeId);
        // 按照联盟ID分组
        Map<Integer, UserRoomTierBean> userRoomTierMap = userRoomTiers.stream()
                .collect(Collectors.toMap(UserRoomTierBean::getTribeId, Function.identity(), (existing, replacement) -> existing));
        List<RoomListBo> filteredRooms = new ArrayList<>();
        for (RoomListBo room : roomList) {
            int creator = room.getCreator();
            if (creator == userId) {
                // 如果是自己创建的房间，直接添加
                filteredRooms.add(room);
                continue;
            }
            // 获取房间类型
            Integer tribeRoom = appBusinessMessageDao.isTribeRoom(room.getRoomId());
            if (tribeRoom == 1) {
                // 如果是联盟房间，检查用户是否有权限
                Integer roomTierValue = appBusinessMessageDao.getRoomTierValue(room.getRoomId());
                UserRoomTierBean userRoomTierBean = userRoomTierMap.get(room.getTribeId());
                if (userRoomTierBean != null) {
                    Integer userValue = userRoomTierBean.getValue();
                    // 如果用户值小于等于房间值则添加到列表
                    if (userValue != null && userValue <= roomTierValue) {
                        filteredRooms.add(room);
                    }
                }
            } else {
                // 如果不是联盟房间，直接添加
                filteredRooms.add(room);
            }
        }
        return filteredRooms;
    }

    /**
     * 重新排序
     * @param roomListBos
     * @return
     */
    private List<RoomListBo> reorder(List<RoomListBo> roomListBos, int userId) {
        // 先按照创建时间倒叙 private int createdTime;
        roomListBos.sort((a, b) -> Integer.compare(b.getCreatedTime(), a.getCreatedTime()));

        // 再将玩家带入的牌局放在最前面
        List<RoomType> bringInRoomOfUser = roomCache.getBringinRoomOfUser(userId);
        List<Integer> bringInRoomId = new ArrayList<>();
        if (bringInRoomOfUser != null) {
            for (RoomType type : bringInRoomOfUser) {
                bringInRoomId.add(type.getRoomId());
            }
        }

        // 将玩家带入的牌局放在最前面
        List<RoomListBo> bringInRooms = new ArrayList<>();
        List<RoomListBo> otherRooms = new ArrayList<>();
        for (RoomListBo room : roomListBos) {
            if (bringInRoomId.contains(room.getRoomId())) {
                room.setIsPlayed(true);
                bringInRooms.add(room);
            } else {
                room.setIsPlayed(false);
                otherRooms.add(room);
            }
        }

        // 合并结果
        bringInRooms.addAll(otherRooms);
        roomListBos = bringInRooms;

        // 最后将进行中的牌局放在最前面private int status;3：等待中、4：进行中、5：游戏中（带入）、6：已结算
//        roomListBos.sort((a, b) -> {
//            if ((a.getStatus() == 4 || a.getStatus() == 5) && (b.getStatus() != 4 && b.getStatus() != 5)) {
//                return -1;
//            } else if ((b.getStatus() == 4 || b.getStatus() == 5) && (a.getStatus() != 4 && a.getStatus() != 5)) {
//                return 1;
//            } else {
//                return 0;
//            }
//        });


        return roomListBos;
    }

    @ApiOperation(value = "输入房间号进入牌局",
            notes = "玩家进入牌局前，通过此接口获取牌局的基础数据及所在游戏服务的访问地址。<br/>" +
                    "status 业务操作状态码<br/>" +
                    "  * 0    : 操作成功<br/>" +
                    "  * 101  ：参数必须指定！<br/>" +
                    "  * 102  ：参数长度超出允许范围！<br/>" +
                    "  * 103  ：参数值未满足最小值约束！<br/>" +
                    "  * 104  ：参数值无效！<br/>" +
                    "  * 199  ：未知参数错误！<br/>" +
                    "  * 2001 : 牌局不存在或已解散 <br/>" +
                    "  * 2004 : 账号已被冻结<br/>" +
                    "  * 2005 : 玩家未加入俱乐部<br/>" +
                    "  * 2006 : 玩家所属俱乐部已被关闭<br/>" +
                    "  * 2007 : 玩家所属俱乐部未绑定联盟<br/>" +
                    "  * 2008 : 玩家所属俱乐部转移中或已被踢出<br/>"
    )
    @ResponseBody
    @RequestMapping("/room/enter")
    public CommonResponse<DzRoomVo> enterRoom(
            @ApiIgnore @RequestAttribute("user_id") int userId,
            @ApiIgnore @RequestAttribute("user_ip") String userIp,
            @Valid @RequestBody RoomEnterReq req,
            HttpServletRequest httpReq,
            BindingResult validedResult
    ) {
        CommonResponse<DzRoomVo> response = new CommonResponse<>();
        Integer roomPath1 = this.roomService.getRoomPath(req.getRoomId());
        //常规校验
        handleValidatedError(validedResult);
        if (null == roomPath1) {
            response.setStatus(RespCode.ROOM_UNKNOWN.getCode());
            response.setMsg("没有牌局!");
            return response;
        }

        ERoomPath roomPath = null;
        switch (roomPath1) {
            case 21:
                roomPath = ERoomPath.shortCard;
                break;
            case 23:
                roomPath = ERoomPath.shortCardAof;
                break;
            case 61:
                roomPath = ERoomPath.dzpk;
                break;
            case 63:
                roomPath = ERoomPath.dzpkAof;
                break;
            case 91:
                roomPath = ERoomPath.omaha;
                break;
            case 93:
                roomPath = ERoomPath.omahaAof;
                break;
        }

        InvokedResult<Object> invokedResult = this.roomService.enterRoomById(userId, req.getRoomId(), roomPath);
        if (invokedResult.getCode() != 0) {
            response.setStatus(invokedResult.getCode());
            response.setMsg(invokedResult.getMsg());
            return response;
        }
        DzRoomVo roomData = this.beanUtil.map(invokedResult.getData(), DzRoomVo.class);
        roomData.setRoomPath(roomData.getRoomPath());
        roomData.setRoomTab(ERoomPath.toApi(roomData.getRoomPath()));
        roomData.setClientIp(userIp);
        roomData.setIsRoomOwner(roomData.getRoomOwnerId() == userId ? 1 : 0);

        response.setData(roomData);
        return response;
    }

    private List<RoomListVo> convert(List<RoomListBo> list) {
        if (null == list || list.isEmpty())
            return null;

        List<RoomListVo> resultLst = new ArrayList<>();
        for (RoomListBo bo : list) {
            RoomListVo vo = beanUtil.map(bo, RoomListVo.class);
            if (null != vo) {
                vo.setRoomPath(bo.getRoomPath().value());
                vo.setRoomTab(ERoomPath.toApi(bo.getRoomPath()));
                vo.setMaxGameTimeSec(bo.getGameMaxTime() * 60 + bo.getDelaySec());
            }

            resultLst.add(vo);
        }

        return resultLst;
    }
}
