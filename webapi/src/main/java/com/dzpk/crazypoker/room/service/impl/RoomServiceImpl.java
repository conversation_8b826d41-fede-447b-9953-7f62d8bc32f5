package com.dzpk.crazypoker.room.service.impl;

import com.dzpk.crazypoker.club.constant.EClubIdentityCode;
import com.dzpk.crazypoker.club.repositories.mysql.IClubDao;
import com.dzpk.crazypoker.club.repositories.mysql.model.ClubJoinedPo;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.club.service.bean.ClubRecordBo;
import com.dzpk.crazypoker.common.constant.EDirectionPage;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.rabbitmq.client.MessageSender;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.InteriorMessage;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageCode;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.utils.DateUtils;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.jackpot.constant.EJackpotType;
import com.dzpk.crazypoker.jackpot.service.IJackpotService;
import com.dzpk.crazypoker.jackpot.service.bean.JackpotPoolSettingBo;
import com.dzpk.crazypoker.room.api.bean.PumpVo;
import com.dzpk.crazypoker.room.api.bean.RoomCreationPlanVo;
import com.dzpk.crazypoker.room.cache.IMttCache;
import com.dzpk.crazypoker.room.cache.IRoomCache;
import com.dzpk.crazypoker.room.cache.RoomType;
import com.dzpk.crazypoker.room.constant.Constant;
import com.dzpk.crazypoker.room.constant.ERoomListMZ;
import com.dzpk.crazypoker.room.constant.ERoomPath;
import com.dzpk.crazypoker.room.repositories.mysql.IRoomCreationPlanDao;
import com.dzpk.crazypoker.room.repositories.mysql.IRoomPumpDao;
import com.dzpk.crazypoker.room.repositories.mysql.IRoomSearchDao;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.mapper.*;
import com.dzpk.crazypoker.room.repositories.mysql.autogen.model.*;
import com.dzpk.crazypoker.room.repositories.mysql.model.MttRoomSearchDo;
import com.dzpk.crazypoker.room.repositories.mysql.model.RoomSearchRemainTimeDo;
import com.dzpk.crazypoker.room.server.*;
import com.dzpk.crazypoker.room.service.IRoomIdGeneratorService;
import com.dzpk.crazypoker.room.service.IRoomService;
import com.dzpk.crazypoker.room.service.bean.*;
import com.dzpk.crazypoker.room.service.transaction.IRoomTransation;
import com.dzpk.crazypoker.system.repositories.mysql.autogen.IMaintainMessageDao;
import com.dzpk.crazypoker.tribe.repositories.mysql.ITribeDao;
import com.dzpk.crazypoker.tribe.service.ITribeService;
import com.dzpk.crazypoker.user.service.IUserService;
import com.dzpk.crazypoker.user.service.bo.UserDetailsInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 服务类
 * 对接口层屏蔽具体的业务逻辑
 * 及业务相关的存储结构
 */
@Slf4j
@Service
public class RoomServiceImpl implements IRoomService {
    // 业务配置,以后加入动态配置
    @Value("${room.logo.url}")
    private String logoUrl = "";
    @Value("${room.list.remainSec}")
    private int roomRemianSec = 30;

    @Autowired
    private IRoomPumpDao iRoomPumpDao;
    @Autowired
    private IRoomCache roomCache;


    @Autowired
    private IMttCache mttCache;

    @Autowired
    private IRoomIdGeneratorService roomIdGeneratorService;

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private IJackpotService jackpotService;

    @Autowired
    private IRoomTransation roomTransation;

    @Autowired
    private IRoomSearchDao roomSearchDao;

    @Autowired
    private DzRoomPoMapper roomDao;

    @Autowired
    private SngRoomPoMapper sngRoomDao;

    @Autowired
    private MttRoomPoMapper mttRoomDao;

    @Autowired
    private BpRoomPoMapper bpRoomDao;

    @Autowired
    private IMaintainMessageDao maintainMessageDao;

    @Autowired
    private IRoomCreationPlanDao roomCreationPlanDao;

    @Autowired
    private RoomJackpotSettionPoMapper jpSettingDao;

    @Autowired
    private IRoomNodeManager roomNodeManager;

    @Autowired
    private IAofRoomNodeManager aofRoomNodeManager;

    @Autowired
    private IAofShortCardNodeManager aofShortCardNodeManager;

    @Autowired
    private IShortCardNodeManager shortCardNodeManager;

    @Autowired
    private IOmahaNodeManager omahaNodeManager;

    @Autowired
    private IAofOmahaNodeManager aofOmahaNodeManager;

    @Autowired
    private IMttNodeManager mttNodeManager;

    @Autowired
    private IUserService userService;

    @Autowired
    private IClubService clubService;

    @Autowired
    private ITribeService tribeService;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private IClubDao clubDao;

    @Autowired
    private ITribeDao tribeDao;

    private static int autoRoomPlanCountLimit = 24;

    @Value("${mttServer.ip}")
    private String mttServerIp;

    @Value("${mttServer.port}")
    private int mttServerPort;

    /**
     * 创建普通&必下场&AOF&组合局
     * <p>
     * 逻辑：
     * 1. 申请牌局服务（IP/port）, 由分配服务的服务负责维护自身的结构
     * 2. 保存牌局数据
     * 同时写入三个表：room_search / 对应的牌局表 / 牌局jackpot配置表
     * 状态初始为3
     * 弃用的字段都设置默认值
     *
     * @param opUserId       当前操作用户ID，必填
     * @param roomData       创建牌局所需参数，必填
     * @param roomPath       标识类型（普通局 / AOF / 必下场），可选，默认：普通局
     * @param isAutoRoomPlan 是否自动开房计划建房。 0-否，1-是
     * @param autoRoomPlanId 自动建房计划id。当isAutoRoomPlan为1时有值
     * @return code    操作状态
     * 301    jackpot配置数据缺失
     * -666    参数设置错误
     */
    @Override
    @Transactional
    public InvokedResult<DzRoomBo> createDzRoom(int opUserId, ERoomPath roomPath, DzCreationBo roomData, int isAutoRoomPlan, Integer autoRoomPlanId) {
        return getDzRoomBoInvokedResult(opUserId, roomPath, roomData, isAutoRoomPlan, autoRoomPlanId, true);
    }

    @Override
    @Transactional
    public InvokedResult<DzRoomBo> createDzRoom2(int opUserId, ERoomPath roomPath, DzCreationBo roomData, int isAutoRoomPlan, Integer autoRoomPlanId) {
        return getDzRoomBoInvokedResult(opUserId, roomPath, roomData, isAutoRoomPlan, autoRoomPlanId, false);
    }

    /**
     * 创建普通&必下场&AOF&组合局
     * <p>
     * 逻辑：
     * 1. 申请牌局服务（IP/port）, 由分配服务的服务负责维护自身的结构
     * 2. 保存牌局数据
     * 同时写入三个表：room_search / 对应的牌局表 / 牌局jackpot配置表
     * 状态初始为3
     * 弃用的字段都设置默认值
     *
     * @param opUserId       当前操作用户ID，必填
     * @param roomData       创建牌局所需参数，必填
     * @param roomPath       标识类型（普通局 / AOF / 必下场），可选，默认：普通局
     * @param isAutoRoomPlan 是否自动开房计划建房。 0-否，1-是
     * @param autoRoomPlanId 自动建房计划id。当isAutoRoomPlan为1时有值
     * @return code    操作状态
     * 301    jackpot配置数据缺失
     * -666    参数设置错误
     */
    private InvokedResult<DzRoomBo> getDzRoomBoInvokedResult(int opUserId, ERoomPath roomPath, DzCreationBo roomData, int isAutoRoomPlan, Integer autoRoomPlanId, boolean checkClub) {
        InvokedResult<DzRoomBo> result = new InvokedResult<>();
        // 参数校验
        if (roomPath != null &&
                roomPath != ERoomPath.dzpk &&
                roomPath != ERoomPath.dzpkAof &&
                roomPath != ERoomPath.dzpkThanoff) {
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("RoomPath设置错误!");
            return result;
        }
        if (checkClub) {
            // 检查用户是否有权限进入牌局
            InvokedResult checkPerm = this.canEnterRoom(opUserId);
            if (checkPerm.getCode() != 0) {
                result.setCode(checkPerm.getCode());
                result.setMsg(checkPerm.getMsg());
                return result;
            }
        }
        log.info("检查用户是否有权限进入牌局");
        //檢查房間名稱是否重複
        if(isAutoRoomPlan == 0 && roomData.getName() != null && checkExistRoomName(roomData.getName(), roomData.getClubId())){
            result.setCode(RespCode.ROOM_NAME_EXIST.getCode());
            result.setMsg(RespCode.ROOM_NAME_EXIST.getDesc());
            return result;
        }
        // 非自动开房计划，牌局名称不能以ZD开头
        if (isAutoRoomPlan == 0 && roomData.getName() != null && roomData.getName().startsWith("ZD")) {
            result.setCode(RespCode.ROOM_NAME_ERROR.getCode());
            result.setMsg(RespCode.ROOM_NAME_ERROR.getDesc());
            return result;
        }
        log.info("非自动开房计划，牌局名称不能以ZD开头");
        if (isAutoRoomPlan == 1) {
            roomData.setName(generateAutoRoomName(roomPath, roomData.getName(), autoRoomPlanId));
        }

        if (null == roomPath)
            roomPath = ERoomPath.dzpk;
        Date createdTime = new Date();
        log.info("加载jackpot数据 开始");
        // 加载jackpot数据
        RoomJackpotSettionPo jpSettingPo = null;
        BigDecimal fund = null;
        if (roomData.getClubRoomType() == 0 && roomData.getJackpotOn() == 1) {
            JackpotPoolSettingBo jpSettingBo = jackpotService.getJackpotSettingByJackpotType(roomPath == ERoomPath.dzpkAof ? EJackpotType.AOF : EJackpotType.DZPK, roomData.getSmallMz());
            if (null == jpSettingBo) {
                result.setCode(RespCode.ROOM_JACKPOT_SETTING_NOTFOUND.getCode());
                result.setMsg(RespCode.ROOM_JACKPOT_SETTING_NOTFOUND.getDesc());
                return result;
            }

            // roomId/roomPath 需要手工设置
            jpSettingPo = this.beanUtil.map(jpSettingBo, RoomJackpotSettionPo.class);
            fund = jackpotService.getSystemJackpotPoolData(roomPath == ERoomPath.dzpk ? 1 : 3, roomData.getSmallMz());

        }

        log.info("加载jackpot数据 结束");
        // 组装牌局表数据
        DzRoomPo roomPo = this.beanUtil.map(roomData, DzRoomPo.class);
        if (roomData.getClubRoomType() == 0) {
            PumpPo pumpPo = roomCreationPlanDao.seletRoomPump(
                    roomData.getSmallMz() + "/" + roomData.getSmallMz() * 2,
                    roomPath.value() == 63 ? 31 : roomPath.value());
            roomPo.setMaxFeeChip(pumpPo.getMaxbet());
            roomPo.setServiceCharge(pumpPo.getPump() * 100);
            // roomPo.setMaxFeeChip(0);
            // roomPo.setServiceCharge(0);
        } else {
            // 俱乐部牌局服務費無上限
            roomPo.setMaxFeeChip(0);
            roomPo.setServiceCharge(roomData.getFee());
        }
        log.info("组装牌局表数据 结束");
//        roomPo.setLogoUrl(this.logoUrl);
        // 替换为俱乐部牌局的Logo
        roomPo.setLogoUrl(roomData.getLogoUrl());
        roomPo.setStatus(3);
        roomPo.setCreator(opUserId);
        roomPo.setCreateTime(Long.valueOf(createdTime.getTime() / 1000).intValue());
        roomPo.setUpdateTime(createdTime);
        roomPo.setCharge(0);

        roomPo.setRoomPath(roomPath.value());

        // roomPath
        // roomType
        RoomSearchPo roomSearchPo = this.beanUtil.map(roomPo, RoomSearchPo.class);
        roomSearchPo.setRoomPath(roomPath.value());
        roomSearchPo.setEmptySeat(roomSearchPo.getPlayerCount());
        roomSearchPo.setLableName(roomData.getLableName());

        // 生成牌局ID
        int roomId = this.genRandomRoomId();
        if (null != jpSettingPo) {
            jpSettingPo.setRoomId(roomId);
            jpSettingPo.setRoomPath(roomPath.value());
        }
        roomPo.setRoomId(roomId);
        roomSearchPo.setRoomId(roomId);
        log.info("保存数据 调用分服服务，获取牌局服务的IP/PORT 开始");
        // 保存数据 调用分服服务，获取牌局服务的IP/PORT
        ServerNodeWapper serverNode;
        if (roomPath == ERoomPath.dzpkAof)
            serverNode = this.aofRoomNodeManager.assign(roomId);
        else
            serverNode = this.roomNodeManager.assign(roomId);
        if (null == serverNode) {
            result.setCode(RespCode.ROOM_SERVER_NOTONLINE.getCode());
            result.setMsg(RespCode.ROOM_SERVER_NOTONLINE.getDesc());
            return result;
        }
        String ip = serverNode.getAccessIp();
        String host = serverNode.getAccessHost();
        int port = serverNode.getAccessPort();
        roomPo.setAccessIp(ip);
        roomPo.setAccessPort(port);
        roomPo.setServerId(serverNode.getServerId());
        roomSearchPo.setAccessIp(ip);
        roomSearchPo.setAccessPort(port);
        roomSearchPo.setServerId(roomPo.getServerId());
        log.info("保存数据 调用分服服务，获取牌局服务的IP/PORT 结束");
        this.roomTransation.createRoom(roomPo, jpSettingPo, roomSearchPo);
        log.info("创建牌局数据结束");

        // 组装数据返回
        DzRoomBo roomBo = this.beanUtil.map(roomPo, DzRoomBo.class);
        roomBo.setCreatedTime(roomPo.getCreateTime());
        roomBo.setRoomPath(roomPath);
        roomBo.setGameIp(ip);
        roomBo.setGameHost(host);
        roomBo.setGamePort(port);
        roomBo.setJackpotId(jpSettingPo == null ? null : jpSettingPo.getJackpotId());
        roomBo.setJackpotFund(null == fund ? 0 : fund.intValue());
        log.info("保存自动开房计划和房间关系开始");
        // 保存自动开房计划和房间关系
        if (isAutoRoomPlan == 1) {
            roomCreationPlanDao.savePlanRoomRelation(roomId, autoRoomPlanId, roomBo.getName(), new Date());
            // 设置计划空位数
            roomCreationPlanDao.resetEmptySeats(autoRoomPlanId);
        }
        log.info("设置消息数据：interiorMessage");
        InteriorMessage interiorMessage = InteriorMessage.builder().param1(String.valueOf(roomId)).param2(String.valueOf(opUserId)).type(EMessageCode.INTERIOR_CREATE_IM_GROUP.getCode()).build();
        log.info("发送消息数据：{}", JsonUtil.toJson(interiorMessage, true));
        messageSender.sendInteriorMessage(interiorMessage);
        result.setData(roomBo);
        return result;
    }

    @Override
    public InvokedResult<ShortCardBo> createShortCardRoom(int opUserId, ERoomPath roomPath, ShortCardCreationBo roomData, int isAutoRoomPlan, Integer autoRoomPlanId) {
        InvokedResult<ShortCardBo> result = new InvokedResult<>();
        // 参数校验
        if (roomPath != null && roomPath != ERoomPath.shortCard &&
                roomPath != ERoomPath.shortCardAof) {
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("RoomPath设置错误!");
            return result;
        }

        // 检查用户是否有权限进入牌局
        InvokedResult checkPerm = this.canEnterRoom(opUserId);
        if (checkPerm.getCode() != 0) {
            result.setCode(checkPerm.getCode());
            result.setMsg(checkPerm.getMsg());
            return result;
        }

        // 非自动开房计划，牌局名称不能以ZD开头
        if (isAutoRoomPlan == 0 && roomData.getName() != null && roomData.getName().startsWith("ZD")) {
            result.setCode(RespCode.ROOM_NAME_ERROR.getCode());
            result.setMsg(RespCode.ROOM_NAME_ERROR.getDesc());
            return result;
        }

        if (isAutoRoomPlan == 1) {
            roomData.setName(generateAutoRoomName(roomPath, roomData.getName(), autoRoomPlanId));
        }

        if (null == roomPath)
            roomPath = ERoomPath.shortCard;
        Date createdTime = new Date();

        // 加载jackpot数据
        RoomJackpotSettionPo jpSettingPo = null;
        BigDecimal fund = null;
        if (roomData.getClubRoomType() == 0 && roomData.getJackpotOn() == 1) {
            JackpotPoolSettingBo jpSettingBo = jackpotService.getJackpotSettingByJackpotType(roomPath == ERoomPath.shortCard ?
                    EJackpotType.SHORTCARD : EJackpotType.AOF, roomData.getQianzhu());
            if (null == jpSettingBo) {
                result.setCode(RespCode.ROOM_JACKPOT_SETTING_NOTFOUND.getCode());
                result.setMsg(RespCode.ROOM_JACKPOT_SETTING_NOTFOUND.getDesc());
                return result;
            }

            // roomId/roomPath 需要手工设置
            jpSettingPo = this.beanUtil.map(jpSettingBo, RoomJackpotSettionPo.class);
            fund = jackpotService.getSystemJackpotPoolData(roomPath == ERoomPath.shortCard ?
                    EJackpotType.SHORTCARD.value() : EJackpotType.AOF.value(), roomData.getQianzhu());

        }

        // 组装牌局表数据
        //roomId & status=3 & creator=userId & createTime& updateTime
        // charge=0
        DzRoomPo roomPo = this.beanUtil.map(roomData, DzRoomPo.class);
        if (roomData.getClubRoomType() == 0) {
            String bindnote;
            if (roomPath == ERoomPath.shortCardAof) {
                bindnote = roomData.getQianzhu() + "/" + roomData.getQianzhu() * 2;
            } else {
                bindnote = String.valueOf(roomData.getQianzhu());
            }
            PumpPo pumpPo = roomCreationPlanDao.seletRoomPump(
                    bindnote,
                    roomPath == ERoomPath.shortCardAof ? 31 : roomPath.value());
            roomPo.setMaxFeeChip(pumpPo.getMaxbet());
            roomPo.setServiceCharge(pumpPo.getPump() * 100);

        } else {
            roomPo.setMaxFeeChip((roomData.getQianzhu() * 2) * 3);
            roomPo.setServiceCharge(roomData.getFee());
        }
        roomPo.setLogoUrl(this.logoUrl);
        roomPo.setStatus(3);
        roomPo.setCreator(opUserId);
        roomPo.setCreateTime(Long.valueOf(createdTime.getTime() / 1000).intValue());
        roomPo.setUpdateTime(createdTime);
        roomPo.setCharge(0);
        roomPo.setRoomPath(roomPath.value());

        // roomPath
        // roomType
        RoomSearchPo roomSearchPo = this.beanUtil.map(roomPo, RoomSearchPo.class);
        roomSearchPo.setRoomPath(roomPath.value());
        roomSearchPo.setEmptySeat(roomSearchPo.getPlayerCount());
        roomSearchPo.setLableName(roomData.getLableName());

        // 生成牌局ID
        int roomId = this.genRandomRoomId();
        if (null != jpSettingPo) {
            jpSettingPo.setRoomId(roomId);
            jpSettingPo.setRoomPath(roomPath.value());
        }
        roomPo.setRoomId(roomId);
        roomSearchPo.setRoomId(roomId);

        // 保存数据
        // 调用分服服务，获取牌局服务的IP/PORT
        ServerNodeWapper serverNode;
        if (roomPath == ERoomPath.shortCardAof)
            serverNode = this.aofShortCardNodeManager.assign(roomId);
        else
            serverNode = this.shortCardNodeManager.assign(roomId);
        if (null == serverNode) {
            result.setCode(RespCode.ROOM_SERVER_NOTONLINE.getCode());
            result.setMsg(RespCode.ROOM_SERVER_NOTONLINE.getDesc());
            return result;
        }
        String ip = serverNode.getAccessIp();
        String host = serverNode.getAccessHost();
        int port = serverNode.getAccessPort();
        roomPo.setAccessIp(ip);
        roomPo.setAccessPort(port);
        roomPo.setServerId(serverNode.getServerId());
        roomSearchPo.setAccessIp(ip);
        roomSearchPo.setAccessPort(port);
        roomSearchPo.setServerId(roomPo.getServerId());
        this.roomTransation.createRoom(roomPo, jpSettingPo, roomSearchPo);

        // 组装数据返回
        ShortCardBo roomBo = this.beanUtil.map(roomPo, ShortCardBo.class);
        roomBo.setCreatedTime(roomPo.getCreateTime());
        roomBo.setRoomPath(roomPath);
        roomBo.setGameIp(ip);
        roomBo.setGameHost(host);
        roomBo.setGamePort(port);
        roomBo.setJackpotId(jpSettingPo == null ? null : jpSettingPo.getJackpotId());
        roomBo.setJackpotFund(null == fund ? 0 : fund.intValue());

        // 保存自动开房计划和房间关系
        if (isAutoRoomPlan == 1) {
            roomCreationPlanDao.savePlanRoomRelation(roomId, autoRoomPlanId, roomBo.getName(), new Date());
            // 设置计划空位数
            roomCreationPlanDao.resetEmptySeats(autoRoomPlanId);
        }

        InteriorMessage interiorMessage = InteriorMessage.builder().param1(String.valueOf(roomId)).param2(String.valueOf(opUserId)).type(EMessageCode.INTERIOR_CREATE_IM_GROUP.getCode()).build();
        messageSender.sendInteriorMessage(interiorMessage);

        result.setData(roomBo);

        return result;
    }

    /**
     * 自动开房房间名称。根据规则生成
     *
     * @param roomPath
     * @param name
     * @param autoRoomPlanId
     * @return
     */
    private String generateAutoRoomName(ERoomPath roomPath, String name, Integer autoRoomPlanId) {
        // 查询该计划下现有的牌局数
        Integer count = roomCreationPlanDao.countRoomsNum(autoRoomPlanId, DateUtils.getDateWithZeroTime(new Date(), 0));
        count = count + 1; // 当前房间的编号
        String roomNum = getRoomNum(count);
        String generatedName = name + " " + roomNum; // 牌局名称+序号
        // 判断牌局名称是否重复，重复的，序号往下排
        while (this.existRoomName(generatedName, autoRoomPlanId)) {
            count++;
            roomNum = getRoomNum(count);
            generatedName = name + " " + roomNum; // 牌局名称+序号
        }

        return generatedName;
    }

    /**
     * 生成三位数规则的房间号
     *
     * @param count
     * @return
     */
    private String getRoomNum(Integer count) {
        String roomNum = count + "";
        if (roomNum.length() < 3) {
            int makeup = 3 - roomNum.length();
            // 前面补0到3位数
            for (int i = 0; i < makeup; i++) {
                roomNum = "0" + roomNum;
            }
        }
        return roomNum;
    }

    /**
     * 判断房间名是否重复
     *
     * @param generatedName
     * @return
     */
    private boolean existRoomName(String generatedName, Integer autoRoomPlanId) {
        Integer count = roomCreationPlanDao.countByRoomName(generatedName, autoRoomPlanId);
        return (count == 0 ? false : true);
    }

    /**
     * 请求进入牌局
     * <p>
     * 逻辑：
     * 1. 根据roomId从room_search表中加载记录（status！=0）
     * 不存在，则返回错误；否则进入下一步
     * 2. 计算当前牌局距离结束还剩余多少时间，
     * 如果小于30秒，则返回错误；否则进入下一步
     * 计算公式：游戏时长 + 延时时间 - 当前系统时间
     * <p>
     * 大菠萝无此条件
     * 3. 从redis中加载删除用户列表,key=delUsers:{roomPath}{roomId}
     * 检查当前用户是否存在列表中，存在，则返回错误；否则进入下一步
     * 4. 从redis中加载剔除用户列表，key=roomKickOutUser::{roomId}
     * 检查当前用户是否存在列表中，存在，则返回错误；否则进入下一步
     * 5. 调用分服服务，获取当前牌局所在的游戏服IP/PORT
     * 6. 根据Room_path加载对应的牌局明细数据
     * 7. 组装返回
     *
     * @param opUserId 当前操作用户ID，必填
     * @param roomId   请求进入的房间ID，必填
     * @param roomPath 请求进入的房间类型，必填
     * @param clubId   请求进入的俱乐部ID
     * @return code    操作状态
     * -666    参数设置错误
     * 1       牌局不存在或已经解散
     */
    public InvokedResult<Object> enterRoom(int opUserId, int roomId, ERoomPath roomPath, Integer clubId) {
        return enterRoomInternal(opUserId, roomId, roomPath, clubId, false);
    }

    public InvokedResult<Object> enterRoomById(int opUserId, int roomId, ERoomPath roomPath) {
        return enterRoomInternal(opUserId, roomId, roomPath, null, true);
    }

    private InvokedResult<Object> enterRoomInternal(int opUserId, int roomId, ERoomPath roomPath, Integer clubId, boolean explicitId) {
        InvokedResult<Object> result = new InvokedResult<>();

        // 检查用户是否被踢出
        boolean isKickout = this.roomCache.isKickoutUserOfRoom(opUserId, roomId);
        if (isKickout) {
            result.setCode(RespCode.ROOM_KICKOUT_USER.getCode());
            result.setMsg(RespCode.ROOM_KICKOUT_USER.getDesc());
            return result;
        }
//        // 检查用户是否有权限进入牌局 去掉检查是否俱乐部
//        InvokedResult checkPerm = this.canEnterRoom(opUserId);
//        if (checkPerm.getCode() != 0) {
//            result.setCode(checkPerm.getCode());
//            result.setMsg(checkPerm.getMsg());
//            return result;
//        }

        List<Integer> roomIdLst = new ArrayList<>();
        roomIdLst.add(roomId);
        List<RoomSearchRemainTimeDo> roomLst = this.roomSearchDao.queryBy(roomIdLst);
        if ((null == roomLst || roomLst.isEmpty()) && roomPath != ERoomPath.mtt) {
            result.setCode(RespCode.ROOM_NOT_EXISTING.getCode());
            result.setMsg(RespCode.ROOM_NOT_EXISTING.getDesc());
            return result;
        }

        if (roomPath != ERoomPath.mtt) {
            RoomSearchRemainTimeDo room = roomLst.get(0);

            // 以房间号进入牌局
            if (explicitId) {
                // 聯盟房不可以以房间号进入
                if (room.getTribeRoomType() == 1) {
                    result.setCode(RespCode.ROOM_NO_PERMISSION.getCode());
                    result.setMsg(RespCode.ROOM_NO_PERMISSION.getDesc());
                    return result;
                }
                if (room.getClubRoomType() == 1) {
                    // 俱樂部房必須是成員才可进入
                    List<ClubJoinedPo> userClubs = clubDao.getJoinedClubs(opUserId);
                    boolean isMemberOfClub = userClubs.stream().anyMatch(
                            club -> club.getId() == room.getClubId()
                                && club.getRoleType() != EClubIdentityCode.APPLICANT.getCode()
                    );
                    if (!isMemberOfClub) {
                        result.setCode(RespCode.ROOM_NO_PERMISSION.getCode());
                        result.setMsg(RespCode.ROOM_NO_PERMISSION.getDesc());
                        return result;
                    }
                    clubId = room.getClubId();
                }
            }

            // 俱樂部房間需要检查聯盟和俱樂部的餘額
            // 聯盟或俱樂部虧損超出限額都不可以進入房間
            if (room.getClubRoomType() == 1 && clubId != null) {
                // 联盟房 检查用户是否在俱乐部内
                InvokedResult checkPerm = this.checkUserInClub(opUserId, clubId);
                if (checkPerm.getCode() != 0) {
                    result.setCode(checkPerm.getCode());
                    result.setMsg(checkPerm.getMsg());
                    return result;
                }
                int clubTotalSupply = clubDao.getTribeChipTotalSupply(clubId);
                int clubUnsettled = clubDao.getTribeChipUnsettled(clubId);
                int clubMinTotalSupply = clubDao.getTribeChipMinTotalSupply(clubId);
                int tribeBalance = 0;
                int tribeLossLimit = 0;

                if (room.getTribeId() != 0) {
                    tribeBalance = tribeDao.getTribeChip(room.getTribeId());
                    tribeLossLimit = tribeDao.getTribeInsuranceLossLimit(room.getTribeId());
                }

                if (clubTotalSupply + clubUnsettled < clubMinTotalSupply || 0 > tribeLossLimit + tribeBalance) {
                    result.setCode(RespCode.ROOM_INSURANCE_LOSS_LIMIT.getCode());
                    result.setMsg(RespCode.ROOM_INSURANCE_LOSS_LIMIT.getDesc());
                    log.error("{} - roomId={} 俱樂部總額+未結算/最少需要={}+{}/{} 聯盟餘額/限額={}/{}", result.getMsg(), room.getRoomId(), clubTotalSupply, clubUnsettled, clubMinTotalSupply, tribeBalance, tribeLossLimit);
                    return result;
                }
            }
        }

        // 加载详情数据
        Object data = null;
        if (roomPath == ERoomPath.pineapple)
            data = this.getBpDetail(roomId, roomPath);
        else if (roomPath == ERoomPath.sng)
            data = this.getSngDetail(roomId, roomPath);
        else if (roomPath == ERoomPath.mtt)
            data = this.getMttDetail(roomId, roomPath, opUserId);
        else {
            ERoomPath roomPath1 = roomPath == ERoomPath.shortCardAof ? ERoomPath.shortCard : roomPath;
            // 加载jackpot数据
            RoomJackpotSettionPo settionPo = this.getJackpotSetting(roomId, roomPath1);
            BigDecimal fund = null;
            data = this.getDzDetail(roomId, roomPath, settionPo, fund);
        }

        result.setData(data);
        return result;
    }

    /**
     * 创建奥马哈&必下场&AOF&组合局
     * <p>
     * 逻辑：
     * 1. 申请牌局服务（IP/port）, 由分配服务的服务负责维护自身的结构
     * 2. 保存牌局数据
     * 同时写入三个表：room_search / 对应的牌局表 / 牌局jackpot配置表
     * 状态初始为3
     * 弃用的字段都设置默认值
     *
     * @param opUserId       当前操作用户ID，必填
     * @param roomData       创建牌局所需参数，必填
     * @param roomPath       标识类型：奥马哈 / AOF / 必下场,可选，默认：omaha
     * @param isAutoRoomPlan 是否自动开房计划建房。 0-否，1-是
     * @param autoRoomPlanId 自动建房计划id。当isAutoRoomPlan为1时有值
     * @return code    操作状态
     * 301    jackpot配置数据缺失
     * -666    参数设置错误
     */
    public InvokedResult<OmahaRoomBo> createOmahaRoom(int opUserId, ERoomPath roomPath, OmahaCreationBo roomData, int isAutoRoomPlan, Integer autoRoomPlanId) {
        InvokedResult<OmahaRoomBo> result = new InvokedResult<>();

        if (roomPath != null &&
                roomPath != ERoomPath.omaha &&
                roomPath != ERoomPath.omahaAof &&
                roomPath != ERoomPath.omahaThanoff) {
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("RoomPath设置错误!");
            return result;
        }

        // 检查用户是否有权限进入牌局
        InvokedResult checkPerm = this.canEnterRoom(opUserId);
        if (checkPerm.getCode() != 0) {
            result.setCode(checkPerm.getCode());
            result.setMsg(checkPerm.getMsg());
            return result;
        }

        // 牌局名称不能以ZD开头
        if (isAutoRoomPlan == 0 && roomData.getName() != null && roomData.getName().startsWith("ZD")) {
            result.setCode(RespCode.ROOM_NAME_ERROR.getCode());
            result.setMsg(RespCode.ROOM_NAME_ERROR.getDesc());
            return result;
        }

        if (isAutoRoomPlan == 1) {
            roomData.setName(generateAutoRoomName(roomPath, roomData.getName(), autoRoomPlanId));
        }

        if (null == roomPath)
            roomPath = ERoomPath.omaha;
        Date createdTime = new Date();

        // 加载jackpot数据
        RoomJackpotSettionPo jpSettingPo = null;
        BigDecimal fund = null;
        if (roomData.getClubRoomType() == 0 && roomData.getJackpotOn() == 1) {
            JackpotPoolSettingBo jpSettingBo = jackpotService.getJackpotSettingByJackpotType(roomPath == ERoomPath.omahaAof ? EJackpotType.AOF : EJackpotType.OMAHA, roomData.getSmallMz());
            if (null == jpSettingBo) {
                result.setCode(RespCode.ROOM_JACKPOT_SETTING_NOTFOUND.getCode());
                result.setMsg(RespCode.ROOM_JACKPOT_SETTING_NOTFOUND.getDesc());
                return result;
            }

            // roomId/roomPath 需要手工设置
            jpSettingPo = this.beanUtil.map(jpSettingBo, RoomJackpotSettionPo.class);
//            fund = jpSettingBo.getFund();
            fund = jackpotService.getSystemJackpotPoolData(roomPath == ERoomPath.omaha ? 2 : 3, roomData.getSmallMz());//加载系统全彩池总额
        }

        // 组装牌局表数据
        //roomId & status=3 & creator=userId & createTime& updateTime
        // charge=0
        int realRoomPath = roomPath.value();
        DzRoomPo roomPo = this.beanUtil.map(roomData, DzRoomPo.class);
        if (roomData.getClubRoomType() == 0) {
            PumpPo pumpPo = roomCreationPlanDao.seletRoomPump(
                    roomData.getSmallMz() + "/" + roomData.getSmallMz() * 2,
                    roomPath.value() == 93 ? 31 : roomPath.value());
            roomPo.setMaxFeeChip(pumpPo.getMaxbet());
            roomPo.setServiceCharge(pumpPo.getPump() * 100);
        } else {
            roomPo.setMaxFeeChip((roomData.getSmallMz() * 2) * 3);
            roomPo.setServiceCharge(roomData.getFee());
        }
        roomPo.setLogoUrl(this.logoUrl);
        roomPo.setStatus(3);
        roomPo.setCreator(opUserId);
        roomPo.setCreateTime(Long.valueOf(createdTime.getTime() / 1000).intValue());
        roomPo.setUpdateTime(createdTime);
        roomPo.setCharge(0);
        roomPo.setRoomPath(realRoomPath);
        roomPo.setRoomType(1);
        roomPo.setRoomPath(realRoomPath);

        // roomPath
        // roomType
        RoomSearchPo roomSearchPo = this.beanUtil.map(roomPo, RoomSearchPo.class);
        roomSearchPo.setRoomPath(realRoomPath);
        roomSearchPo.setEmptySeat(roomSearchPo.getPlayerCount());
        roomSearchPo.setLableName(roomData.getLableName());

        // 生成牌局ID
        int roomId = this.genRandomRoomId();
        if (null != jpSettingPo) {
            jpSettingPo.setRoomId(roomId);
            jpSettingPo.setRoomPath(realRoomPath);
        }
        roomPo.setRoomId(roomId);
        roomSearchPo.setRoomId(roomId);

        // 保存数据
        // 调用分服服务，获取牌局服务的IP/PORT
        ServerNodeWapper serverNode;
        if (roomPath == ERoomPath.omahaAof)
            serverNode = this.aofOmahaNodeManager.assign(roomId);
        else serverNode = this.omahaNodeManager.assign(roomId);
        if (null == serverNode) {
            result.setCode(RespCode.ROOM_SERVER_NOTONLINE.getCode());
            result.setMsg(RespCode.ROOM_SERVER_NOTONLINE.getDesc());
            return result;
        }
        String ip = serverNode.getAccessIp();
        String host = serverNode.getAccessHost();
        int port = serverNode.getAccessPort();
        roomPo.setAccessIp(ip);
        roomPo.setAccessPort(port);
        roomPo.setServerId(serverNode.getServerId());
        roomSearchPo.setAccessIp(ip);
        roomSearchPo.setAccessPort(port);
        roomSearchPo.setServerId(roomPo.getServerId());

        this.roomTransation.createRoom(roomPo, jpSettingPo, roomSearchPo);

        // 组装数据返回
        OmahaRoomBo roomBo = this.beanUtil.map(roomPo, OmahaRoomBo.class);
        roomBo.setCreatedTime(roomPo.getCreateTime());
        roomBo.setRoomPath(roomPath);
        roomBo.setGameIp(ip);
        roomBo.setGameHost(host);
        roomBo.setGamePort(port);
        roomBo.setJackpotId(jpSettingPo == null ? null : jpSettingPo.getJackpotId());
        roomBo.setJackpotFund(null == fund ? 0 : fund.intValue());

        // 保存自动开房计划和房间关系
        if (isAutoRoomPlan == 1) {
            roomCreationPlanDao.savePlanRoomRelation(roomId, autoRoomPlanId, roomBo.getName(), new Date());
            // 设置计划空位数
            roomCreationPlanDao.resetEmptySeats(autoRoomPlanId);
        }

        InteriorMessage interiorMessage = InteriorMessage.builder().param1(String.valueOf(roomId)).param2(String.valueOf(opUserId)).type(EMessageCode.INTERIOR_CREATE_IM_GROUP.getCode()).build();
        messageSender.sendInteriorMessage(interiorMessage);

        result.setData(roomBo);
        return result;
    }

    @Override
    public InvokedResult createMttRoom(int opUserId, OfficiialMttCreationBo roomData) {
        InvokedResult result = new InvokedResult<>();
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        ERoomPath roomPath = ERoomPath.mtt;
        Date createdTime = new Date();

        // 调用分服服务，获取牌局服务的IP/PORT
        String ip = mttServerIp;
        int port = mttServerPort;

        // 组装牌局表数据
        MttRoomPo roomPo = this.beanUtil.map(roomData, MttRoomPo.class);
        roomPo.setLogoUrl(this.logoUrl);
        roomPo.setStatus((byte) 1);//0 结束／解散，1 新建/未开赛，2 比赛中
        roomPo.setCreateTime(Long.valueOf(createdTime.getTime() / 1000).intValue());
        roomPo.setCharge(0);

        // roomPath
        // roomType
        RoomSearchPo roomSearchPo = this.beanUtil.map(roomPo, RoomSearchPo.class);
        int realRoomPath = roomPath.value();
        roomSearchPo.setAccessIp(ip);
        roomSearchPo.setAccessPort(port);
        roomSearchPo.setRoomPath(realRoomPath);
        roomSearchPo.setName(roomPo.getMttName());
        roomSearchPo.setMttHunterOn(roomPo.getHunterMatch());
        roomSearchPo.setEmptySeat(roomSearchPo.getPlayerCount());

        // 生成牌局ID
        int roomId = this.genRandomRoomId();
        roomPo.setGameId(roomId);
        roomSearchPo.setRoomId(roomId);

        // 保存数据
        this.roomTransation.createMttRoom(roomPo, roomSearchPo);

        InteriorMessage interiorMessage = InteriorMessage.builder().param1(String.valueOf(roomId)).param2(String.valueOf(opUserId)).type(EMessageCode.INTERIOR_CREATE_IM_GROUP.getCode()).build();
        this.messageSender.sendInteriorMessage(interiorMessage);

        return result;
    }

    @Override
    public InvokedResult<MttViewBo> viewMttRoom(int userId, int matchId, ERoomPath roomPath) {
        InvokedResult<MttViewBo> result = new InvokedResult();

        // 1、查出比赛详情
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);
        Map<Integer, String> blindNote = this.mttCache.getBlindNote(matchId);
        List<Integer> blinds = new LinkedList<>();
        List<Integer> ante = new LinkedList<>();
        for (Map.Entry<Integer, String> stringEntry : blindNote.entrySet()) {
            String[] split = stringEntry.getValue().split(",");
            blinds.add(Integer.valueOf(split[0]));
            ante.add(Integer.valueOf(split[1]));
        }
        // 2、检查比赛是否存在
        if (null == detail || detail.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        }

        MttViewBo.MttViewBoBuilder builder = MttViewBo.builder();
        builder.name(detail.get("mttName"));//比赛名称
        builder.matchId(Integer.parseInt(detail.get("gameID")));//比赛id

        int participants = this.mttCache.getMttEntryNum(matchId);
        //平均记分牌
        //（成功报名人数(participants) * 初始积分) / 剩余玩家数
        int initialScore = 0;
        int leftPlayers = 0;
        Double avgScore = null;
        if (null != detail.get("initialScore"))
            initialScore = Integer.parseInt(detail.get("initialScore"));
        if (null != detail.get("leftPlayers"))
            leftPlayers = Integer.parseInt(detail.get("leftPlayers"));
        if (participants > 0 && initialScore != 0 && leftPlayers > 0) {
            avgScore = (participants * 1.0 * initialScore) / leftPlayers;
        }
        builder.averageScore(null == avgScore ? 0 : avgScore);//平均记分牌

        // 最后涨盲时间(upBlindBeginTime ,毫秒） + 涨盲周期（updateCycle,分钟）- 当前系统时间
        Long remainUpblindTime = null;
        if ((null != detail.get("upBlindBeginTime") ||
                null != detail.get("startTime")) &&
                null != detail.get("updateCycle")) {
            long lastUpBlindBeginTime = 0;
            if (null != detail.get("upBlindBeginTime")) {
                lastUpBlindBeginTime = Long.parseLong(detail.get("upBlindBeginTime"));
            }
            if (lastUpBlindBeginTime <= 0 && null != detail.get("startTime"))
                lastUpBlindBeginTime = Long.parseLong(detail.get("startTime")) * 1000;
            int updateCycle = Integer.parseInt(detail.get("updateCycle"));
            long nextTime = lastUpBlindBeginTime + updateCycle * 60 * 1000;
            long currentTime = System.currentTimeMillis();
            if (nextTime >= currentTime)
                remainUpblindTime = (nextTime - currentTime) / 1000;
        }
        builder.leftBlindTime(remainUpblindTime == null ? 0 : remainUpblindTime.intValue());//剩余涨盲时间
        builder.leftPlayer(leftPlayers);//剩余玩家数

//        builder.isCreator(opUserId == Integer.parseInt(detail.get("creatorID")));//是否创建者
        builder.startTime(Integer.parseInt(detail.get("startTime")));//比赛开始时间 单位秒
        builder.blindNote(blinds);
        builder.ante(ante);
        builder.participants(participants);//报名人数
        builder.upperLimit(Integer.parseInt(detail.get("upperLimit")));//参赛人数上限
        builder.initialChip(Integer.parseInt(detail.get("initialChip")));//初始筹码
        boolean matchCanRebuy = "1".equals(detail.get("allowRebuy"));
        builder.canRebuy(matchCanRebuy);//比赛是否允许重购
        int maxRebuyBlindLevel = Integer.parseInt(detail.get("maxUpdateLevel"));
        builder.maxRebuyLevel(maxRebuyBlindLevel);//可重购的最大盲注级别
        builder.canAppend("1".equals(detail.get("allowAppend")));//是否允许增购
        builder.updateCycle(Integer.parseInt(detail.get("updateCycle")));//涨盲时间
        builder.mttType(Integer.parseInt(detail.get("mttType")));// 6/9人局
        builder.entryTime(Integer.parseInt(detail.get("entryTime")));//开放报名时间
        builder.showTime(Integer.parseInt(detail.get("showTime")));//前端展示时间
        builder.blindType(Integer.parseInt(detail.get("blindType")));//盲注表类型 (0:A/1:B)
        builder.initialScore(Integer.parseInt(detail.get("initialScore")));//起始计分牌
        builder.lowerLimit(Integer.parseInt(detail.get("lowerLimit")));//参赛人数下限
        builder.gameType(Integer.parseInt(detail.get("gameType")));//比赛类型：1 欢乐赛，2 特别赛

        int entryFee = Integer.parseInt(detail.get("registationFee"));    // 报名费
        builder.registerFee(entryFee);//报名费
        builder.serviceFee(Integer.parseInt(detail.get("serviceFee")));//服务费
        builder.initialPool(Integer.parseInt(detail.getOrDefault("initialPool", "0")));//固定奖池
        builder.blindScale(Double.valueOf(detail.getOrDefault("blindTableLevel", "1.0")));//盲注倍数
        builder.hasIpLimit("1".equals(detail.getOrDefault("ip", "0")));//是否开启ip限制
        builder.hasGpsLimit("1".equals(detail.getOrDefault("gps", "0")));//是否开启GPS限制
        builder.canDelay("1".equals(detail.get("allowDelay")));//是否允许延迟报名
        builder.maxDelayLevel(Integer.parseInt(detail.get("maxDelayLevel")));//可延迟报名的最大盲注级别
        int entryTime = Integer.parseInt(detail.getOrDefault("advancedEntry", "0")); // 提前入桌时间
        builder.advancedEntry(entryTime);//提前入桌时间，默认为30分钟 (30x60)

        int blindLevel = Integer.parseInt(detail.get("currentBlindLevel"));// 当前盲注级别
        blindLevel = blindLevel > 0 ? blindLevel : 1;
        builder.currBlindLevel(blindLevel);//当前盲注级别
        //TODO 后期可能有非官方
        int blindType = Integer.parseInt(detail.get("blindType"));
        boolean isOfficial = true;
        if (isOfficial) {//官方赛
            builder.currentSb(Constant.mttBlindTable[blindType][blindLevel - 1][1]);//当前小盲
            builder.currAnte(Constant.mttBlindTable[blindType][blindLevel - 1][2]);//当前前注
        } else {
        }
        int matchCanRebuyTimes = Integer.parseInt(detail.getOrDefault("rebuyTimes", "0"));
        builder.rebuyTimes(matchCanRebuyTimes);//比赛允许重购的次数
        int rebuyPlayers = Integer.parseInt(detail.get("rebuyPlayers"));// 重购玩家数
        int addPlayers = Integer.parseInt(detail.get("appendPlayers"));// 增购玩家数
        // 总奖池 = 报名费 x (成功报名人数 + 重购次数 + 增购次数)
        builder.totalChips(entryFee * (participants + rebuyPlayers + addPlayers));//总奖池
        int startTime = Integer.parseInt(detail.getOrDefault("startTime", "0"));// 比赛开始时间
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (startTime > nowTime) {//比赛未开始
            builder.isStart(false);
        } else {//比赛已经开始
            builder.isStart(true);
            builder.activePlayers(Integer.parseInt(detail.get("activePlayers")));//剩余玩家数
            builder.totalBonus(detail.get("totalBonus"));//总奖池筹码数
            builder.totalEntryFees(entryFee * participants);//报名费
            builder.totalRebuyFees(entryFee * rebuyPlayers);//重购筹码数
            builder.runningTime(nowTime - startTime);//比赛进行时间
        }
        int gameStatus = checkStatus(matchId, userId, detail);
        if (gameStatus == 2) {
            // 立即进入状态，返回IP和port用于建立TCP长连接
            builder.mttIp(mttServerIp);
            builder.mttPort(mttServerPort + "");
//            RoomServer roomServer = Tools.getMTTServerById(gamePath, gameId);
//            if (roomServer != null) {
//                map.put("mttIP", roomServer.getIp());					// 获得该MTT对应的MTT服务器所在的IP
//                map.put("mttPort", roomServer.getPort());				// 获得该MTT对应的MTT服务所在port
//            }
        }

        if (matchCanRebuy) {//比赛开启了重购
            boolean playerCanRebuy = true;//是否可以重购
            int alreadyRebuy = 0;//已经重购次数
            if (gameStatus == 1 || gameStatus == 3 || gameStatus == 4 || gameStatus == 7) {//已报名的状态
                if (this.mttCache.checkPlayerIsOut(matchId, userId)) {//已经被淘汰
                    alreadyRebuy = this.mttCache.getPlayerRebuyTimes(matchId, userId);//已经重购次数
                    if (alreadyRebuy >= matchCanRebuyTimes || blindLevel > maxRebuyBlindLevel) {//重购次数用完或者重购级别条件不允许或奖励圈条件不满足
                        playerCanRebuy = false;
                    }
                } else {
                    playerCanRebuy = false;
                }
            } else {
                playerCanRebuy = false;
            }
            builder.canPlayerRebuy(playerCanRebuy);//玩家能否重购
            builder.alreadyRebuy(alreadyRebuy);
        }

        //TODO 增加了控制带入的时候，需要判断是否处于重购审批状态
        builder.gameStatus(gameStatus);//玩家相对于比赛的游戏状态
        builder.isHunter("1".equals(detail.get("huntsman_match")));//是否猎人赛

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        result.setData(builder.build());

        return result;
    }

    @Override
    public InvokedResult<MttPlayersBo> getMttPlayers(int matchId, int page) {
        InvokedResult<MttPlayersBo> result = new InvokedResult();

        // 1、查出比赛详情
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);

        // 2、检查比赛是否存在
        if (null == detail || detail.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        }

        // 获取该MTT所有已报名成功的玩家列表
        if (page < 1) page = 1;
        Set<String> userIdSet = this.mttCache.getMttEntrySet(matchId);
        List<MttPlayerListBo> list = new ArrayList<>();
        if (userIdSet.size() >= Constant.PAGE_SIZE * (page - 1)) {
            log.debug("getMttPlayers={}", Arrays.toString(userIdSet.toArray()));
            int beginIndex = (page - 1) * Constant.PAGE_SIZE;
            int endIndex = page * Constant.PAGE_SIZE;
            int index = 0;
            for (String playerId : userIdSet) {
                if (index >= beginIndex && index < endIndex) {
                    Map<String, String> userInfo = this.mttCache.getMttPlayerInfo(matchId, Integer.parseInt(playerId));

                    list.add(MttPlayerListBo.builder().nickName(userInfo.get("nickName")).head(userInfo.get("head")).
                            userId(Integer.parseInt(playerId)).score(Integer.parseInt(detail.get("initialScore"))).build());
                    index++;
                }
            }

            result.setData(MttPlayersBo.builder().playerList(list).totalPlayerNum(userIdSet.size()).build());
        }

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Override
    public InvokedResult<MttViewRewardBo> viewMttReward(int matchId, int userId, int page) {
        InvokedResult<MttViewRewardBo> result = new InvokedResult();
        // 1、查出比赛详情
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);
        log.info("mtt detail:{}, matchId:{}, userId:{}, page:{}", detail, matchId, userId, page);
        Map<Integer, Integer> rewardRanking = this.mttCache.getRewardRanking(matchId);
        // 2、检查比赛是否存在
        if (null == detail || detail.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        }
        MttViewRewardBo.MttViewRewardBoBuilder builder = MttViewRewardBo.builder();
        int bonusType = 0;//奖励类型
        int participants = Integer.parseInt(detail.getOrDefault("participants", "0"));
        ;//参加人数
        int rewardNumber = rewardRanking.size();//奖励人数
        int entryFee = Integer.parseInt(detail.get("registationFee"));
        //int initialPool = Integer.parseInt(detail.getOrDefault("initialPool", "0"));
        int initialPool = 0;
        int rebuyTimes = Integer.parseInt(detail.get("rebuyPlayers"));            // 重购玩家数
        int entryBonus = (participants + rebuyTimes) * entryFee;
        int totalBonus = entryBonus + initialPool; //总奖池大小
        bonusType = Integer.parseInt(detail.getOrDefault("bonusType", "0"));

//        if (participants <=   9) {
//            rewardNumber = getBonus(bonusType, 0, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  10 <= participants && participants <=   13) {
//            rewardNumber = getBonus( bonusType, 1, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  14 <= participants && participants <=   20) {
//            rewardNumber = getBonus( bonusType, 2, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  21 <= participants && participants <=   27) {
//            rewardNumber = getBonus( bonusType, 3, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  28 <= participants && participants <=   34) {
//            rewardNumber = getBonus( bonusType, 4, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  35 <= participants && participants <=   41) {
//            rewardNumber = getBonus( bonusType, 5, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  42 <= participants && participants <=   48) {
//            rewardNumber = getBonus( bonusType, 6, totalBonus, builder, matchId, participants, page, detail);
//        } else if (  49 <= participants && participants <=  55) {
//            rewardNumber = getBonus( bonusType, 7, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 56 <= participants && participants <=  62) {
//            rewardNumber = getBonus( bonusType, 8, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 63 <= participants && participants <=  81) {
//            rewardNumber = getBonus( bonusType, 9, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 82 <= participants && participants <=  100) {
//            rewardNumber = getBonus( bonusType, 10, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 101 <= participants && participants <=  119) {
//            rewardNumber = getBonus( bonusType, 11, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 120 <= participants && participants <=  138) {
//            rewardNumber = getBonus( bonusType, 12, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 139 <= participants && participants <=  157) {
//            rewardNumber = getBonus( bonusType, 13, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 158 <= participants && participants <=  176) {
//            rewardNumber = getBonus( bonusType, 14, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 177 <= participants && participants <=  204) {
//            rewardNumber = getBonus( bonusType, 15, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 205 <= participants && participants <=  232) {
//            rewardNumber = getBonus( bonusType, 16, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 233 <= participants && participants <=  260) {
//            rewardNumber = getBonus( bonusType, 17, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 261 <= participants && participants <=  306) {
//            rewardNumber = getBonus( bonusType, 18, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 307 <= participants && participants <=  370) {
//            rewardNumber = getBonus( bonusType, 19, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 371 <= participants && participants <=  434) {
//            rewardNumber = getBonus( bonusType, 20, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 435 <= participants && participants <=  498) {
//            rewardNumber = getBonus( bonusType, 21, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 499 <= participants && participants <= 562) {
//            rewardNumber = getBonus( bonusType, 22, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 563 <= participants && participants <= 626) {
//            rewardNumber = getBonus( bonusType, 23, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 627 <= participants && participants <= 690) {
//            rewardNumber = getBonus( bonusType, 24, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 691 <= participants && participants <= 754) {
//            rewardNumber = getBonus( bonusType, 25, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 755 <= participants && participants <=  818) {
//            rewardNumber = getBonus( bonusType, 26, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 819 <= participants && participants <=  909) {
//            rewardNumber = getBonus( bonusType, 27, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 910 <= participants && participants <=  1027) {
//            rewardNumber = getBonus( bonusType, 28, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1028 <= participants && participants <=  1145) {
//            rewardNumber = getBonus( bonusType, 29, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1146 <= participants && participants <=  1272) {
//            rewardNumber = getBonus( bonusType, 30, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1273 <= participants && participants <=  1399) {
//            rewardNumber = getBonus( bonusType, 31, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1400 <= participants && participants <=  1526) {
//            rewardNumber = getBonus( bonusType, 32, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1527 <= participants && participants <=  1689) {
//            rewardNumber = getBonus( bonusType, 33, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1690 <= participants && participants <=  1861) {
//            rewardNumber = getBonus( bonusType, 34, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 1862 <= participants && participants <=  2033) {
//            rewardNumber = getBonus( bonusType, 35, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 2034 <= participants && participants <=  2394) {
//            rewardNumber = getBonus( bonusType, 36, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 2395 <= participants && participants <=  3115) {
//            rewardNumber = getBonus( bonusType, 37, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 3116 <= participants && participants <= 4196) {
//            rewardNumber = getBonus( bonusType, 38, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 4197 <= participants && participants <= 5637) {
//            rewardNumber = getBonus( bonusType, 39, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 5638 <= participants && participants <= 7078) {
//            rewardNumber = getBonus( bonusType, 40, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( 7079 <= participants && participants <= 8519) {
//            rewardNumber = getBonus( bonusType, 41, totalBonus, builder, matchId, participants, page, detail);
//        } else if ( participants >= 8520) {
//            rewardNumber = getBonus( bonusType, 42, totalBonus, builder, matchId, participants, page, detail);
//        } else {
//            rewardNumber = 0;
//        }
        List<MttRewardListBo> mttRewardListBos = new LinkedList<>();
        for (Map.Entry<Integer, Integer> map : rewardRanking.entrySet()) {
            MttRewardListBo mttRewardListBo = new MttRewardListBo();
            mttRewardListBo.setRank(map.getKey());
            mttRewardListBo.setPercent(1.00);
            mttRewardListBo.setBonus(String.valueOf(map.getValue() / 100));
            mttRewardListBos.add(mttRewardListBo);
            initialPool = initialPool + map.getValue();
        }
        builder.currMttRewardList(mttRewardListBos);
        int gameStatus = checkStatus(matchId, userId, detail);
        if (gameStatus == 2) {
            // 立即进入状态，返回IP和port用于建立TCP长连接
            String serverid = this.roomSearchDao.queryMatchServerId(matchId);
            ServerNodeWapper serverNode = this.mttNodeManager.getNode(serverid);
            if (serverNode == null) {
                builder.mttIp("127.0.0.1");
                builder.mttPort("8055");
            } else {
                builder.mttIp(serverNode.getAccessIp());
                builder.mttPort(String.valueOf(serverNode.getAccessPort()));
            }
        }
        builder.gameStatus(gameStatus);// 赛事状态
        builder.totalChips(initialPool);//总奖池
        builder.initialPool(initialPool);//固定奖池
        builder.bonusType(bonusType);//奖励类型
        builder.rewardNumber(rewardNumber);//奖励人数
        builder.totalPlayerNum(participants);//玩家数


        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        result.setData(builder.build());

        return result;
    }

    @Override
    public InvokedResult<MttTableInfoBo> viewMttTable(int matchId, int page) {
        InvokedResult<MttTableInfoBo> result = new InvokedResult();
        // 1、查出比赛详情
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);

        // 2、检查比赛是否存在
        if (null == detail || detail.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        }
        MttTableInfoBo.MttTableInfoBoBuilder builder = MttTableInfoBo.builder();
        Map<String, String> tableInfoMap = this.mttCache.getMttTableInfo(matchId);
        if (null != tableInfoMap && !tableInfoMap.isEmpty()) {//有牌桌信息
            Map<Integer, String> tempTableInfos = new HashMap<Integer, String>();
            //redis的map没有自动排序，需要手动排序
            for (String key : tableInfoMap.keySet()) {
                tempTableInfos.put(Integer.valueOf(key), tableInfoMap.get(key));
            }
            List<String> tableInfoList = new ArrayList<>(tempTableInfos.values());
            builder.totalDeskNum(tableInfoList.size());//总牌桌数
            List<MttTableInfoListBo> tableList = new ArrayList<>();
            if (tableInfoMap.size() > (page - 1) * Constant.PAGE_SIZE) {// 分页
                tableInfoList = tableInfoList.subList((page - 1) * Constant.PAGE_SIZE,
                        Math.min(page * Constant.PAGE_SIZE, tableInfoList.size()));

                for (String tableStr : tableInfoList) {
                    String[] info = tableStr.split("@");
                    tableList.add(MttTableInfoListBo.builder().deskNum(Integer.parseInt(info[0]))
                            .playerCount(Integer.parseInt(info[1])).totalScore(Integer.parseInt(info[2]))
                            .maxScore(Integer.parseInt(info[3])).minScore(Integer.parseInt(info[4]))
                            .build());
                }
            }
            builder.deskList(tableList);
        } else {//没有牌桌
            builder.totalDeskNum(0);
            builder.deskList(new ArrayList<>());
        }


        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        result.setData(builder.build());
        return result;
    }

    @Override
    public InvokedResult<MttBlindBo> viewMttBlind(int matchId, int userId) {
        InvokedResult<MttBlindBo> result = new InvokedResult();
        // 1、查出比赛详情
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);

        // 2、检查比赛是否存在
        if (null == detail || detail.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        }
        MttBlindBo.MttBlindBoBuilder builder = MttBlindBo.builder();
        int tableSize;
        int blindType = Integer.parseInt(detail.getOrDefault("blindType", "0"));
        boolean isOfficial = true;
        if (isOfficial) {//官方赛
            tableSize = Constant.mttBlindTable[blindType].length;
        } else {
            tableSize = Constant.blindTable[blindType].length;
        }
        builder.maxLevel(Integer.getInteger(detail.getOrDefault("maxUpdateLevel", "0")));//最大盲注级别
        builder.matchName(detail.get("mttName"));//比赛名字
        builder.matchId(Integer.parseInt(detail.get("gameID")));//房间id
        builder.canRebuy("1".equals(detail.get("allowRebuy")));//是否允许重购
        builder.canAppend("1".equals(detail.get("allowAppend")));//是否允许增购
        builder.currentBlindLevel(Integer.parseInt(detail.get("currentBlindLevel")));//当前盲注级别

        List<MttBlindListBo> blindList = new ArrayList<>();
        for (int i = 0; i < tableSize; i++) {
            MttBlindListBo.MttBlindListBoBuilder blindBuilder = MttBlindListBo.builder();
            blindBuilder.level(i + 1);
            blindBuilder.time(Integer.parseInt(detail.get("updateCycle")));
            if (isOfficial) {
                blindBuilder.sb(Constant.mttBlindTable[blindType][i][1]);
                blindBuilder.ante(Constant.mttBlindTable[blindType][i][2]);

            } else {
                blindBuilder.sb(Constant.blindTable[blindType][i][1]);
                blindBuilder.ante(Constant.blindTable[blindType][i][2]);
            }

            blindList.add(blindBuilder.build());
        }
        log.info("blind info:={}" + Arrays.toString(blindList.toArray()));
        builder.blindList(blindList);

//        int startTime = Integer.parseInt(detail.getOrDefault("startTime", "0"));     // 比赛开始时间
//        int nowTime   = (int)(System.currentTimeMillis()/1000);
//        int entryTime = Integer.parseInt(detail.getOrDefault("advancedEntry", "0")); // 提前入桌时间
        int gameStatus = checkStatus(matchId, userId, detail);
        if (gameStatus == 2) {
            // 立即进入状态，返回IP和port用于建立TCP长连接
            builder.mttIp(mttServerIp);
            builder.mttPort(mttServerPort + "");
//            resultJson.put("mttIP", roomServer.getIp());					// 获得该MTT对应的MTT服务器所在的IP
//            resultJson.put("mttPort", roomServer.getPort());				// 获得该MTT对应的MTT服务所在port
        }
        builder.gameStatus(gameStatus);


        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        result.setData(builder.build());
        return result;
    }

    @Override
    public InvokedResult<MttCountDownBo> getCountDown(int matchId) {
        InvokedResult result = new InvokedResult();

        MttRoomPoExample sel = new MttRoomPoExample();
        sel.or().andGameIdEqualTo(matchId);
        List<MttRoomPo> mttRoomList = this.mttRoomDao.selectByExample(sel);
        long time = -1;
        if (null == mttRoomList || mttRoomList.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        } else {
            time = mttRoomList.get(0).getStartTime() * 1000 - System.currentTimeMillis();
        }

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        result.setData(MttCountDownBo.builder().countDownTime(time).build());
        return result;
    }

    /**
     * 查询牌局列表
     * <p>
     * 逻辑：
     * 1. 根据条件从room_search表检索数据
     * 2. 从redis中查询玩家带入过的牌局，修改游戏状态
     * 游戏中  ： 房主已经点击开始，且玩家带入过的牌桌
     * 进行中  ： 房主已经点击开始
     * 等待中  ： 房主还没有点击开始
     * 3. 组装数据返回
     *
     * @param userId      当前请求的用户ID
     * @param roomPathLst 牌局类型，null或空则忽略此条件
     * @param manzhu      盲注类型，null则忽略此条件
     * @param direction   翻页动作，默认：加载第一页
     * @return
     */
    @Override
    public InvokedResult<List<RoomListBo>> listRoomBy(int userId, List<ERoomPath> roomPathLst, Integer clubRoomType, ERoomListMZ manzhu,
                                                      Integer[] mzList, EDirectionPage direction) {
        InvokedResult<List<RoomListBo>> result = new InvokedResult<>();
        // 从room_search表检索数据
        Date sysTime = new Date();
        int sysTimeSec = Long.valueOf(sysTime.getTime() / 1000).intValue();
        Integer remainSec = this.roomRemianSec;
        Integer minMz = null;
        Integer maxMz = null;
        if (manzhu != null) {
            minMz = manzhu.min();
            maxMz = manzhu.max();
        }
        List<Integer> list = Arrays.asList(mzList);

        List<Integer> realRoomPathLst = null;
        if (roomPathLst != null) {
            if (!roomPathLst.isEmpty()) {
                realRoomPathLst = new ArrayList<>();
                for (ERoomPath roomPath : roomPathLst)
                    realRoomPathLst.add(roomPath.value());
            }
        }

        log.info("牌局列表查询的系统时间：" + sysTimeSec);


        List<RoomSearchRemainTimeDo> searchPoLst = null;
        if (!list.isEmpty()) {
            searchPoLst = this.roomSearchDao.queryByPage(realRoomPathLst, clubRoomType,
                    minMz, maxMz, list, remainSec);
        } else {
            searchPoLst = this.roomSearchDao.queryByPage(realRoomPathLst, clubRoomType,
                    minMz, maxMz, null, remainSec);
        }
        List<RoomListBo> searchBoLst = this.beanUtil.map(searchPoLst, RoomListBo.class);
        if (searchBoLst.isEmpty()) {
            result.setData(searchBoLst);
            return result;
        }

        List<ClubJoinedPo> userClubs = clubDao.getJoinedClubs(userId).stream()
                .filter(po -> po.getRoleType() != EClubIdentityCode.APPLICANT.getCode()).collect(Collectors.toList());
        searchBoLst = searchBoLst.stream()
                .filter(room -> room.getClubRoomType() == 0
                        || (room.getClubRoomType() == 2 && room.getCreator() == userId)
                        || userClubs.stream().anyMatch(
                                club -> club.getId() == room.getClubId()
                                    || (room.getTribeRoomType() == 1 && club.getTribeId() != null && club.getTribeId().equals(room.getTribeId()))))
                .collect(Collectors.toList());

        searchBoLst.forEach(bo -> {
            //计算坐下人数
            bo.setSeatedNum(bo.getRoomPu() - bo.getEmptySeat());

            //聯盟房間需要玩家俱樂部的名稱
            if (bo.getTribeRoomType() == 1) {
                Stream<ClubJoinedPo> userClubsOfTribe = userClubs.stream().filter(po -> bo.getTribeId().equals(po.getTribeId()));
                bo.setUserClubsOfTribe(userClubsOfTribe.map(ClubJoinedPo::getClubName).collect(Collectors.joining("||")));
            }
        });

        /**
         * 从redis中查询玩家带入过的牌局，修改游戏状态
         *    游戏中  ： 房主已经点击开始，且玩家带入过的牌桌
         *    进行中  ： 房主已经点击开始
         *    等待中  ： 房主还没有点击开始
         */
        Map<ERoomPath, List<RoomListBo>> roomPathMap = searchBoLst.stream()
                .filter(bo -> bo.getStatus() == 4)
                .collect(Collectors.groupingBy(RoomListBo::getRoomPath));

        roomPathMap.keySet().forEach(roomPath -> {
            List<RoomType> applyLst = this.roomCache.getBringinRoomOfUser(userId);
            if (null != applyLst && !applyLst.isEmpty()) {
                List<Integer> roomIdLst = applyLst.stream().map(RoomType::getRoomId).collect(Collectors.toList());
                List<RoomListBo> targetLst = roomPathMap.get(roomPath);
                targetLst.forEach(bo -> {
                    if (roomIdLst.contains(bo.getRoomId())) {
                        bo.setStatus(5);
                    }
                });
            }
        });

        result.setData(searchBoLst);
        return result;
    }

    @Override
    public InvokedResult<List<RoomListBo>> listClubRoomBy(int userId, int tribeId, int clubId) {
        List<RoomSearchRemainTimeDo> roomSearch = roomSearchDao.queryClubRoomByPage(clubId, tribeId);
        InvokedResult<List<RoomListBo>> result = new InvokedResult<>();
        List<RoomListBo> searchBoLst = this.beanUtil.map(roomSearch, RoomListBo.class);
        List<ClubJoinedPo> userClubs = clubDao.getJoinedClubs(userId).stream()
                .filter(po -> po.getRoleType() != EClubIdentityCode.APPLICANT.getCode()).collect(Collectors.toList());

        searchBoLst.forEach(bo -> {
            //计算坐下人数
            bo.setSeatedNum(bo.getRoomPu() - bo.getEmptySeat());

            //聯盟房間需要玩家俱樂部的名稱
            if (bo.getTribeRoomType() == 1) {
                Stream<ClubJoinedPo> userClubsOfTribe = userClubs.stream().filter(po -> bo.getTribeId().equals(po.getTribeId()));
                bo.setUserClubsOfTribe(userClubsOfTribe.map(ClubJoinedPo::getClubName).collect(Collectors.joining("||")));
            }
        });

        result.setData(searchBoLst);
        return result;
    }

    /**
     * 查询玩家带入过的牌局列表
     * <p>
     * 逻辑：
     * 1. 从redis中查询玩家带入过的牌局ID
     * 2. 从room_search表检索数据
     * 3. 修改状态
     * 如果状态=4,则重置成5
     *
     * @param userId 当前请求的用户ID
     * @return
     */
    public List<RoomListBo> listMyRoomBy(int userId) {
        // 从redis中查询玩家带入过的牌局ID
        List<RoomType> applyLst = this.roomCache.getBringinRoomOfUser(userId);
        List<Integer> creator = this.roomSearchDao.queryRoomIdByCreator(userId);

        if ((null == applyLst || applyLst.isEmpty()) && (null == creator || creator.isEmpty()))
            return null;

        // 从room_search表检索数据
        List<Integer> roomIdLst = new ArrayList<>();
        if (null != applyLst) {
            roomIdLst = applyLst.stream().map(RoomType::getRoomId).collect(Collectors.toList());
        }
        roomIdLst.addAll(creator);
        List<RoomSearchRemainTimeDo> searchPoLst = this.roomSearchDao.queryBy(roomIdLst);
        List<RoomListBo> searchBoLst = this.beanUtil.map(searchPoLst, RoomListBo.class);
        if (null == searchBoLst || searchBoLst.isEmpty())
            return null;

        Set<String> roomSettlement = roomCache.getRoomSettlement(userId);
        List<Integer> settlementRoomId = new ArrayList<>();
        if (roomSettlement != null) {
            for (String roomId : roomSettlement) {
                settlementRoomId.add(Integer.valueOf(roomId));
            }
        }

        List<ClubJoinedPo> userClubs = clubDao.getJoinedClubs(userId).stream()
                .filter(po -> po.getRoleType() != EClubIdentityCode.APPLICANT.getCode()).collect(Collectors.toList());

        searchBoLst.forEach(bo -> {
            //修改状态成已结算
            if (settlementRoomId.contains(bo.getRoomId())) {
                bo.setStatus(6);
            }

            //计算坐下人数
            bo.setSeatedNum(bo.getRoomPu() - bo.getEmptySeat());

            //聯盟房間需要玩家俱樂部的名稱
            if (bo.getTribeRoomType() == 1) {
                Stream<ClubJoinedPo> userClubsOfTribe = userClubs.stream().filter(po -> bo.getTribeId().equals(po.getTribeId()));
                bo.setUserClubsOfTribe(userClubsOfTribe.map(ClubJoinedPo::getClubName).collect(Collectors.joining("||")));
            }
        });

        /**
         * 修改状态
         *    如果状态=4,则重置成5
         */
        searchBoLst.stream().filter(bo -> bo.getStatus() == 4)
                .forEach(bo -> bo.setStatus(5));

        return searchBoLst;
    }

    /**
     * 创建德州自动开房计划
     *
     * @param opUserId     当前操作用户ID，必填
     * @param roomPath     标识类型（普通局 / AOF / 必下场），可选，默认：普通局
     * @param dzCreationBo 创建牌局所需参数，必填
     * @return code    操作状态
     */
    @Override
    public InvokedResult createDzRoomPlan(int opUserId, ERoomPath roomPath, DzCreationBo dzCreationBo) {
        InvokedResult result = new InvokedResult();

        // 检查用户是否有权限进入牌局
        InvokedResult checkPerm = this.canEnterRoom(opUserId);
        if (checkPerm.getCode() != 0) {
            result.setCode(checkPerm.getCode());
            result.setMsg(checkPerm.getMsg());
            return result;
        }

        // 判断计划是否超过了限制
        Integer planCounts = roomCreationPlanDao.countPlans();
        if (planCounts >= autoRoomPlanCountLimit) {
            result.setCode(RespCode.PLAN_COUNT_LIMIT.getCode());
            result.setMsg(RespCode.PLAN_COUNT_LIMIT.getDesc());
            return result;
        }

        RoomCreationPlanConfigPo data = beanUtil.map(dzCreationBo, RoomCreationPlanConfigPo.class);
        data.setRoomPath(roomPath.value());
        data.setRoomMode(0);
        data.setCreateBy(opUserId);
        data.setCreateTime(new Date());
        data.setUpdateBy(opUserId);
        data.setUpdateTime(new Date());
        data.setPlayerCount(dzCreationBo.getRoomPu());
        data.setSbChip(dzCreationBo.getSmallMz());
        data.setMaxPlayTime(dzCreationBo.getGameMaxTime());
        data.setMinPlayTime(dzCreationBo.getGameMinTime());
        data.setMinRate(dzCreationBo.getInMinRate());
        data.setMaxRate(dzCreationBo.getInMaxRate());
        data.setIp(dzCreationBo.getLimitIpOn());
        data.setOpTime(dzCreationBo.getOpTimeSec());
        data.setStraddle(dzCreationBo.getStraddleOn());
        data.setLeaveTable(dzCreationBo.getAheadLeaveOn());
        data.setVp(dzCreationBo.getVpOn());
        data.setMuckSwitch(dzCreationBo.getMuckOn());
        data.setLimitGps(dzCreationBo.getLimitGpsOn());
        data.setInsurance(dzCreationBo.getInsuranceOn());
        data.setJackpotOn(dzCreationBo.getJackpotOn());
        data.setLableName(dzCreationBo.getLableName());
        data.setStatus(1);
        // 查询创建人名字
        UserDetailsInfoBo userDetailsInfoBo = userService.findById(opUserId);
        if (userDetailsInfoBo != null) {
            data.setCreatorName(userDetailsInfoBo.getNickName());
        }
        roomCreationPlanDao.insertRoomCreationPlanConfig(data);
        return result;
    }

    @Override
    public InvokedResult createShortCardRoomPlan(int opUserId, ERoomPath roomPath, ShortCardCreationBo dzCreationBo) {
        InvokedResult result = new InvokedResult();

        // 检查用户是否有权限进入牌局
        InvokedResult checkPerm = this.canEnterRoom(opUserId);
        if (checkPerm.getCode() != 0) {
            result.setCode(checkPerm.getCode());
            result.setMsg(checkPerm.getMsg());
            return result;
        }

        // 判断计划是否超过了限制
        Integer planCounts = roomCreationPlanDao.countPlans();
        if (planCounts >= autoRoomPlanCountLimit) {
            result.setCode(RespCode.PLAN_COUNT_LIMIT.getCode());
            result.setMsg(RespCode.PLAN_COUNT_LIMIT.getDesc());
            return result;
        }

        RoomCreationPlanConfigPo data = beanUtil.map(dzCreationBo, RoomCreationPlanConfigPo.class);
        data.setRoomPath(roomPath.value());
        data.setRoomMode(0);
        data.setCreateBy(opUserId);
        data.setCreateTime(new Date());
        data.setUpdateBy(opUserId);
        data.setUpdateTime(new Date());
        data.setPlayerCount(dzCreationBo.getRoomPu());
        data.setSbChip(dzCreationBo.getSmallMz());
        data.setMaxPlayTime(dzCreationBo.getGameMaxTime());
        data.setMinPlayTime(dzCreationBo.getGameMinTime());
        data.setMinRate(dzCreationBo.getInMinRate());
        data.setMaxRate(dzCreationBo.getInMaxRate());
        data.setIp(dzCreationBo.getLimitIpOn());
        data.setOpTime(dzCreationBo.getOpTimeSec());
        data.setStraddle(dzCreationBo.getStraddleOn());
        data.setLeaveTable(dzCreationBo.getAheadLeaveOn());
        data.setVp(dzCreationBo.getVpOn());
        data.setMuckSwitch(dzCreationBo.getMuckOn());
        data.setLimitGps(dzCreationBo.getLimitGpsOn());
        data.setInsurance(dzCreationBo.getInsuranceOn());
        data.setJackpotOn(dzCreationBo.getJackpotOn());
        data.setLableName(dzCreationBo.getLableName());
        data.setStatus(1);
        // 查询创建人名字
        UserDetailsInfoBo userDetailsInfoBo = userService.findById(opUserId);
        if (userDetailsInfoBo != null) {
            data.setCreatorName(userDetailsInfoBo.getNickName());
        }
        roomCreationPlanDao.insertRoomCreationPlanConfig(data);
        return result;
    }

    /**
     * 创建omaha开房计划
     *
     * @param opUserId
     * @param roomPath
     * @param creationBo
     */
    @Override
    public InvokedResult createOmahaRoomPlan(int opUserId, ERoomPath roomPath, OmahaCreationBo creationBo) {
        InvokedResult result = new InvokedResult();
        // 检查用户是否有权限进入牌局
        InvokedResult checkPerm = this.canEnterRoom(opUserId);
        if (checkPerm.getCode() != 0) {
            result.setCode(checkPerm.getCode());
            result.setMsg(checkPerm.getMsg());
            return result;
        }

        // 判断计划是否超过了限制
        Integer planCounts = roomCreationPlanDao.countPlans();
        if (planCounts >= autoRoomPlanCountLimit) {
            result.setCode(RespCode.PLAN_COUNT_LIMIT.getCode());
            result.setMsg(RespCode.PLAN_COUNT_LIMIT.getDesc());
            return result;
        }

        RoomCreationPlanConfigPo data = beanUtil.map(creationBo, RoomCreationPlanConfigPo.class);
        data.setRoomPath(roomPath.value());
        data.setRoomMode(creationBo.getMode());
        data.setCreateBy(opUserId);
        data.setCreateTime(new Date());
        data.setUpdateBy(opUserId);
        data.setUpdateTime(new Date());
        data.setPlayerCount(creationBo.getRoomPu());
        data.setSbChip(creationBo.getSmallMz());
        data.setMaxPlayTime(creationBo.getGameMaxTime());
        data.setMinPlayTime(creationBo.getGameMinTime());
        data.setMinRate(creationBo.getInMinRate());
        data.setMaxRate(creationBo.getInMaxRate());
        data.setIp(creationBo.getLimitIpOn());
        data.setOpTime(creationBo.getOpTimeSec());
        data.setStraddle(creationBo.getStraddleOn());
        data.setLeaveTable(creationBo.getAheadLeaveOn());
        data.setVp(creationBo.getVpOn());
        data.setMuckSwitch(creationBo.getMuckOn());
        data.setLimitGps(creationBo.getLimitGpsOn());
        data.setInsurance(creationBo.getInsuranceOn());
        data.setJackpotOn(creationBo.getJackpotOn());
        data.setLableName(creationBo.getLableName());
        data.setStatus(1);
        // 查询创建人名字
        UserDetailsInfoBo userDetailsInfoBo = userService.findById(opUserId);
        if (userDetailsInfoBo != null) {
            data.setCreatorName(userDetailsInfoBo.getNickName());
        }
        roomCreationPlanDao.insertRoomCreationPlanConfig(data);
        return result;
    }

    /**
     * 查询自动开房列表权限
     *
     * @return
     */
    @Override
    public List<RoomCreationPlanVo> queryRoomCreationConfigList(Integer userId) {
        List<RoomCreationPlanVo> list = new ArrayList<>();
        List<RoomCreationPlanConfigPo> poList = roomCreationPlanDao.queryList();
        if (poList != null) {
            for (RoomCreationPlanConfigPo po : poList) {
                resetTimezone(po);
                if (po.getStatus() == 3) { // 如果是3状态，维护暂停状态，改为0未开启状态
                    po.setStatus(0);
                }
                RoomCreationPlanVo vo = beanUtil.map(po, RoomCreationPlanVo.class);
                Integer createBy = po.getCreateBy();
                if (createBy.equals(userId)) {// 是否自己的计划
                    vo.setSelf(1);
                }
                list.add(vo);
            }
        }
        return list;
    }

    /**
     * 操作计划
     *
     * @param userId
     * @param id
     * @param status
     */
    @Override
    public InvokedResult operateRoomCreationPlan(int userId, Integer id, Integer status) {
        InvokedResult result = new InvokedResult();
        // 操作计划，需要进行判断是否是在维护状态
//        MaintainMessageBo maintainMessageBo = maintainMessageDao.queryMaintainMessage();
//        if(maintainMessageBo != null) {
//            result.setCode(RespCode.OPERATE_IN_MAINTIAN_TIME.getCode());
//            result.setMsg(RespCode.OPERATE_IN_MAINTIAN_TIME.getDesc());
//            return  result;
//        }

        // 查询计划，判断是否操作人的计划
        RoomCreationPlanConfigPo po = roomCreationPlanDao.queryRoomPlanById(id);
        if (po != null && po.getCreateBy() != null && po.getCreateBy() != userId) {
            log.error("room plan operation permission not enough. createBy:{}, userId:{}.", po.getCreateBy(), userId);
            result.setCode(RespCode.PERMISSION_NOT_ENOUGH.getCode());
            result.setMsg(RespCode.PERMISSION_NOT_ENOUGH.getDesc());
            return result;
        }

        roomCreationPlanDao.updateRoomCreationPlan(userId, id, status);
        return result;
    }

    @Override
    public List<MttRoomListItemBo> listMttRoomBy(int userId, int matchType, int pageNum, int pageSize) {
//        1、默认展示比赛列表中优先开赛的比赛
//        2、如果用户有报名参加比赛，则展示用户已报名的比赛
//        3、如果用户报名参加多个比赛，则展示用户已报名比赛中优先开赛的比赛
//        4、比赛栏仅展示1个比赛

        List<MttRoomSearchDo> mttRoomSearchDoList = this.roomSearchDao.queryMttRoomBy(matchType, (pageNum - 1) * pageSize, pageNum * pageSize);
        String mttLogo = roomSearchDao.queryMttLogo();
        List<MttRoomListItemBo> dataList = new ArrayList<>();
        mttRoomSearchDoList.forEach(d -> {
            d.setLogoUrl("http://47.57.11.101:8061" + mttLogo);
            dataList.add(this.beanUtil.map(d, MttRoomListItemBo.class));
        });
        List<Integer> matchIdList = dataList.stream().map(d -> d.getMatchId()).collect(Collectors.toList());
        List<Map<String, String>> cacheList = this.mttCache.batchGetMttGameDetail(matchIdList);
        Map<Integer, Map<String, String>> gameDetailMap = new HashMap<>();
        for (Map<String, String> temp : cacheList) {
            if (null != temp && !temp.isEmpty()) {
                gameDetailMap.put(Integer.parseInt(temp.get("gameID")), temp);
            }
        }
        //加载当前报名人数
        List<Object> tempEntryNumList = this.mttCache.batchGetMttEntryNum(matchIdList);
        for (int i = 0; i < dataList.size(); i++) {
            Object o = tempEntryNumList.get(i);
            if (null == o || ((Long) o) < 0) {
                dataList.get(i).setCurrEntryNum(0);
            } else {
                dataList.get(i).setCurrEntryNum(((Long) o).intValue());
            }
        }
        //
        for (MttRoomListItemBo bo : dataList) {
            int matchId = bo.getMatchId();
            Map<String, String> gameDetail = gameDetailMap.get(matchId);
            if (gameDetail == null) {
                if (bo.getStartTime() > System.currentTimeMillis()) {
                    bo.setGameStatus(0);
                    bo.setHasGpsLimit(false);
                    bo.setCanPlayerRebuy(false);
                }
            } else {
                bo.setGameStatus(checkStatus(matchId, userId, gameDetail));
                bo.setHasGpsLimit("1".equals(gameDetail.getOrDefault("gps", "0")));
                bo.setCanPlayerRebuy(checkCanPlayerRebuy(gameDetail, userId, matchId, bo.getGameStatus()));
            }
        }


        return dataList;
    }

    /**
     * 判断用户是否能够重购
     *
     * @param gameDetail
     * @param userId
     * @return
     */
    private Boolean checkCanPlayerRebuy(Map<String, String> gameDetail, int userId, int matchId, int gameStatus) {
        boolean matchCanRebuy = "1".equals(gameDetail.get("allowRebuy"));
        if (matchCanRebuy) {//比赛开启了重购
            boolean playerCanRebuy = true;//是否可以重购
            int alreadyRebuy = 0;//已经重购次数
            if (gameStatus == 1 || gameStatus == 3 || gameStatus == 4) {//已报名的状态
                if (this.mttCache.checkPlayerIsOut(matchId, userId)) {//已经被淘汰
                    alreadyRebuy = this.mttCache.getPlayerRebuyTimes(matchId, userId);//已经重购次数
                    int matchCanRebuyTimes = Integer.parseInt(gameDetail.getOrDefault("rebuyTimes", "0"));
                    int blindLevel = Integer.parseInt(gameDetail.get("currentBlindLevel"));// 当前盲注级别
                    blindLevel = blindLevel > 0 ? blindLevel : 1;
                    int maxRebuyBlindLevel = Integer.parseInt(gameDetail.get("maxUpdateLevel"));
                    if (alreadyRebuy >= matchCanRebuyTimes || blindLevel > maxRebuyBlindLevel) {//重购次数用完或者重购级别条件不允许或奖励圈条件不满足
                        playerCanRebuy = false;
                    }
                } else {
                    playerCanRebuy = false;
                }
            } else {
                playerCanRebuy = false;
            }
            return playerCanRebuy;
        }
        return false;
    }

    /**
     * 获取mtt比赛倒计时
     *
     * @param matchId
     * @param userId
     * @return
     */
    @Override
    public InvokedResult<Long> mttCountDown(int matchId, int userId) {
        InvokedResult<Long> result = new InvokedResult<>();
        // 1、查出比赛详情
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);

        // 2、检查比赛是否存在
        if (null == detail || detail.isEmpty()) {
            result.setCode(RespCode.ROOM_MTT_NOT_EXIST.getCode());
            result.setMsg(RespCode.ROOM_MTT_NOT_EXIST.getDesc());
            return result;
        }

        int startTime = Integer.parseInt(detail.get("startTime"));
        long timeCountDown = startTime * 1000L - System.currentTimeMillis();
        result.setData(timeCountDown);
        return result;
    }

    @Override
    public List<PumpVo> findAllPump() {
        return iRoomPumpDao.findPump();
    }

    @Override
    public Integer getRoomPath(int roomId) {
        return iRoomPumpDao.findRoomPathByRoomId(roomId);
    }

    /**
     * 加载指定牌局的jackpot配置数据
     *
     * @param roomId
     * @return
     */
    private RoomJackpotSettionPo getJackpotSetting(int roomId, ERoomPath roomPath) {
        RoomJackpotSettionPo result = null;

        RoomJackpotSettionPoExample select = new RoomJackpotSettionPoExample();
        select.createCriteria()
                .andRoomIdEqualTo(roomId)
                .andRoomPathEqualTo(roomPath.value());
        List<RoomJackpotSettionPo> list = this.jpSettingDao.selectByExample(select);
        if (null != list && !list.isEmpty())
            result = list.get(0);

        return result;
    }

    /**
     * 根据ID加载牌局详情数据
     *
     * @param roomId    待加载的牌局ID，必填
     * @param roomPath  待加载的牌局Path，必填
     * @param settionPo jackpot配置，可选
     * @param fund      jackpot池金额，可选
     * @return
     */
    private DzOmahaRoomBo getDzDetail(int roomId, ERoomPath roomPath,
                                      RoomJackpotSettionPo settionPo, BigDecimal fund) {
        DzRoomPoExample select = new DzRoomPoExample();
        select.createCriteria()
                .andRoomIdEqualTo(roomId);

        List<DzRoomPo> roomPoLst = this.roomDao.selectByExample(select);
        if (null == roomPoLst || roomPoLst.isEmpty())
            return null;

        DzRoomPo roomPo = roomPoLst.get(0);

        DzOmahaRoomBo result = null;
        ServerNodeWapper serverNode = null;
        if (roomPath == ERoomPath.dzpk ||
                roomPath == ERoomPath.dzpkThanoff ||
                roomPath == ERoomPath.dzpkAof) {
            result = this.beanUtil.map(roomPo, DzRoomBo.class);
            if (roomPath == ERoomPath.dzpkAof)
                serverNode = this.aofRoomNodeManager.getNode(roomPo.getServerId());
            else
                serverNode = this.roomNodeManager.getNode(roomPo.getServerId());
        } else if (roomPath == ERoomPath.omaha ||
                roomPath == ERoomPath.omahaThanoff ||
                roomPath == ERoomPath.omahaAof) {
            result = this.beanUtil.map(roomPo, OmahaRoomBo.class);
            if (roomPath == ERoomPath.omahaAof)
                serverNode = this.aofOmahaNodeManager.getNode(roomPo.getServerId());
            else
                serverNode = this.omahaNodeManager.getNode(roomPo.getServerId());
        } else if (roomPath == ERoomPath.shortCard ||
                roomPath == ERoomPath.shortCardAof) {
            result = this.beanUtil.map(roomPo, OmahaRoomBo.class);
            if (roomPath == ERoomPath.shortCard)
                serverNode = this.shortCardNodeManager.getNode(roomPo.getServerId());
            else
                serverNode = this.aofShortCardNodeManager.getNode(roomPo.getServerId());
        }

        if (null == serverNode) {
            log.error("无法根据serverID获取服务节点:{}", roomPo.getServerId());
            return null;
        }
        int jacktype = 0;
        if (roomPath == ERoomPath.dzpk) {
            jacktype = 1;
        } else if (roomPath == ERoomPath.omaha) {
            jacktype = 2;
        } else if (roomPath == ERoomPath.shortCard || roomPath == ERoomPath.shortCardAof) {
            jacktype = 4;
        } else {
            jacktype = 3;
        }
        if (jacktype == 4) {
            fund = jackpotService.getSystemJackpotPoolData(jacktype, roomPo.getQianzhu());
        } else {
            fund = jackpotService.getSystemJackpotPoolData(jacktype, roomPo.getSbChip());
        }

        result.setCreatedTime(roomPo.getCreateTime());
        result.setRoomPath(ERoomPath.of(roomPo.getRoomPath()));
        result.setJackpotId(settionPo == null ? null : settionPo.getJackpotId());
        result.setJackpotFund(null == fund ? 0 : fund.intValue());
        result.setGameIp(serverNode.getAccessIp());
        result.setGameHost(serverNode.getAccessHost());
        result.setGamePort(serverNode.getAccessPort());

        return result;
    }

    /**
     * 根据ID加载牌局详情数据
     *
     * @param roomId   待加载的牌局ID，必填
     * @param roomPath 待加载的牌局Path，必填
     * @return
     */
    private BpRoomBo getBpDetail(int roomId, ERoomPath roomPath) {
        BpRoomPoExample select = new BpRoomPoExample();
        select.createCriteria()
                .andRoomIdEqualTo(roomId);

        List<BpRoomPo> roomPoLst = this.bpRoomDao.selectByExample(select);
        if (null == roomPoLst || roomPoLst.isEmpty())
            return null;

        String ip = "************";
        int port = 8053;
        BpRoomBo roomBo = this.beanUtil.map(roomPoLst.get(0), BpRoomBo.class);
        roomBo.setRoomPath(roomPath);
        roomBo.setGameIp(ip);
        roomBo.setGamePort(port);

        return roomBo;
    }

    /**
     * 根据ID加载牌局详情数据
     *
     * @param roomId   待加载的牌局ID，必填
     * @param roomPath 待加载的牌局Path，必填
     * @return
     */
    private SngRoomBo getSngDetail(int roomId, ERoomPath roomPath) {
        SngRoomPoExample select = new SngRoomPoExample();
        select.createCriteria()
                .andRoomIdEqualTo(roomId);

        List<SngRoomPo> roomPoLst = this.sngRoomDao.selectByExample(select);
        if (null == roomPoLst || roomPoLst.isEmpty())
            return null;

        String ip = "************";
        int port = 8054;
        SngRoomBo roomBo = this.beanUtil.map(roomPoLst.get(0), SngRoomBo.class);
        roomBo.setRoomPath(roomPath);
        roomBo.setGameIp(ip);
        roomBo.setGamePort(port);

        return roomBo;
    }

    /**
     * 根据ID加载牌局详情数据
     *
     * @param matchId  待加载的比赛Id，必填
     * @param roomPath 待加载的牌局Path，必填
     * @return
     */
    private MttRoomBo getMttDetail(int matchId, ERoomPath roomPath, Integer userId) {
        Map<String, String> detail = this.mttCache.getMttGameDetail(matchId);
        Map<Integer, String> blindNote = this.mttCache.getBlindNote(matchId);
        if (null == detail || detail.isEmpty()) {
            return null;
        }

        List<Integer> blinds = new LinkedList<>();
        List<Integer> ante = new LinkedList<>();
        for (Map.Entry<Integer, String> stringEntry : blindNote.entrySet()) {
            String[] split = stringEntry.getValue().split(",");
            blinds.add(Integer.valueOf(split[0]));
            ante.add(Integer.valueOf(split[1]));
        }
        ServerNodeWapper serverNode = this.mttNodeManager.getNode("serverid");
        if (null == serverNode) {
            log.error("无法根据serverID获取服务节点:{}", "serverid");
//            return null;
        } else {

            log.debug("getMttDetail ip={},port={}", serverNode.getAccessIp(), serverNode.getAccessPort());
        }

        MttRoomBo.MttRoomBoBuilder builder = MttRoomBo.builder();
        builder.matchId(matchId);
        builder.roomPath(roomPath.value());
        builder.matchName(detail.get("mttName"));
        builder.ante(ante);
        builder.blindNote(blinds);
//        int startTime = Integer.parseInt(detail.getOrDefault("startTime", "0"));     // 比赛开始时间
//        int nowTime   = (int)(System.currentTimeMillis()/1000);
//        int entryTime = Integer.parseInt(detail.getOrDefault("advancedEntry", "0")); // 提前入桌时间
        builder.gameStatus(checkStatus(matchId, userId, detail));
        builder.mttType(Integer.parseInt(detail.get("mttType")));
        builder.matchIp(mttServerIp);
        builder.matchPort(mttServerPort);

        return builder.build();
    }

    /**
     * 生成空闲随机ID号
     *
     * @return
     */
    private int genRandomRoomId() {
        return roomIdGeneratorService.nextId();
    }

    /**
     * 检查用户是否允许进入牌局
     *
     * @param opUserId 当前请求用户
     * @return
     */
    private InvokedResult canEnterRoom(int opUserId) {
        InvokedResult result = new InvokedResult();

        /**
         * 允许调用条件：
         * 1. 用户未被冻结
         * 2. 用户加入俱乐部
         *    俱乐部状态正常（非关闭）
         * 3. 所属俱乐部已加入联盟
         * 4. 所属俱乐部对应联盟的状态正常（非踢出和转移中）
         * 4.1 联盟的状态是关闭时，只允许进入【带入过的未结束的牌局】
         *
         * 满足以上条件,才进行数据检索
         */
        boolean isUserFreeze = this.userService.isFreezeByUserId(opUserId);
        if (isUserFreeze) {// 账号已被冻结
            result.setCode(RespCode.ROOM_ENTER_USERFREEZE.getCode());
            result.setMsg(RespCode.ROOM_ENTER_USERFREEZE.getDesc());
            return result;
        }
        ClubRecordBo clubBo = this.clubService.getUserClub(opUserId);
        if (null == clubBo) {
            // 玩家未加入俱乐部
            result.setCode(RespCode.ROOM_ENTER_USERNOCLUB.getCode());
            result.setMsg(RespCode.ROOM_ENTER_USERNOCLUB.getDesc());
            return result;
        }
        if (clubBo.getClubStatus() != 0) {
            // 所属俱乐部处于关闭状态
            result.setCode(RespCode.ROOM_ENTER_CLUBCLOSED.getCode());
            result.setMsg(RespCode.ROOM_ENTER_CLUBCLOSED.getDesc());
            return result;
        }
        return result;
    }

    /**
     * 检查用户在俱乐部内
     *
     * @param opUserId 当前请求用户
     * @param clubId 俱乐部id
     * @return
     */
    private InvokedResult checkUserInClub(int opUserId, int clubId) {
        InvokedResult result = new InvokedResult();
        long count = this.clubService.checkUserInClub(opUserId, clubId);
        if (count <= 0) {
            // 玩家未加入俱乐部
            result.setCode(RespCode.ROOM_ENTER_USERNOCLUB.getCode());
            result.setMsg(RespCode.ROOM_ENTER_USERNOCLUB.getDesc());
            return result;
        }
        return result;
    }

    private void resetTimezone(RoomCreationPlanConfigPo po) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(po.getStartTime());
            calendar.add(Calendar.HOUR_OF_DAY, 8);
            po.setStartTime(calendar.getTime());
            calendar.setTime(po.getEndTime());
            calendar.add(Calendar.HOUR_OF_DAY, 8);
            po.setEndTime(calendar.getTime());
        } catch (Exception e) {
            log.error("resetTimezone error.");
        }
    }

    /**
     * 获得我的MTT比赛的当前状态
     *
     * @param matchId //     * @param startTime
     *                //     * @param nowTime
     *                //     * @param nowTime
     * @param userId
     * @return 0:可报名;1:等待开赛;2:延迟报名;3:进行中;4:立即进入;5:报名截止;6:等待审批;7:重购条件不足
     */
    private int checkStatus(int matchId, int userId, Map<String, String> gameDetail) {
        int status = 0;

        int startTime = Integer.parseInt(gameDetail.getOrDefault("startTime", "0"));     // 比赛开始时间
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        int entryTime = Integer.parseInt(gameDetail.getOrDefault("advancedEntry", "0")); // 提前入桌时间

        // 用户报名成功
        if (this.mttCache.checkPlayerIsEnter(matchId, userId)) {
            if (nowTime > startTime) {    // 比赛已开始
                status = 3;                // 状态为进行中
            } else if (startTime - nowTime >= 0 && startTime - nowTime <= entryTime) { // 当前时间小于等开始时间并且大于提前时间
                status = 4;            // 状态为立即进入
            } else {
                status = 1;                // 状态为等待开赛
            }
        } else if (nowTime >= startTime) {    // 当前时间大于开始时间
            // 允许延迟报名：1、当前盲注级别小于等于最大延迟盲注级别；2、小于最大报名人数上限；3、未被淘汰的玩家；4、未进入钱圈
            boolean isAllowDelayAplly = false;
            int allowDelay = Integer.parseInt(gameDetail.getOrDefault("allowDelay", "0"));
            int maxDelayLevel = Integer.parseInt(gameDetail.getOrDefault("maxDelayLevel", "12"));
            int currentBlindLevel = Integer.parseInt(gameDetail.get("currentBlindLevel"));
            int upperLimit = Integer.parseInt(gameDetail.get("upperLimit"));
            int participants = Integer.parseInt(gameDetail.get("participants")); // 获取参加MTT锦标赛的报名成功玩家数目

            if (allowDelay == 1 && currentBlindLevel <= maxDelayLevel && upperLimit > participants) {
                isAllowDelayAplly = true;
            }
            if (isAllowDelayAplly) {
                status = 2;            // 状态为延迟报名
            } else {
                status = 5;            // 状态为报名截止
            }
        } else {
            status = 0;                    // 状态为可报名
        }

        // 增加重购逻辑
        boolean matchCanRebuy = "1".equals(gameDetail.get("allowRebuy"));
        int matchCanRebuyTimes = Integer.parseInt(gameDetail.getOrDefault("rebuyTimes", "0"));
        int blindLevel = Integer.parseInt(gameDetail.get("currentBlindLevel"));// 当前盲注级别
        int maxRebuyBlindLevel = Integer.parseInt(gameDetail.get("maxUpdateLevel"));
        blindLevel = blindLevel > 0 ? blindLevel : 1;
        if (matchCanRebuy) {//比赛开启了重购
            int alreadyRebuy = 0;//已经重购次数
            if (status == 1 || status == 3 || status == 4) {//已报名的状态
                if (this.mttCache.checkPlayerIsOut(matchId, userId)) {//已经被淘汰
                    alreadyRebuy = this.mttCache.getPlayerRebuyTimes(matchId, userId);//已经重购次数
                    if (alreadyRebuy >= matchCanRebuyTimes || blindLevel > maxRebuyBlindLevel) {//重购次数用完或者重购级别条件不允许或奖励圈条件不满足
                        status = 7;
                    }
                }
            }
        }

        log.info("checkStatus. matchId:{}, startTime:{}, nowTime:{}, entryTime:{}, status:{}.", matchId, startTime, nowTime, entryTime, status);
        return status;
    }

    /**
     * 从奖励表中获取第column列的数据并存到传入的map中
     *
     * @param bonusType
     * @param column
     * @return 获得奖励的人数
     */
    private int getBonus(int bonusType, int column, int total, MttViewRewardBo.MttViewRewardBoBuilder builder, int matchId, int participants, int page, Map<String, String> detail) {
        int rewardCount = 0;

        int num = Constant.officalMttBonusNumber[bonusType][column];
        int allRewardCount = num < rewardCount ? rewardCount : num;

        /**
         * 获取对应奖励表范围的奖励百分比
         */
        List<Object[]> bonusContents = new ArrayList<Object[]>();
        for (int i = 0; i < num; i++) {
            double percent = Constant.officalBonusTable[bonusType][i][column];
            if (percent == 0.00) {
                break;
            }
            Object[] obj = new Object[2];
            obj[0] = percent;   // percent
            bonusContents.add(obj);
        }

        List<MttRewardListBo> mttRewardList = new ArrayList<MttRewardListBo>();
        // 获取奖励表中第column列的所有奖励比例
        for (int i = 0; i < allRewardCount; i++) {
            double percent = 0D;
            if (i < num) percent = Constant.getRewardChip(i + 1, bonusContents, participants);

            int str = (int) (total * percent / 100);
            String bonusString;
            if (str < 10000) {
                bonusString = String.valueOf(str);
            } else {
                bonusString = (str / 1000.0) + "k";
            }

            mttRewardList.add(MttRewardListBo.builder().rank(i + 1).percent(percent).bonus(bonusString).build());
        }

        /**
         * 在比赛开始前默认显示为奖励圈最后一名，每次进入该界面时刷新；
         比赛进行中时，重购级别和延迟报名截止前，默认显示为奖励圈最后一名；
         比赛进行中时，重购级别和延迟报名截止后，显示下一个将要决出的名次
         */

        MttRewardListBo lastReward = null;

        int startTime = Integer.parseInt(detail.get("startTime"));
        int nextRewardNum = Integer.parseInt(detail.get("nextRewardNum"));

        int mttReward = 0;
        if (detail.get("mttReward") != null) {
            mttReward = Integer.parseInt(detail.get("mttReward"));
        }

        log.debug("matchId: " + matchId + " nextRewardNum: " + nextRewardNum + " mttReward: " + mttReward);
        // 判断开始时间是否合法
        int nowTime = (int) (System.currentTimeMillis() / 1000);
        if (nowTime < startTime) {
            lastReward = mttRewardList.get(mttRewardList.size() - 1);
        } else {
            if (mttReward == 0) {
                lastReward = mttRewardList.get(mttRewardList.size() - 1);
            } else {
                lastReward = mttRewardList.get(nextRewardNum - 1);
            }

        }

        builder.nextRewardNum(lastReward.getRank());
        builder.nextReward(lastReward.getBonus());

        //分页数据
        List<MttRewardListBo> pageRewardList = new ArrayList<MttRewardListBo>();
        int end = page * (Constant.PAGE_SIZE < mttRewardList.size() ? page * Constant.PAGE_SIZE : mttRewardList.size());

        for (int i = (page - 1) * Constant.PAGE_SIZE; i < end; i++) {
            pageRewardList.add(mttRewardList.get(i));
        }

        builder.currMttRewardList(pageRewardList);
        return num;
    }

    private boolean checkExistRoomName(String name, Integer clubId) {
        Integer count = roomSearchDao.countByRoomName(name, clubId);
        return (count != 0);
    }
}
