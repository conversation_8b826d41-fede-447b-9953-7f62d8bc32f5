package com.dzpk.crazypoker.room.repositories.mysql;

import com.dzpk.crazypoker.room.repositories.mysql.model.TribeMemberBanChatPo;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface TribeMemberBanChatPoDao {

    @Select("SELECT ban_start_time as banStartTime, ban_end_time as banEndTime " +
            "FROM crazy_poker.tribe_member_ban_chat " +
            "WHERE user_id = #{userId} AND tribe_id = #{tribeId} " +
            "AND ban_start_time <= NOW() AND ban_end_time > NOW() ")
    TribeMemberBanChatPo getUserBanChatLast(Integer userId, Integer tribeId);
}