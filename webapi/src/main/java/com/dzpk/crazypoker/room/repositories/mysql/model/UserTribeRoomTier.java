package com.dzpk.crazypoker.room.repositories.mysql.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * UserTribeRoomTier
 *
 * <AUTHOR>
 * @since 2025/6/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserTribeRoomTier {
    private Long id; // ID
    private Integer tribeId; // 联盟ID
    private Integer clubId; // 俱乐部ID
    private Integer userId; // 用户ID
    private Integer tierId; // 层级ID
    private String tierName; // 层级名称
    private Integer tierValue; // 层级值
}
