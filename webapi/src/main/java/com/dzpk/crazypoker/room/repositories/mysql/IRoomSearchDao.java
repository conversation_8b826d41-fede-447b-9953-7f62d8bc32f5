package com.dzpk.crazypoker.room.repositories.mysql;

import com.dzpk.crazypoker.room.repositories.mysql.model.MttRoomSearchDo;
import com.dzpk.crazypoker.room.repositories.mysql.model.RoomSearchRemainTimeDo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface IRoomSearchDao {

    String QUERY_COLUMNS = "rs.`bp_deal_mode` AS `bpDealMode`, " +
            "rs.`bp_joker_on` AS `bpJokerOn`, " +
            "rs.`bp_mode` AS `bpMode`, " +
            "rs.`club_room_type` AS `clubRoomType`, " +
            "rs.`delay_sec` AS `delaySec`, " +
            "rs.`empty_seat` AS `emptySeat`, " +
            "rs.`game_max_time` AS `gameMaxTime`, " +
            "rs.`id` AS `id`, " +
            "rs.`insurance_on` AS `insuranceOn`, " +
            "rs.`jackpot_on` AS `jackpotOn`, " +
            "rs.`lable_name` AS `lableName`, " +
            "rs.`logo_url` AS `logoUrl`, " +
            "rs.`mtt_hunter_on` AS `mttHunterOn`, " +
            "rs.`name` AS `name`, " +
            "rs.`omaha_mode` AS `omahaMode`, " +
            "rs.`open_time` AS `openTime`, " +
            "rs.`player_count` AS `playerCount`, " +
            "rs.`qianzhu` AS `qianzhu`, " +
            "rs.`room_id` AS `roomId`, " +
            "rs.`room_path` AS `roomPath`, " +
            "rs.`sb_chip` AS `sbChip`, " +
            "gr.`in_chip` AS `inChip`, " +
            "rs.`start_time` AS `startTime`, " +
            "rs.`status` AS `status`, " +
            "rs.`updated_item` AS `updatedItem`, " +
            "rs.`updated_time` AS `updatedTime`, " +
            "rs.`tribe_room_type` AS `tribeRoomType`, " +
            "gr.`club_id` AS `clubId`, " +
            "gr.`tribe_id` AS `tribeId`, " +
            "gr.`creator` AS `creator`, " +
            "IF(rs.status=4, UNIX_TIMESTAMP() - UNIX_TIMESTAMP(rs.start_time), 0) AS `elapsedSec`, " +
            "IF(rs.status=4, UNIX_TIMESTAMP(rs.start_time)+rs.game_max_time*60+rs.delay_sec, UNIX_TIMESTAMP()) - UNIX_TIMESTAMP() AS `remainSec` ";

    @Select({"<script>" ,
            "select ", QUERY_COLUMNS,
            " from room_search rs left join club_room_extend cre on rs.room_id=cre.room_id" ,
            " left join group_room gr on rs.room_id=gr.room_id ",
            " where rs.status!=0 " ,
            " <if test='remainSec!=null and remainSec>0'>",
            "<![CDATA[",
            "   AND (rs.status != 4 OR UNIX_TIMESTAMP(rs.start_time)+rs.game_max_time*60+rs.delay_sec >= #{remainSec} + UNIX_TIMESTAMP())" ,
            "]]>",
            " </if>",
            " <if test='roomPath!=null'>",
            "   and rs.room_path in" ,
            "   <foreach collection='roomPath' index='index' item='item' open='(' separator=',' close=')'>",
            "     #{item}",
            "   </foreach>",
            " </if>",
            " <if test='clubRoomType!=null'>",
            "   and rs.club_room_type=#{clubRoomType} ",
            " </if>",
            " <if test='minMz!=null and minMz>0'>",
            "<![CDATA[",
            "   and rs.sb_chip>=#{minMz} " ,
            "]]>",
            " </if>",
            " <if test='maxMz!=null and maxMz>0'>",
            "<![CDATA[",
            "   and rs.sb_chip<=#{maxMz}" ,
            "]]>",
            " </if>",
            " <if test='mzString!=null'>",
            " and rs.sb_chip in" ,
            "<foreach collection='mzString' index='index' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            " </foreach>",
            " </if>",
            " order by rs.open_time desc",
            "</script>"})
    List<RoomSearchRemainTimeDo> queryByPage(@Param("roomPath") List<Integer> roomPathLst, @Param("clubRoomType") Integer clubRoomType, @Param("minMz") Integer minMz, @Param("maxMz") Integer maxMz,
                                             @Param("mzString") List<Integer> mzString,@Param("remainSec") Integer remainSec);
    @Select({"<script>" ,
            "select ", QUERY_COLUMNS,
            " from room_search rs left join club_room_extend cre on rs.room_id=cre.room_id" ,
            " left join group_room gr on rs.room_id=gr.room_id ",
            " where rs.status!=0 and rs.club_room_type = 1 and (gr.club_id=#{clubId} " ,
            " <if test='tribeId != 0'>",
            " OR (cre.tribe_id = #{tribeId} and rs.tribe_room_type = '1')",
            " </if>",
            ") order by rs.open_time desc",
            "</script>"})
    List<RoomSearchRemainTimeDo> queryClubRoomByPage( @Param("clubId")int clubId,@Param("tribeId")int tribeId);

    @Select({"<script>" ,
            "select ", QUERY_COLUMNS,
            " from room_search rs left join club_room_extend cre on rs.room_id=cre.room_id" ,
            " left join group_room gr on rs.room_id=gr.room_id ",
            " where rs.status!=0 and rs.club_room_type = 1 and (gr.club_id in " ,
            "   <foreach item='clubId' index='index' collection='clubIds' open='(' separator=',' close=')'> ",
            "     #{clubId}",
            "   </foreach>" ,
            ") order by rs.open_time desc",
            "</script>"})
    List<RoomSearchRemainTimeDo> queryClubIdRoomByPage( @Param("clubIds") Set<Integer> clubIds);


    @Select({"<script>" ,
            "select tribe_id from tribe_members where tribe_id=#{tribeId}",
            "</script>"})
    List<Integer> queryClubRoomId(@Param("tribeId")int tribeId);
    @Select({"<script>" ,
            "select ", QUERY_COLUMNS,
            " from room_search rs left join club_room_extend cre on rs.room_id=cre.room_id",
            " left join group_room gr on rs.room_id=gr.room_id ",
            " where rs.status!=0 ",
            " <if test='roomIdLst!=null'>",
            "   and rs.room_id in ",
            "   <foreach item='roomId' index='index' collection='roomIdLst' open='(' separator=',' close=')'> ",
            "     #{roomId}",
            "   </foreach>" ,
            " </if>",
            " order by rs.open_time desc",
            "</script>"})
    List<RoomSearchRemainTimeDo> queryBy(@Param("roomIdLst") List<Integer> roomIdLst);
    @Select("select room_id from group_room where creator=#{userId} and status!=0")
    List<Integer> queryRoomIdByCreator(Integer userId);
    @Select("SELECT match_id as matchId,match_name as matchName,logo_url as logoUrl,entry_fee as entryFee,voucher," +
            "upper_limit as upperLimit,(unix_timestamp(start_time) * 1000) as startTime from mtt_room_search WHERE `status` != 0 and status != 3 and mtt_type = #{mttType} ORDER BY start_time desc limit #{pageN},#{pageS}")
    List<MttRoomSearchDo> queryMttRoomBy(@Param("mttType")Integer mttType,@Param("pageN")int pageN,@Param("pageS")int pageS);
    @Select("SELECT banner_url from mtt_banner")
    String queryMttLogo();
    @Select("SELECT * FROM mtt_room_search WHERE match_id = #{matchId}")
    String queryMatchServerId(@Param("matchId") Integer matchId);

    @Select("SELECT match_id as matchId,match_name as matchName,logo_url as logoUrl,entry_fee as entryFee,voucher," +
            "upper_limit as upperLimit,(unix_timestamp(start_time) * 1000) as startTime from mtt_room_search WHERE `status` = 0 ORDER BY start_time LIMIT 1")
    List<MttRoomSearchDo> queryMttRoomByFastStart();

    @Select({"<script> select room_id from group_room where room_id in ",
            "<foreach item='roomId' index='index' collection='roomIdLst' open='(' separator=',' close=')'>",
            " #{roomId}",
            "</foreach></script>"})
    List<Integer> queryExistingRoomIdBy(@Param("roomIdLst") List<Integer> roomIdLst);

    @Select("SELECT count(0) " +
            "FROM room_search t1 " +
            "LEFT JOIN group_room gr ON gr.room_id = t1.room_id " +
            "WHERE t1.name = #{name} " +
            "AND t1.status != 0 " +
            "AND gr.club_id = #{clubId} ")
    Integer countByRoomName(@Param("name") String name, @Param("clubId") Integer clubId);
}
