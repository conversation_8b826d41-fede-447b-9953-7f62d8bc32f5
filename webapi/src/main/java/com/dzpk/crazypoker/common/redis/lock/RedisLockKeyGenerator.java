package com.dzpk.crazypoker.common.redis.lock;


/**
 * RedisLockKeys
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
public class RedisLockKeyGenerator {

    public interface Keys {
        String PLATFORM_ACCOUNT_LOCK = "ACCOUNT_LOCK:PLATFORM:";

        String TRIBE_ACCOUNT_LOCK = "ACCOUNT_LOCK:TRIBE:";

        String CLUB_ACCOUNT_LOCK = "ACCOUNT_LOCK:CLUB:";

        String USER_ACCOUNT_LOCK = "ACCOUNT_LOCK:USER:";

        String BATCH_COINS_RECYCLING_LOCK = "BATCH_COINS_RECYCLING_LOCK:CLUB:";
    }

    public static String generatePlatformKey(Integer platformCode) {
        return Keys.PLATFORM_ACCOUNT_LOCK + platformCode;
    }

    public static String generateTribeKey(Integer tribeId) {
        return Keys.TRIBE_ACCOUNT_LOCK + tribeId;
    }

    public static String generateClubKey(Integer clubId) {
        return Keys.CLUB_ACCOUNT_LOCK + clubId;
    }

    public static String generateUserKey(Integer userId) {
        return Keys.USER_ACCOUNT_LOCK + userId;
    }

    public static String generateBatchCoinsRecyclingLock(Integer batchCoinsRecyclingLock) {
        return Keys.BATCH_COINS_RECYCLING_LOCK + batchCoinsRecyclingLock;
    }

}
