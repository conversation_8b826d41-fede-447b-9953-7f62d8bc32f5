package com.dzpk.crazypoker.common.redis.lock;


import com.dzpk.crazypoker.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * DistributedLock
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Slf4j
public class RedisDistributedLock {
    private final String lockKey;
    private final String lockValue;
    private final long expireTime;
    private final TimeUnit timeUnit;
    private final int retryCount;
    private final long retryIntervalMs;

    // 构造函数增加重试次数和重试间隔参数
    public RedisDistributedLock(String lockKey,
                                long expireTime,
                                TimeUnit timeUnit,
                                int retryCount,
                                long retryIntervalMs) {
        this.lockKey = lockKey;
        this.lockValue = UUID.randomUUID().toString();
        this.expireTime = expireTime;
        this.timeUnit = timeUnit;
        this.retryCount = retryCount;
        this.retryIntervalMs = retryIntervalMs;
    }

    /**
     * 获取锁，支持重试机制
     * @return 锁的状态
     */
    public Boolean lock() {
        // 打印获取锁的信息
        log.info("尝试获取 Redis Lock: key={}, value={}, expireTime={}, timeUnit={}", lockKey, lockValue, expireTime, timeUnit);
        int attempts = 0;
        while (attempts < retryCount) {
            RedisService redisService = SpringContextUtils.getBean(RedisService.class);
            boolean success = redisService.setIfAbsent(lockKey, lockValue, expireTime, timeUnit);
            if (success) {
                return true;  // 成功获取锁
            }
            attempts++;
            try {
                log.info("获取 Redis Lock 失败，Key={}，尝试第 {} 次，等待 {} 毫秒后重试", lockKey, attempts, retryIntervalMs);
                Thread.sleep(retryIntervalMs);  // 等待一段时间再重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;  // 重试次数用尽，返回锁获取失败
    }

    /**
     * 释放锁，原子操作
     */
    public void unlock() {
        String luaScript =
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                        "   return redis.call('del', KEYS[1]) " +
                        "else " +
                        "   return 0 " +
                        "end";

        try {
            RedisService redisService = SpringContextUtils.getBean(RedisService.class);
            StringRedisTemplate template = redisService.getStringRedisTemplate();
            template.execute(
                    new DefaultRedisScript<>(luaScript, Long.class),
                    Collections.singletonList(lockKey),
                    lockValue
            );
            log.info("释放 Redis Lock: key={}, value={}", lockKey, lockValue);
        } catch (Exception e) {
            throw new RuntimeException("Failed to release lock", e);
        }
    }
}
