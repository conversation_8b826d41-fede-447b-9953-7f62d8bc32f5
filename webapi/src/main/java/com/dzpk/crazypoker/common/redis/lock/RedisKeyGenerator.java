package com.dzpk.crazypoker.common.redis.lock;


/**
 * RedisKeyGenerator
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
public class RedisKeyGenerator {

    public interface Keys {
        /**
         * 玩家带入房间
         */
        String USER_BRING_IN_ROOM = "urs:bringInRooms:";
        /**
         * 批量回收联盟币配置
         */
        String BATCH_COINS_RECYCLING_CONFIG = "BATCH_COINS_RECYCLING_CONFIG:";
    }

    public static String generateUserBringInRoomKey(Integer userId) {
        return Keys.USER_BRING_IN_ROOM + userId;
    }

    public static String generateBatchCoinsRecyclingConfigKey(Integer clubId) {
        return Keys.BATCH_COINS_RECYCLING_CONFIG + clubId;
    }



}
