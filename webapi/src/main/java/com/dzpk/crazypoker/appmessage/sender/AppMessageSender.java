package com.dzpk.crazypoker.appmessage.sender;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.appmessage.sender.bean.*;
import com.dzpk.crazypoker.appmessage.sender.config.AppMessageRabbitMqKeys;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * AppMessageSender
 * 消息发送器
 * <AUTHOR>
 * @date 2024/11/11
 */
@Slf4j
@Component
public class AppMessageSender {

    @Resource
    ObjectMapper objectMapper;


    /**
     * 发送业务消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendBusinessMessage(BusinessMessage param) {
        // 校验参数
        if (param == null) {
            log.error("BusinessMessage is null");
            return false;
        }

        if (param.getBusinessCode() == null) {
            log.error("businessCode is null");
            return false;
        }

        if (param.getReceiverUserId() == null) {
            log.error("receiverUserId is null");
            return false;
        }

        sendMessage(AppMessage.builder()
                .messageId(param.getMessageId())
                .businessCode(param.getBusinessCode())
                .businessData(param.getBusinessData())
                .senderId(param.getSenderId())
                .receiverUserId(param.getReceiverUserId())
                .build());

        return true;
    }

    /**
     * 发送通知消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendNoticeMessage(NoticeMessage param) {

        // 校验参数
        if (param == null) {
            log.error("NoticeMessage is null");
            return false;
        }

        if (param.getNoticeId() == null) {
            log.error("noticeId is null");
            return false;
        }

        sendNotice(AppNotice.builder()
                .noticeId(param.getNoticeId())
                .messageId(param.getMessageId())
                .build());
        return true;
    }

    /**
     * 发送模板消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendTemplateMessage(TemplateMessage param) {
        // 校验参数
        if (param == null) {
            log.error("TemplateMessage is null");
            return false;
        }

        if (param.getTplCode() == null) {
            log.error("tplCode is null");
            return false;
        }

        if (param.getReceiverUserId() == null) {
            log.error("receiverUserId is null");
            return false;
        }

        sendMessage(AppMessage.builder()
                .messageId(param.getMessageId())
                .tplCode(param.getTplCode())
                .params(param.getParams())
                .senderId(param.getSenderId())
                .receiverUserId(param.getReceiverUserId())
                .build());

        return true;
    }

    /**
     * 发送内容消息
     * @param param 参数
     * @return 是否发送成功
     */
    public Boolean sendContentMessage(ContentMessage param) {
        // 校验参数
        if (param == null) {
            log.error("ContentMessage is null");
            return false;
        }

        if (param.getContent() == null) {
            log.error("content is null");
            return false;
        }

        // 处理内容消息

        if (param.getCategoryCode() == null) {
            log.error("categoryCode is null");
            return false;
        }

        if (param.getReceiverUserId() == null) {
            log.error("receiverUserId is null");
            return false;
        }

        sendMessage(AppMessage.builder()
                .messageId(param.getMessageId())
                .categoryCode(param.getCategoryCode())
                .content(JSONObject.toJSONString(param.getContent()))
                .renderType(param.getRenderType())
                .params(param.getParams())
                .senderId(param.getSenderId())
                .receiverUserId(param.getReceiverUserId())
                .build());
        return true;
    }


    @Resource
    private RabbitTemplate rabbitTemplate;


    /**
     * 发送 AppMessage 消息
     * @param appMessage 消息
     */
    private void sendMessage(AppMessage appMessage) {
        try {
            // 使用 RabbitTemplate 发送消息到指定的 Exchange 和 RoutingKey
            JsonNode json = objectMapper.valueToTree(appMessage);
            rabbitTemplate.convertAndSend(
                    AppMessageRabbitMqKeys.Exchange.MESSAGE,
                    AppMessageRabbitMqKeys.RoutingKey.MESSAGE,
                    json,
                    message -> {
                        // 手动设置消息属性
                        message.getMessageProperties().setHeader("__TypeId__", "com.fasterxml.jackson.databind.JsonNode");
                        message.getMessageProperties().setContentType("application/json");
                        return message;
                    });
            log.info("Sent message: {}", json);
        } catch (Exception e) {
            log.error("Failed to send message: {}", appMessage, e);
        }
    }

    /**
     * 发送 AppNotice 消息
     * @param appNotice 消息
     */
    private void sendNotice(AppNotice appNotice) {
        try {
            // 使用 RabbitTemplate 发送消息到指定的 Exchange 和 RoutingKey
            JsonNode json = objectMapper.valueToTree(appNotice);
            rabbitTemplate.convertAndSend(
                    AppMessageRabbitMqKeys.Exchange.MESSAGE,
                    AppMessageRabbitMqKeys.RoutingKey.MESSAGE,
                    json,
                    message -> {
                        // 手动设置消息属性
                        message.getMessageProperties().setHeader("__TypeId__", "com.fasterxml.jackson.databind.JsonNode");
                        message.getMessageProperties().setContentType("application/json");
                        return message;
                    });
            log.info("Sent notice: {}", json);
        } catch (Exception e) {
            log.error("Failed to send notice: {}", appNotice, e);
        }
    }





}
