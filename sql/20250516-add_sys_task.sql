-- 定时处理任务
CREATE TABLE crazy_poker.sys_update_task (
   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
   `task_name` varchar(100) NOT NULL COMMENT '任务名称',
   `task_type` int NOT NULL DEFAULT 0 COMMENT '任务类型 1修复入房旧数据',
   `params` longtext COMMENT '参数jsonstring',
   `task_status` tinyint NOT NULL DEFAULT 0 COMMENT '状态 0待执行 1执行中 2执行完成 3执行失败',
   `execute_count` int NOT NULL DEFAULT 0 COMMENT '执行次数',
   `msg` longtext COMMENT '执行信息',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='定时处理任务';

-- 定时处理任务记录
CREATE TABLE crazy_poker.sys_task_record (
   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
   `sys_task_id` int NOT NULL COMMENT '任务id',
   `task_status` tinyint NOT NULL COMMENT '状态 1开始执行 2执行完成 3执行失败',
   `execute_num` int NOT NULL DEFAULT 0 COMMENT '已执行数量',
   `msg` longtext COMMENT '信息',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='定时处理任务记录';

INSERT INTO `crazy_poker`.`sys_update_task` (`task_name`, `task_type`, `params`) VALUES ('修正入房信息任务', 1, '{\"hasBringIn\": 1,\"limit\": 2000}');
