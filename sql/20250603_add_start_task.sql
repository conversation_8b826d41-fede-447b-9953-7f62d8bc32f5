DROP TABLE IF EXISTS crazy_poker.single_task_start_lock;
-- 单任务启动锁
CREATE TABLE crazy_poker.single_task_start_lock (
`id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
`ip` varchar(100) NOT NULL COMMENT 'IP地址',
`port` int NOT NULL COMMENT '端口',
`status` int NOT NULL DEFAULT 0 COMMENT '状态:0启动中,1启动成功,2启动失败',
`service_name` varchar(100) NOT NULL COMMENT '服务名称',
`task_key` varchar(100) NOT NULL COMMENT '任务key',
`unique_id` varchar(100) NOT NULL COMMENT '唯一ID，格式:task_key:yyyymmhhmm',
`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY uk_unique_id (unique_id),
KEY idx_task_service (task_key, service_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='单任务启动锁';