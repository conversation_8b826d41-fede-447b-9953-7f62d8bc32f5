-- 房间手数明细
DROP TABLE IF EXISTS crazy_poker.game_data_room;
CREATE TABLE crazy_poker.game_data_room (
                                            `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                            `ai` tinyint(1) DEFAULT 0 COMMENT '是否为AI：0否/1是',
                                            `allin_hand` int DEFAULT 0 COMMENT 'allin手数',
                                            `allin_win_hand` int DEFAULT 0 COMMENT 'allin赢手数',
                                            `ante` bigint DEFAULT 0 COMMENT '前注',
                                            `at_id` bigint DEFAULT NULL COMMENT 'ai用户ID',
                                            `bcbet_hand` int DEFAULT 0 COMMENT '连续加注手数',
                                            `bet_hand` int DEFAULT 0 COMMENT '下注手数',
                                            `big_blind_hand` int DEFAULT 0 COMMENT '大盲手数',
                                            `big_blind_win_hand` int DEFAULT 0 COMMENT '大盲注获胜手数',
                                            `blind` varchar(50) DEFAULT NULL COMMENT '盲注',
                                            `blind_hand` int DEFAULT 0 COMMENT '盲注手数',
                                            `blind_win_hand` int DEFAULT 0 COMMENT '盲注获胜手数',
                                            `bring_in` bigint DEFAULT 0 COMMENT '带入筹码',
                                            `call_hand` int DEFAULT 0 COMMENT '跟注手数',
                                            `cbet_hand` int DEFAULT 0 COMMENT '持续下注手数',
                                            `check_hand` int DEFAULT 0 COMMENT '让牌手数',
                                            `club_id` int DEFAULT 0 COMMENT '俱乐部ID',
                                            `club_name` varchar(255) DEFAULT NULL COMMENT '俱乐部名称',
                                            `club_random_id` int DEFAULT 0 COMMENT '俱乐部编号',
                                            `earn` bigint DEFAULT 0 COMMENT '总盈利',
                                            `flop_hand` int DEFAULT 0 COMMENT '翻牌手数',
                                            `flop_win_hand` int DEFAULT 0 COMMENT '翻牌获胜手数',
                                            `fold_hand` int DEFAULT 0 COMMENT '弃牌手数',
                                            `game_type` int DEFAULT 0 COMMENT '游戏类型',
                                            `hand_first_time` timestamp COMMENT '第一手开始时间',
                                            `hand_last_time` timestamp COMMENT '最后一手结束时间',
                                            `hands` longtext DEFAULT NULL COMMENT '手牌',
                                            `nickname` varchar(255) DEFAULT NULL COMMENT '玩家昵称',
                                            `pfr_hand` int DEFAULT 0 COMMENT '翻牌前加注手数',
                                            `pool_hand` int DEFAULT 0 COMMENT '入池手数',
                                            `pool_win_hand` int DEFAULT 0 COMMENT '入池获胜手数',
                                            `raise_hand` int DEFAULT 0 COMMENT '下注+加注手数',
                                            `river_hand` int DEFAULT 0 COMMENT '河牌手数',
                                            `river_win_hand` int DEFAULT 0 COMMENT '河牌获胜手数',
                                            `room_club_id` int DEFAULT 0 COMMENT '房间俱乐部ID',
                                            `room_club_name` varchar(255) DEFAULT NULL COMMENT '房间俱乐部名称',
                                            `room_club_random_id` int DEFAULT 0 COMMENT '房间俱乐部编号',
                                            `room_end_time` timestamp COMMENT '房间结束时间',
                                            `room_id` bigint DEFAULT 0 COMMENT '房间ID',
                                            `room_name` varchar(255) DEFAULT NULL COMMENT '房间名称',
                                            `room_start_time` timestamp COMMENT '房间开始时间',
                                            `room_type` int DEFAULT 0 COMMENT '房间类型：1联盟房、2俱乐部房、3私人房',
                                            `room_user_id` varchar(50) DEFAULT NULL COMMENT '房间用户ID',
                                            `service_fee` bigint DEFAULT 0 COMMENT '服务费用',
                                            `showdown_hand` int DEFAULT 0 COMMENT '摊牌手数',
                                            `showdown_win_hand` int DEFAULT 0 COMMENT '摊牌获胜手数',
                                            `small_blind_hand` int DEFAULT 0 COMMENT '小盲注手数',
                                            `small_blind_win_hand` int DEFAULT 0 COMMENT '小盲注获胜手数',
                                            `superior_nickname` varchar(255) DEFAULT NULL COMMENT '上级昵称',
                                            `superior_random_num` varchar(20) DEFAULT NULL COMMENT '上级编号',
                                            `superior_rebate_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '上级返点率',
                                            `superior_user_id` bigint DEFAULT NULL COMMENT '上级用户ID',
                                            `tbet_hand` int DEFAULT 0 COMMENT '3bet手数',
                                            `total_hand` int DEFAULT 0 COMMENT '总手牌手数',
                                            `tribe_id` int DEFAULT 0 COMMENT '联盟ID',
                                            `tribe_name` varchar(255) DEFAULT NULL COMMENT '联盟名称',
                                            `tribe_random_id` int DEFAULT 0 COMMENT '联盟编号',
                                            `turn_hand` int DEFAULT 0 COMMENT '转牌手数',
                                            `turn_win_hand` int DEFAULT 0 COMMENT '转牌获胜手数',
                                            `user_id` bigint DEFAULT 0 COMMENT '用户ID',
                                            `user_random_num` varchar(20) DEFAULT NULL COMMENT '用户编号',
                                            `win_chips` bigint DEFAULT 0 COMMENT '赢得筹码',
                                            `win_hand` int DEFAULT 0 COMMENT '获胜手数',
                                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='房间手数明细';

-- 房间手数明细 - 索引
ALTER TABLE crazy_poker.game_data_room
    ADD UNIQUE INDEX idx_room_user_id (room_user_id),
    ADD INDEX idx_room_id (room_id),
    ADD INDEX idx_ai (ai),
    ADD INDEX idx_room_time (room_id, hand_last_time DESC);


-- 战绩明细
DROP TABLE IF EXISTS crazy_poker.game_data_detail;
CREATE TABLE crazy_poker.game_data_detail (
                                              `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                              `ai` tinyint(1) DEFAULT 0 COMMENT '是否为AI：0否/1是',
                                              `ante` bigint DEFAULT 0 COMMENT '前注金额',
                                              `at_id` bigint DEFAULT NULL COMMENT 'AI用户ID',
                                              `blind` varchar(20) DEFAULT NULL COMMENT '盲注级别(格式：小盲/大盲)',
                                              `bring_in` bigint DEFAULT 0 COMMENT '带入筹码量',
                                              `bring_out` bigint DEFAULT 0 COMMENT '带出筹码量',
                                              `club_id` int DEFAULT 0 COMMENT '俱乐部ID',
                                              `club_insurance_fee_rate` DECIMAL(10,4) DEFAULT 0.00 COMMENT '俱乐部保险费用分成比率(%)',
                                              `club_insurance_ratio` DECIMAL(10,4) DEFAULT 0.00 COMMENT '俱乐部保险分成比率(%)',
                                              `club_insurance_share` bigint DEFAULT 0.00 COMMENT '俱乐部保险分成金额',
                                              `club_lose_rebate_rate` DECIMAL(10,4) DEFAULT 0.00 COMMENT '俱乐部水下返水比例(%)',
                                              `club_name` varchar(255) DEFAULT NULL COMMENT '俱乐部名称',
                                              `club_random_id` int DEFAULT 0 COMMENT '俱乐部编号',
                                              `club_win_rebate_rate` DECIMAL(10,4) DEFAULT 0.00 COMMENT '俱乐部水上返水比例(%)',
                                              `final_earn` bigint DEFAULT 0 COMMENT '最终盈利(带出-带入)',
                                              `game_duration` int DEFAULT 0 COMMENT '游戏时长(秒)',
                                              `game_type` int DEFAULT 61 COMMENT '游戏类型ID 61=德州扑克',
                                              `hand_first_time` timestamp COMMENT '第一手开始时间戳',
                                              `hand_last_time` timestamp COMMENT '最后一手结束时间戳',
                                              `initial_earn` bigint DEFAULT 0 COMMENT '原始盈利',
                                              `insurance_buy` bigint DEFAULT 0 COMMENT '保险买入金额',
                                              `insurance_income` bigint DEFAULT 0 COMMENT '保险赔付金额',
                                              `insurance_total` bigint DEFAULT 0 COMMENT '保险合计金额',
                                              `leave_at` timestamp COMMENT '离开时间戳',
                                              `nickname` varchar(255) DEFAULT NULL COMMENT '玩家昵称',
                                              `platform_fee_rate` DECIMAL(10,4) DEFAULT 0.00 COMMENT '平台服务费率(%)',
                                              `platform_fee_rate_multiplier` int DEFAULT 1 COMMENT '平台费率倍数',
                                              `platform_insurance_share` bigint DEFAULT 0 COMMENT '平台保险分成',
                                              `platform_service_share` bigint DEFAULT 0 COMMENT '平台服务费分成',
                                              `pool_hand` int DEFAULT 0 COMMENT '入池手数',
                                              `pool_win_hand` int DEFAULT 0 COMMENT '入池获胜手数',
                                              `room_club_id` int DEFAULT 0 COMMENT '房间所属俱乐部ID',
                                              `room_club_name` varchar(255) DEFAULT NULL COMMENT '房间所属俱乐部名称',
                                              `room_club_random_id` int DEFAULT 0 COMMENT '房间俱乐部编号',
                                              `room_end_time` timestamp COMMENT '房间结束时间戳',
                                              `room_id` bigint DEFAULT NULL COMMENT '房间ID',
                                              `room_name` varchar(255) DEFAULT NULL COMMENT '房间名称',
                                              `room_start_time` timestamp COMMENT '房间开始时间戳',
                                              `room_type` tinyint DEFAULT 1 COMMENT '房间类型：1联盟房/2俱乐部房/3私人房',
                                              `room_user_id` varchar(50) NOT NULL COMMENT '房间用户唯一ID',
                                              `service_charge` int DEFAULT 0 COMMENT '房间服务费率(%)',
                                              `service_fee` bigint DEFAULT 0 COMMENT '服务费金额',
                                              `sit_at` timestamp COMMENT '坐下时间戳',
                                              `superior_nickname` varchar(255) DEFAULT NULL COMMENT '上级贵宾昵称',
                                              `superior_random_num` varchar(20) DEFAULT NULL COMMENT '上级贵宾编号',
                                              `superior_rebate_rate` DECIMAL(10,4) DEFAULT NULL COMMENT '上级贵宾返点比率(%)',
                                              `superior_user_id` bigint DEFAULT NULL COMMENT '上级贵宾用户ID',
                                              `total_hand` int DEFAULT 0 COMMENT '总手牌数',
                                              `tribe_fee_rate` int DEFAULT 0 COMMENT '联盟服务费分成比率(%)',
                                              `tribe_id` int DEFAULT 0 COMMENT '联盟ID',
                                              `tribe_insurance_share` bigint DEFAULT 0 COMMENT '联盟保险分成',
                                              `tribe_name` varchar(255) DEFAULT NULL COMMENT '联盟名称',
                                              `tribe_platform_fee_rate` int DEFAULT 0 COMMENT '联盟平台服务费分成比率(%)',
                                              `tribe_platform_insurance_fee_rate` int DEFAULT 0 COMMENT '联盟平台保险费分成比率(%)',
                                              `tribe_random_id` int DEFAULT 0 COMMENT '联盟编号',
                                              `tribe_service_share` bigint DEFAULT 0 COMMENT '联盟服务费分成',
                                              `user_id` bigint DEFAULT 0 COMMENT '用户ID',
                                              `user_random_num` varchar(20) DEFAULT NULL COMMENT '用户编号',
                                              `water_bottom_rebate` bigint DEFAULT 0 COMMENT '水下返点金额',
                                              `water_top_rebate` bigint DEFAULT 0 COMMENT '水上返点金额',
                                              `win_chips` bigint DEFAULT 0 COMMENT '赢得筹码量',
                                              `win_hand` int DEFAULT 0 COMMENT '获胜手数',
                                              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='战绩明细';

-- 战绩明细 - 索引
ALTER TABLE crazy_poker.game_data_detail
    ADD UNIQUE INDEX idx_room_user_id (room_user_id),
    ADD INDEX idx_room_end_time (room_end_time DESC),
    ADD INDEX idx_room_id (room_id),
    ADD INDEX idx_user_random_num (user_random_num),
    ADD INDEX idx_tribe_id (tribe_id),
    ADD INDEX idx_club_room_type (club_id, room_type);


-- 个人战绩明细
DROP TABLE IF EXISTS crazy_poker.game_data_profit;
CREATE TABLE crazy_poker.game_data_profit (
                                              `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                              `af_rate` DECIMAL(10,2) DEFAULT 0 COMMENT '激进率(%)',
                                              `ai` TINYINT(1) DEFAULT 0 COMMENT '是否为AI：0否/1是',
                                              `allin_hand` INT DEFAULT 0 COMMENT 'allin手数',
                                              `allin_win_hand` INT DEFAULT 0 COMMENT 'allin赢手数',
                                              `allin_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT 'allin胜率(%)',
                                              `at_id` BIGINT DEFAULT NULL COMMENT 'AI用户ID',
                                              `avg_bring_in` BIGINT DEFAULT 0 COMMENT '平均带入',
                                              `avg_earn` BIGINT DEFAULT 0 COMMENT '平均盈亏',
                                              `bring_in` BIGINT DEFAULT 0 COMMENT '总带入',
                                              `call_hand` INT DEFAULT 0 COMMENT '跟注手数',
                                              `call_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '跟注率(%)',
                                              `cbet_hand` INT DEFAULT 0 COMMENT '持续跟注手数',
                                              `cbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '持续跟注率(%)',
                                              `game_cnt` INT DEFAULT 0 COMMENT '游戏局数',
                                              `gold_avg_bring_in` BIGINT DEFAULT 0 COMMENT '金币平均带入',
                                              `gold_avg_earn` BIGINT DEFAULT 0 COMMENT '金币平均盈亏',
                                              `gold_bring_in` BIGINT DEFAULT 0 COMMENT '金币总带入',
                                              `gold_earn` BIGINT DEFAULT 0 COMMENT '金币总盈亏',
                                              `gold_game_cnt` INT DEFAULT 0 COMMENT '金币总局数',
                                              `last_sync_day` INT DEFAULT 0 COMMENT '同步日期(YYYYMMDD)',
                                              `last_sync_month_day` VARCHAR(21) DEFAULT NULL COMMENT '同步月份起止时间',
                                              `last_sync_week_day` VARCHAR(21) DEFAULT NULL COMMENT '同步周起止时间',
                                              `month_af_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月激进率(%)',
                                              `month_allin_hand` INT DEFAULT 0 COMMENT '月allin手数',
                                              `month_allin_win_hand` INT DEFAULT 0 COMMENT '月allin赢手数',
                                              `month_allin_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月allin胜率(%)',
                                              `month_avg_bring_in` BIGINT DEFAULT 0 COMMENT '月平均带入',
                                              `month_avg_earn` BIGINT DEFAULT 0 COMMENT '月平均盈亏',
                                              `month_bring_in` BIGINT DEFAULT 0 COMMENT '月带入',
                                              `month_call_hand` INT DEFAULT 0 COMMENT '月跟注手数',
                                              `month_call_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月跟注率(%)',
                                              `month_cbet_hand` INT DEFAULT 0 COMMENT '月持续落注手数',
                                              `month_cbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月持续落注率(%)',
                                              `month_game_cnt` INT DEFAULT 0 COMMENT '游戏总局数',
                                              `month_gold_avg_bring_in` BIGINT DEFAULT 0 COMMENT '月金币平均带入',
                                              `month_gold_avg_earn` BIGINT DEFAULT 0 COMMENT '月金币平均盈亏',
                                              `month_gold_bring_in` BIGINT DEFAULT 0 COMMENT '月金币带入',
                                              `month_gold_earn` BIGINT DEFAULT 0 COMMENT '月金币盈亏',
                                              `month_gold_game_cnt` INT DEFAULT 0 COMMENT '月金币局数',
                                              `month_pfr_hand` INT DEFAULT 0 COMMENT '月翻前加注手数',
                                              `month_pfr_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月翻前加注率(%)',
                                              `month_pool_hand` INT DEFAULT 0 COMMENT '月入池手数',
                                              `month_pool_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月入池率(%)',
                                              `month_pool_win_hand` INT DEFAULT 0 COMMENT '月入池赢手数',
                                              `month_pool_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月入池胜率(%)',
                                              `month_raise_hand` INT DEFAULT 0 COMMENT '月加注手数',
                                              `month_raise_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月加注率(%)',
                                              `month_showdown_hand` INT DEFAULT 0 COMMENT '月摊牌手数',
                                              `month_showdown_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月摊牌率(%)',
                                              `month_showdown_win_hand` INT DEFAULT 0 COMMENT '月摊牌手数',
                                              `month_showdown_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月摊牌胜率(%)',
                                              `month_tbet_hand` INT DEFAULT 0 COMMENT '月3bet手数',
                                              `month_tbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月3bet率(%)',
                                              `month_total_earn` BIGINT DEFAULT 0 COMMENT '月总盈亏',
                                              `month_total_hand` INT DEFAULT 0 COMMENT '月总手数',
                                              `month_win_hand` INT DEFAULT 0 COMMENT '月赢手数',
                                              `month_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '月胜率(%)',
                                              `nickname` VARCHAR(255) DEFAULT NULL COMMENT '玩家昵称',
                                              `pfr_hand` INT DEFAULT 0 COMMENT '翻前加注手数',
                                              `pfr_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '翻前加注率(%)',
                                              `pool_hand` INT DEFAULT 0 COMMENT '入池手数',
                                              `pool_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '入池率(%)',
                                              `pool_win_hand` INT DEFAULT 0 COMMENT '入池赢手数',
                                              `pool_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '入池胜率(%)',
                                              `raise_hand` INT DEFAULT 0 COMMENT '加注手数',
                                              `raise_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '加注率(%)',
                                              `showdown_hand` INT DEFAULT 0 COMMENT '摊牌手数',
                                              `showdown_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '摊牌率(%)',
                                              `showdown_win_hand` INT DEFAULT 0 COMMENT '摊牌赢手数',
                                              `showdown_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '摊牌胜率(%)',
                                              `tbet_hand` INT DEFAULT 0 COMMENT '3bet手数',
                                              `tbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '3bet率(%)',
                                              `total_earn` BIGINT DEFAULT 0 COMMENT '总盈亏',
                                              `total_hand` INT DEFAULT 0 COMMENT '总手数',
                                              `user_id` BIGINT NOT NULL COMMENT '用户ID',
                                              `user_random_num` VARCHAR(20) DEFAULT NULL COMMENT '用户编号',
                                              `week_af_rate` DECIMAL(10,2) DEFAULT 0 COMMENT '周激进率(%)',
                                              `week_allin_hand` INT DEFAULT 0 COMMENT '周allin手数',
                                              `week_allin_win_hand` INT DEFAULT 0 COMMENT '周allin赢手数',
                                              `week_allin_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周allin胜率(%)',
                                              `week_avg_bring_in` BIGINT DEFAULT 0 COMMENT '周平均带入',
                                              `week_avg_earn` BIGINT DEFAULT 0 COMMENT '周平均盈亏',
                                              `week_bring_in` BIGINT DEFAULT 0 COMMENT '周总带入',
                                              `week_call_hand` INT DEFAULT 0 COMMENT '周跟注手数',
                                              `week_call_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周跟注率(%)',
                                              `week_cbet_hand` INT DEFAULT 0 COMMENT '周持续跟注手数',
                                              `week_cbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周持续跟注率(%)',
                                              `week_game_cnt` INT DEFAULT 0 COMMENT '周游戏总数',
                                              `week_gold_avg_bring_in` BIGINT DEFAULT 0 COMMENT '周金币平均带入',
                                              `week_gold_avg_earn` BIGINT DEFAULT 0 COMMENT '周金币平均盈亏',
                                              `week_gold_bring_in` BIGINT DEFAULT 0 COMMENT '周金币带入',
                                              `week_gold_earn` BIGINT DEFAULT 0 COMMENT '周金币盈亏',
                                              `week_gold_game_cnt` INT DEFAULT 0 COMMENT '周金币局数',
                                              `week_pfr_hand` INT DEFAULT 0 COMMENT '周翻前加注手数',
                                              `week_pfr_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周翻前加注率(%)',
                                              `week_pool_hand` INT DEFAULT 0 COMMENT '周入池手数',
                                              `week_pool_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周入池率(%)',
                                              `week_pool_win_hand` INT DEFAULT 0 COMMENT '周池赢手数',
                                              `week_pool_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周池赢率(%)',
                                              `week_raise_hand` INT DEFAULT 0 COMMENT '周加注手数',
                                              `week_raise_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周加注率(%)',
                                              `week_showdown_hand` INT DEFAULT 0 COMMENT '周摊牌手数',
                                              `week_showdown_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周摊牌率(%)',
                                              `week_showdown_win_hand` INT DEFAULT 0 COMMENT '周摊牌胜场',
                                              `week_showdown_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周摊牌胜率(%)',
                                              `week_tbet_hand` INT DEFAULT 0 COMMENT '周3bet手数',
                                              `week_tbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周3bet率(%)',
                                              `week_total_earn` BIGINT DEFAULT 0 COMMENT '周总盈亏',
                                              `week_total_hand` INT DEFAULT 0 COMMENT '周总手数',
                                              `week_win_hand` INT DEFAULT 0 COMMENT '周赢手数',
                                              `week_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '周胜率(%)',
                                              `win_hand` INT DEFAULT 0 COMMENT '赢手数',
                                              `win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '胜率(%)',
                                              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                              `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                              PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人战绩明细';

-- 个人战绩明细 - 索引
ALTER TABLE crazy_poker.game_data_profit
    ADD UNIQUE INDEX idx_user_id (user_id),
    ADD INDEX idx_user_random_num (user_random_num);

-- 用户联盟每日战绩统计表
DROP TABLE IF EXISTS crazy_poker.user_tribe_game_data_daily;
CREATE TABLE crazy_poker.user_tribe_game_data_daily (
                                                        `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                        `af_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '激进率(%)',
                                                        `ai` TINYINT(1) DEFAULT 0 COMMENT '是否为AI：0否/1是',
                                                        `allin_hand` INT DEFAULT 0 COMMENT 'allin手数',
                                                        `allin_win_hand` INT DEFAULT 0 COMMENT 'allin赢手数',
                                                        `allin_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT 'allin胜率(%)',
                                                        `at_id` BIGINT DEFAULT NULL COMMENT 'AI用户ID',
                                                        `avg_bring_in` DECIMAL(12,2) DEFAULT 0 COMMENT '平均带入',
                                                        `avg_earn` DECIMAL(12,2) DEFAULT 0 COMMENT '平均盈利',
                                                        `bring_in` DECIMAL(12,2) DEFAULT 0 COMMENT '总带入',
                                                        `call_hand` INT DEFAULT 0 COMMENT '跟注手数',
                                                        `call_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '跟注率(%)',
                                                        `cbet_hand` INT DEFAULT 0 COMMENT '持续下注手数',
                                                        `cbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '持续下注率(%)',
                                                        `game_cnt` INT DEFAULT 0 COMMENT '游戏总局数',
                                                        `nickname` VARCHAR(255) DEFAULT NULL COMMENT '玩家昵称',
                                                        `pfr_hand` INT DEFAULT 0 COMMENT '翻前加注手数',
                                                        `pfr_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '翻前加注率(%)',
                                                        `pool_hand` INT DEFAULT 0 COMMENT '入池手数',
                                                        `pool_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '入池率(%)',
                                                        `pool_win_hand` INT DEFAULT 0 COMMENT '入池赢手数',
                                                        `pool_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '入池胜率(%)',
                                                        `raise_hand` INT DEFAULT 0 COMMENT '加注手数',
                                                        `raise_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '加注率(%)',
                                                        `showdown_hand` INT DEFAULT 0 COMMENT '摊牌手数',
                                                        `showdown_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '摊牌率(%)',
                                                        `showdown_win_hand` INT DEFAULT 0 COMMENT '摊牌赢手数',
                                                        `showdown_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '摊牌胜率(%)',
                                                        `tbet_hand` INT DEFAULT 0 COMMENT '3Bet手数',
                                                        `tbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '3Bet率(%)',
                                                        `total_earn` DECIMAL(12,2) DEFAULT 0 COMMENT '总盈亏',
                                                        `total_hand` INT DEFAULT 0 COMMENT '总手数',
                                                        `tribe_id` INT DEFAULT 0 COMMENT '联盟ID',
                                                        `tribe_name` VARCHAR(255) DEFAULT NULL COMMENT '联盟名称',
                                                        `tribe_random_id` INT DEFAULT 0 COMMENT '联盟编号',
                                                        `user_id` BIGINT NOT NULL COMMENT '用户ID',
                                                        `user_random_num` VARCHAR(20) DEFAULT NULL COMMENT '用户编号',
                                                        `win_hand` INT DEFAULT 0 COMMENT '赢手数',
                                                        `win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '胜率(%)',
                                                        `last_sync_day` INT DEFAULT 0 COMMENT '同步日期(YYYYMMDD)',
                                                        `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                                        `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户联盟每日战绩统计表';

-- 用户联盟每日战绩统计表 - 索引
ALTER TABLE crazy_poker.user_tribe_game_data_daily
    ADD INDEX idx_tribe_id (tribe_id),
ADD INDEX idx_sync_tribe (last_sync_day, tribe_id),
ADD INDEX idx_tribe_user (tribe_id, user_id),
ADD INDEX idx_user_random_num (user_random_num);



-- 用户联盟每日战绩统计表
DROP TABLE IF EXISTS crazy_poker.user_game_data_daily;
CREATE TABLE crazy_poker.user_game_data_daily (
                                                  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                  `af_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '激进率(%)',
                                                  `ai` TINYINT(1) DEFAULT 0 COMMENT '是否为AI：0否/1是',
                                                  `allin_hand` INT DEFAULT 0 COMMENT 'allin手数',
                                                  `allin_win_hand` INT DEFAULT 0 COMMENT 'allin赢手数',
                                                  `allin_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT 'allin胜率(%)',
                                                  `at_id` BIGINT DEFAULT NULL COMMENT 'AI用户ID',
                                                  `avg_bring_in` DECIMAL(12,2) DEFAULT 0 COMMENT '平均带入',
                                                  `avg_earn` DECIMAL(12,2) DEFAULT 0 COMMENT '平均盈利',
                                                  `bring_in` DECIMAL(12,2) DEFAULT 0 COMMENT '总带入',
                                                  `call_hand` INT DEFAULT 0 COMMENT '跟注手数',
                                                  `call_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '跟注率(%)',
                                                  `cbet_hand` INT DEFAULT 0 COMMENT '持续下注手数',
                                                  `cbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '持续下注率(%)',
                                                  `game_cnt` INT DEFAULT 0 COMMENT '游戏总局数',
                                                  `last_sync_day` INT DEFAULT 0 COMMENT '同步日期(YYYYMMDD)',
                                                  `nickname` VARCHAR(255) DEFAULT NULL COMMENT '玩家昵称',
                                                  `pfr_hand` INT DEFAULT 0 COMMENT '翻前加注手数',
                                                  `pfr_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '翻前加注率(%)',
                                                  `pool_hand` INT DEFAULT 0 COMMENT '入池手数',
                                                  `pool_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '入池率(%)',
                                                  `pool_win_hand` INT DEFAULT 0 COMMENT '入池赢手数',
                                                  `pool_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '入池胜率(%)',
                                                  `raise_hand` INT DEFAULT 0 COMMENT '加注手数',
                                                  `raise_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '加注率(%)',
                                                  `showdown_hand` INT DEFAULT 0 COMMENT '摊牌手数',
                                                  `showdown_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '摊牌率(%)',
                                                  `showdown_win_hand` INT DEFAULT 0 COMMENT '摊牌赢手数',
                                                  `showdown_win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '摊牌胜率(%)',
                                                  `tbet_hand` INT DEFAULT 0 COMMENT '3Bet手数',
                                                  `tbet_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '3Bet率(%)',
                                                  `total_earn` DECIMAL(12,2) DEFAULT 0 COMMENT '总盈亏',
                                                  `total_hand` INT DEFAULT 0 COMMENT '总手数',
                                                  `user_id` BIGINT NOT NULL COMMENT '用户ID',
                                                  `user_random_num` VARCHAR(20) DEFAULT NULL COMMENT '用户编号',
                                                  `win_hand` INT DEFAULT 0 COMMENT '赢手数',
                                                  `win_rate` DECIMAL(10,4) DEFAULT 0 COMMENT '胜率(%)',
                                                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                                  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日游戏数据统计表';

-- 用户每日游戏数据统计表 - 索引
ALTER TABLE crazy_poker.user_game_data_daily
    ADD INDEX idx_sync_user (last_sync_day, user_id),
ADD INDEX idx_user_random_num (user_random_num);