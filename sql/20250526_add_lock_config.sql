DROP TABLE IF EXISTS crazy_poker.redis_lock_config;
-- Redis锁配置
CREATE TABLE crazy_poker.redis_lock_config (
                                               `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                               `name` varchar(100) NOT NULL COMMENT '名称',
                                               `code` varchar(100) NOT NULL COMMENT '锁的编码',
                                               `expire_time` bigint COMMENT '过期时间',
                                               `time_unit` varchar(50) NOT NULL COMMENT '过期时间单位: NANOSECONDS,MICROSECONDS,MILLISECONDS,SECONDS,MINUTES,HOURS,DAYS',
                                               `retry_count` int NOT NULL DEFAULT 0 COMMENT '重试次数',
                                               `retry_interval_ms` int NOT NULL DEFAULT 0 COMMENT '重试间隔毫秒',
                                               `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分布式锁配置';

-- 默认锁配置
INSERT INTO crazy_poker.redis_lock_config (name, code, expire_time, time_unit, retry_count, retry_interval_ms) VALUES
('默认锁配置', 'default', 5, 'SECONDS', 10, 500);

-- 房间结算锁配置
INSERT INTO crazy_poker.redis_lock_config (name, code, expire_time, time_unit, retry_count, retry_interval_ms) VALUES
('房间结算-平台账户锁', 'room_settlement:platform', 10, 'SECONDS', 20, 500),
('房间结算-联盟账户锁', 'room_settlement:tribe', 10, 'SECONDS', 20, 500),
('房间结算-俱乐部账户锁', 'room_settlement:club', 10, 'SECONDS', 20, 500),
('房间结算-用户账户锁', 'room_settlement:user', 10, 'SECONDS', 20, 500);

-- 房间带入锁配置
INSERT INTO crazy_poker.redis_lock_config (name, code, expire_time, time_unit, retry_count, retry_interval_ms) VALUES
('房间带入-用户账户锁', 'room_add_chips:user', 5, 'SECONDS', 10, 500);

-- AI自动分配币锁配置
INSERT INTO crazy_poker.redis_lock_config (name, code, expire_time, time_unit, retry_count, retry_interval_ms) VALUES
('AI自动分配币-提前离桌分配任务', 'coins_recycling:ahead', 5, 'SECONDS', 10, 500),
('AI自动分配币-批量分配任务', 'coins_recycling:batch', 5, 'SECONDS', 10, 500),
('AI自动分配币-提前分配任务-俱乐部账户锁', 'coins_recycling:ahead:club', 5, 'SECONDS', 10, 500),
('AI自动分配币-提前分配任务-用户账户锁', 'coins_recycling:ahead:user', 5, 'SECONDS', 10, 500),
('AI自动分配币-批量分配任务-俱乐部账户锁', 'coins_recycling:batch:club', 5, 'SECONDS', 10, 500),
('AI自动分配币-批量分配任务-用户账户锁', 'coins_recycling:batch:user', 5, 'SECONDS', 10, 500);

