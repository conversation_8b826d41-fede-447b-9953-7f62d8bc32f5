package com.allinpokers.yunying.tier.dao;


import com.allinpokers.yunying.tier.bean.MemberRoomTier;
import com.allinpokers.yunying.tier.bean.MemberRoomTierQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * TierDao
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Mapper
public interface TierDao {

    List<MemberRoomTier> findMemberRoomTier(MemberRoomTierQuery memberRoomTierQuery);

    Long countMemberRoomTier(MemberRoomTierQuery memberRoomTierQuery);

    List<MemberRoomTier> findUserClubInfo(MemberRoomTierQuery memberRoomTierQuery);

    MemberRoomTier findTierInfo(MemberRoomTierQuery memberRoomTierQuery);

    MemberRoomTier findUserTier(MemberRoomTierQuery memberRoomTierQuery);

    void insertTribeDefaultRoomTier(MemberRoomTierQuery memberRoomTierQuery);

}
