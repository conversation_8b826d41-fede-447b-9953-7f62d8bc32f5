package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.crazypoker.TribeMembers;
import com.allinpokers.yunying.entity.crazypoker.example.TribeMembersExample;
import com.allinpokers.yunying.entity.crazypoker.key.TribeMembersKey;
import com.allinpokers.yunying.entity.plus.tribe.TribeMemberAndUser;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 部落俱乐部列表  Mapper
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface TribeMembersDao extends BaseDao<TribeMembers, TribeMembersExample, TribeMembersKey> {
    /**
     * 通过联盟id 获取联盟的成员列表的信息，可以通过条件筛选
     *
     * @param tribeId
     * @return
     */
    @Select("<script> " +
            " SELECT club_record.chip as chip, club_record.header clubHead, club_record.use_custom useCustom, club_record.custom_url customUrl, tribe_members.*," +
            "club_record.trans_type transType,club_record.open_rebate open_rebate,club_record.`random_id` club_random_id, club_record.`name` club_name,club_record.`creator` club_creator,club_record.`create_time` club_create_time ,club_record.`upper_limit` club_upper_limit,club_record.`club_members`," +
            "club_record.profit club_profit,club_record.`fund` club_fund,u.nike_name club_creator_name,u.PHONE phone, u.random_num as userRandomNum,tribe.profit as tribeProfit" +
            "  FROM tribe_members left join tribe_record tribe on tribe_members.tribe_id=tribe.id,club_record ,user_details_info u " +
            " WHERE  tribe_members.`tribe_id`=#{tribeId} " +
            "<if test='profit !=null and profit>0'> AND club_record.`profit`=#{profit}</if>" +
            "<trim prefix='and (' suffix=')' prefixOverrides='OR' >" +
            "<if test='clubIdList != null and clubIdList.size>0 '>OR club_record.random_id in " +
            "  <foreach collection='clubIdList' item='item' open='(' close=')' separator=','> " +
            "            #{item}" +
            "  </foreach>" +
            "</if>" +
            "<if test='clubNameList != null and clubNameList.size>0 '> OR club_record.`name` in " +
            "  <foreach collection='clubNameList' item='item' open='(' close=')' separator=','> " +
            "            #{item}" +
            "  </foreach>" +
            "</if>" +
            "</trim> " +
            "AND tribe_members.`club_id`=club_record.`id` AND  u.USER_ID =club_record.creator " +
            "</script> ")
    List<TribeMemberAndUser> selectTribeMembers(@Param("tribeId") Integer tribeId, @Param("profit") Integer profit, @Param("clubIdList") List<String> clubeIdList, @Param("clubNameList") List<String> clubNameList);


    /**
     * 通过联盟id 获取联盟的成员列表的信息，可以通过条件筛选 显性俱乐部id 或者昵称
     *
     * @param tribeId 联盟id
     * @return
     */
    @Select("<script> " +
            "SELECT tribe_members.*," +
            "club_record.trans_type transType,club_record.open_rebate open_rebate, club_record.`random_id` club_random_id," +
            "club_record.`name` club_name,club_record.`creator` club_creator,club_record.`create_time` club_create_time ," +
            "club_record.`upper_limit` club_upper_limit,club_record.`club_members`,club_record.profit club_profit," +
            "club_record.`fund` club_fund,club_record.tribe_profit tribeProfit,u.nike_name club_creator_name,u.PHONE phone, u.random_num as userRandomNum" +
            "  FROM tribe_members ,club_record ,user_details_info u " +
            "WHERE  tribe_members.`tribe_id`=#{tribeId} " +
            "<if test='profit !=null and profit>0'> AND club_record.`profit`=#{profit}</if>" +
            "<trim prefix='and (' suffix=')' prefixOverrides='OR' >" +
            "<if test='clubRandomIdList != null and clubRandomIdList.size>0 '>" +
            "  OR club_record.random_id in " +
            "  <foreach collection='clubRandomIdList' item='item' open='(' close=')' separator=','> " +
            "            #{item}" +
            "  </foreach>" +
            "</if>" +
            "<if test='clubNameList != null and clubNameList.size>0 '> OR club_record.`name` in " +
            "  <foreach collection='clubNameList' item='item' open='(' close=')' separator=','> " +
            "            #{item}" +
            "  </foreach>" +
            "</if>" +
            "</trim> " +
            "AND tribe_members.`club_id`=club_record.`id` AND  u.USER_ID =club_record.creator " +
            "</script> ")
    List<TribeMemberAndUser> selectTribeMembersByClubRandomIdOrClubName(@Param("tribeId") Integer tribeId, @Param("profit") Integer profit, @Param("clubRandomIdList") List<String> clubRandomIdList,
                                                                        @Param("clubNameList") List<String> clubNameList);

        @Select({
                "<script>",
                "SELECT count(0)",
                "FROM tribe_members",
                "WHERE tribe_id = #{tribeId}",
                "AND type != 1",
                "</script>"
        })
        int countTribeMembersExceptCreater(@Param("tribeId") Integer tribeId);


    @Select("select tribe_id from tribe_members where club_id = #{clubId} limit 1")
    Integer getTribeIdByClubId(@Param("clubId") Integer clubId);


}