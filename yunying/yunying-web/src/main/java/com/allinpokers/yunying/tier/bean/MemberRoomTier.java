package com.allinpokers.yunying.tier.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MemberRoomTier
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberRoomTier {

    private String randomNum;

    private String nickname;

    private String clubIds;

    private String clubRandomIds;

    private String clubNames;

    private String tierName;

    private Integer tierId;

    private Integer userId;

    private Integer tribeId;
    
    private Integer value;


}
