package com.allinpokers.yunying.tier.service;


import com.allinpokers.yunying.mongodb.model.GameDataDetail;
import com.allinpokers.yunying.tier.bean.MemberRoomTier;
import com.allinpokers.yunying.tier.bean.MemberRoomTierQuery;
import com.allinpokers.yunying.tier.dao.TierDao;
import com.allinpokers.yunying.util.excel.ExcelModel;
import com.allinpokers.yunying.util.excel.ExcelRow;
import com.allinpokers.yunying.util.excel.ExcelSheet;
import com.allinpokers.yunying.util.excel.ExcelUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * TierService
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Component
public class TierService {

    @Resource
    TierDao tierDao;

    /**
     * 导出成员层级记录
     * @param query
     * @param response
     */
    public void exportMemberRoomTier(MemberRoomTierQuery query, HttpServletResponse response) {

        long total = tierDao.countMemberRoomTier(query);

        List<MemberRoomTier> list = new ArrayList<>();

        if (total > 0) {
            int pageNum = 1;
            int pageSize = 2000;
            int totalPage = (int) Math.ceil((double) total / pageSize);
            for (int i = 0; i < totalPage; i++) {
                PageHelper.startPage(pageNum, pageSize);
                List<MemberRoomTier> pageList = tierDao.findMemberRoomTier(query);
                if (pageList != null && !pageList.isEmpty()) {
                    // 查询分层消息
                    MemberRoomTier tribeInfo = tierDao.findTierInfo(query);

                    // 设置分层名称
                    if (tribeInfo != null) {
                        for (MemberRoomTier info : pageList) {
                            info.setTierName(tribeInfo.getTierName());
                        }
                    }
                    // 拼接用户ID字符串
                    String userIds = "";
                    for (int j = 0; j < pageList.size(); j++) {
                        MemberRoomTier info = pageList.get(j);
                        if (j == 0) {
                            userIds = String.valueOf(info.getUserId());
                        } else {
                            userIds += "," + info.getUserId();
                        }
                    }
                    // 设置查询条件中的用户ID
                    query.setUserIds(userIds);

                    // 查询用户俱乐部信息
                    List<MemberRoomTier> userClubInfo = tierDao.findUserClubInfo(query);

                    for (MemberRoomTier memberRoomTier : pageList) {
                        // 设置俱乐部信息
                        for (MemberRoomTier clubInfo : userClubInfo) {
                            if (memberRoomTier.getUserId().equals(clubInfo.getUserId())) {
                                memberRoomTier.setClubIds(clubInfo.getClubIds());
                                memberRoomTier.setClubRandomIds(clubInfo.getClubRandomIds());
                                memberRoomTier.setClubNames(clubInfo.getClubNames());
                            }
                        }
                    }

                    // 将查询结果添加到总列表中
                    list.addAll(pageList);
                }
                pageNum++;
            }
        }

        ExcelSheet sheet = new ExcelSheet("层级成员列表");
        List<ExcelModel> models = new ArrayList<>();

        models.add(ExcelModel.builder()
                .rows(Lists.newArrayList(new ExcelRow().add("记录总数：").add(total)))
                .afterBlankLine(1)
                .build());

        String[] title = new String[]{"序号", "玩家ID", "玩家名称", "俱乐部ID", "俱乐部名称", "当前分层"};

        List<ExcelRow> rows = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            MemberRoomTier info = list.get(i);
            ExcelRow row = new ExcelRow()
                    .add(i + 1)
                    // 玩家ID
                    .add(info.getRandomNum())
                    // 玩家名称
                    .add(info.getNickname())
                    // 俱乐部ID
                    .add(info.getClubRandomIds())
                    // 俱乐部名称
                    .add(info.getClubNames())
                    // 当前分层
                    .add(info.getTierName());
            rows.add(row);
        }

        models.add(ExcelModel.builder()
                .titles(Lists.newArrayList(title))
                .rows(rows)
                .afterBlankLine(1)
                .build());

        sheet.setModels(models);

        try {
            String fileName = URLEncoder.encode(sheet.getName(), "UTF-8");
            response.setHeader("Content-Type", "application/octet-stream");
            response.setHeader("Content-Disposition", String.format("attachment;fileName=\"%s.%s\"", fileName, "xlsx"));
            ExcelUtils.write(response.getOutputStream(), sheet);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
