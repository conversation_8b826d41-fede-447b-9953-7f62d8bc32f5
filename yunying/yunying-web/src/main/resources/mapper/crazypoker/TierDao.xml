<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.tier.dao.TierDao">

  <resultMap id="MemberRoomTierMap" type="com.allinpokers.yunying.tier.bean.MemberRoomTier">
    <result property="randomNum" column="random_num"/>
    <result property="nickname" column="nickname"/>
    <result property="clubRandomIds" column="club_random_ids"/>
    <result property="clubNames" column="club_names"/>
    <result property="tierName" column="tier_name"/>
    <result property="tierId" column="tier_id"/>
    <result property="userId" column="user_id"/>
    <result property="tribeId" column="tribe_id"/>
    <result property="value" column="value"/>
  </resultMap>

  <select id="findMemberRoomTier" resultMap="MemberRoomTierMap" parameterType="com.allinpokers.yunying.tier.bean.MemberRoomTierQuery">
    SELECT
      URT.id,
      URT.tribe_id,
      URT.user_id,
      URT.tier_id,
      UDI.random_num,
      UDI.nike_name as nickname
    FROM user_room_tier URT
      LEFT JOIN tribe_room_tier TMRT ON URT.tier_id = TMRT.id
      LEFT JOIN user_details_info UDI ON URT.user_id = UDI.user_id
    WHERE URT.tribe_id = #{tribeId} AND URT.tier_id = #{tierId}
    <if test="userRandomNums != null">
      AND UDI.random_num in (${userRandomNums})
    </if>
    <if test="clubId != null">
      AND URT.user_id IN (
      SELECT
      cm.user_id
      FROM tribe_members tm
      LEFT JOIN club_members cm ON tm.club_id = cm.club_id
      WHERE tm.tribe_id = #{tribeId} AND cm.club_id = #{clubId})
    </if>
  </select>

  <select id="findUserClubInfo" resultMap="MemberRoomTierMap" parameterType="com.allinpokers.yunying.tier.bean.MemberRoomTierQuery">
      SELECT
        cm.user_id,
        GROUP_CONCAT(DISTINCT cr.id ORDER BY cr.id) AS club_ids,
        GROUP_CONCAT(DISTINCT cr.name ORDER BY cr.id) AS club_names,
        GROUP_CONCAT(DISTINCT cr.random_id ORDER BY cr.id) AS club_random_ids
      FROM
        crazy_poker.club_members cm
          JOIN
        crazy_poker.tribe_members tm ON cm.club_id = tm.club_id
          JOIN
        crazy_poker.club_record cr ON cm.club_id = cr.id
      WHERE
        cm.user_id IN (${userIds}) AND tm.tribe_id = #{tribeId}
      GROUP BY cm.user_id
  </select>

  <select id="findTierInfo" resultMap="MemberRoomTierMap" parameterType="com.allinpokers.yunying.tier.bean.MemberRoomTierQuery">
    SELECT
      rt.id AS tier_id,
      COALESCE(trt.tier_name, rt.tier_name) AS tier_name
    FROM crazy_poker.room_tier rt
           LEFT JOIN crazy_poker.tribe_room_tier trt ON trt.tier_id = rt.id AND trt.tribe_id = #{tribeId}
    WHERE rt.id = #{tierId}
  </select>

  <select id="countMemberRoomTier" resultType="java.lang.Long" parameterType="com.allinpokers.yunying.tier.bean.MemberRoomTierQuery">
    SELECT
        count(URT.id) as count
    FROM user_room_tier URT
    LEFT JOIN tribe_room_tier TMRT ON URT.tier_id = TMRT.id
    LEFT JOIN user_details_info UDI ON URT.user_id = UDI.user_id
    WHERE URT.tribe_id = #{tribeId} AND URT.tier_id = #{tierId}
    <if test="userRandomNums != null">
      AND UDI.random_num in (${userRandomNums})
    </if>
    <if test="clubId != null">
      AND URT.user_id IN (
      SELECT
      cm.user_id
      FROM tribe_members tm
      LEFT JOIN club_members cm ON tm.club_id = cm.club_id
      WHERE tm.tribe_id = #{tribeId} AND cm.club_id = #{clubId})
    </if>
  </select>

  <select id="findUserTier" resultMap="MemberRoomTierMap" parameterType="com.allinpokers.yunying.tier.bean.MemberRoomTierQuery">
    select
      urt.tribe_id,
      urt.user_id,
      urt.tier_id,
      rt.tier_name,
      rt.value
    from user_room_tier urt
    left join room_tier rt on urt.tier_id = rt.id
    where urt.user_id = #{userId} and urt.tribe_id = #{tribeId}
  </select>
  
  <insert id="insertTribeDefaultRoomTier" parameterType="com.allinpokers.yunying.tier.bean.MemberRoomTierQuery">
    insert into user_room_tier(tribe_id,user_id,tier_id)
    values (#{tribeId}, #{userId}, (select rt.id from room_tier rt where manageable = 1 order by value asc limit 1))
  </insert>

</mapper>