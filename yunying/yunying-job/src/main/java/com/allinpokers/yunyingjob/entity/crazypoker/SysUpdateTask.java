package com.allinpokers.yunyingjob.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 定时处理任务  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysUpdateTask {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Integer id;

    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String taskName;

    /**
     * 任务类型 1修复入房旧数据
     */
    @ApiModelProperty("任务类型 1修复入房旧数据")
    private Integer taskType;

    /**
     * 需要处理总条数
     */
    @ApiModelProperty("需要处理总条数")
    private Integer total;

    /**
     * 已执行数量
     */
    @ApiModelProperty("已执行数量")
    private Integer executeNum;

    /**
     * 参数jsonstring
     */
    @ApiModelProperty("参数jsonstring")
    private String params;

    /**
     * 状态 0待执行 1执行中 2执行完成 3执行失败
     */
    @ApiModelProperty("状态 0待执行 1执行中 2执行完成 3执行失败")
    private Integer taskStatus;

    /**
     * 执行次数
     */
    @ApiModelProperty("执行次数")
    private String executeCount;

    /**
     * 执行信息
     */
    @ApiModelProperty("执行信息")
    private String msg;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 操作人ID
     */
    @ApiModelProperty("操作人ID")
    private Integer operatorId;
}