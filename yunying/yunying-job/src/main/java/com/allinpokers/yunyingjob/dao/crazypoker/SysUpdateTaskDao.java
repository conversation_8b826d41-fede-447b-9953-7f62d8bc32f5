package com.allinpokers.yunyingjob.dao.crazypoker;

import com.allinpokers.yunyingjob.entity.crazypoker.SysUpdateTask;
import com.allinpokers.yunyingjob.entity.crazypoker.plus.task.UserSeatLeaveTime;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时处理任务  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SysUpdateTaskDao {

    @Select("select * from crazy_poker.sys_update_task where task_type = #{type} and task_status = 0 limit 1")
    SysUpdateTask getWaitingTask(@Param("type") Integer type);

    @Select("select ujr.user_id, ujr.room_id, CONCAT(ujr.room_id, '_', ujr.user_id) roomUserId, " +
            "MIN(ubl.create_time) first_seat_time , MIN(ubl.create_time) last_seat_time, " +
            "MAX(ufc.create_time) last_leave_time, ujr.updated_time " +
            "from user_join_room  ujr " +
            "left join user_balance_audit_log ubl on ubl.user_id = ujr.user_id and ujr.room_id = ubl.room_id and ubl.type = 2 " +
            "left join user_integral_fee_contribution ufc on ufc.user_id = ujr.user_id and ufc.room_id = ujr.room_id " +
            "WHERE ujr.has_bringin = #{hasBringIn} and ujr.first_seat_time is null " +
            "group by roomUserId having first_seat_time is not null " +
            "order by ujr.updated_time desc " +
            "limit #{limit}")
    List<UserSeatLeaveTime> getSeatTimeList(@Param("hasBringIn") Integer hasBringIn, @Param("limit") Integer limit);

    @Select("select count(*) from crazy_poker.user_join_room where has_bringin = #{hasBringIn} and first_seat_time is null")
    int countUserJoinNeedUpdate(@Param("hasBringIn") Integer hasBringIn);

    @Update("update crazy_poker.user_join_room set first_seat_time = #{firstSeatTime}, last_seat_time = #{lastSeatTime}, last_leave_time = #{lastLeaveTime} " +
            "where user_id = #{userId} and room_id = #{roomId}")
    int updateUserJoinRoomSeatTime(@Param("userId") Integer userId, @Param("roomId") Integer roomId, @Param("firstSeatTime") LocalDateTime firstSeatTime,
                                   @Param("lastSeatTime") LocalDateTime lastSeatTime, @Param("lastLeaveTime") LocalDateTime lastLeaveTime);

    @Update("update crazy_poker.sys_update_task set task_status = 1 where id = #{id} and task_status = 0")
    int updateTaskRunning(@Param("id") Integer id);

    @Update("update crazy_poker.sys_update_task set task_status = 2, execute_count = execute_count + 1, msg = #{msg} where id = #{id} and task_status = 1")
    int updateTaskSuccess(@Param("id") Integer id, @Param("msg") String msg);

    @Update("update crazy_poker.sys_update_task set task_status = 0, execute_count = execute_count + 1, msg = #{msg} where id = #{id} and task_status = 1")
    int updateTaskWait(@Param("id") Integer id, @Param("msg") String msg);

    @Update("update crazy_poker.sys_update_task set task_status = 3, msg = #{msg} where id = #{id} and task_status = 1")
    int updateTaskFail(@Param("id") Integer id, @Param("msg") String msg);

}