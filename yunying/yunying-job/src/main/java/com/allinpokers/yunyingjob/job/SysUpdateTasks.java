package com.allinpokers.yunyingjob.job;

import com.allinpokers.yunyingjob.service.SysUpdateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时处理任务
 */
@Component
@Slf4j
public class SysUpdateTasks {

    @Resource
    private SysUpdateTaskService sysUpdateTaskService;

    /**
     * 定时处理任务
     */
    @Scheduled(cron="0 */5 * * * ?  ")
    public void sysUpdateTasksJob() {
        log.info("---定时处理任务开始执行---");
        sysUpdateTaskService.sysUpdateTasksJob();
    }

}
