package com.allinpokers.yunyingjob.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunyingjob.dao.crazypoker.*;
import com.allinpokers.yunyingjob.entity.crazypoker.SysUpdateTask;
import com.allinpokers.yunyingjob.entity.crazypoker.plus.task.UserSeatLeaveTime;
import com.allinpokers.yunyingjob.service.SysUpdateTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 定时处理任务
 */
@Service
@Slf4j
public class SysUpdateTaskServiceImpl implements SysUpdateTaskService {

    @Resource
    private SysUpdateTaskDao sysUpdateTaskDao;

    @Resource
    private SysTaskRecordDao sysTaskRecordDao;

    @Resource
    private AvatarLibraryDao avatarLibraryDao;

    @Resource
    private UserAvatarRecordDao userAvatarRecordDao;

    @Override
    public void sysUpdateTasksJob() {
        SysUpdateTask task = sysUpdateTaskDao.getWaitingTask(1);
        if (task != null) {
            log.info("---修复入房旧数据--===> taskId： {}", task.getId());
            int updateCount = sysUpdateTaskDao.updateTaskRunning(task.getId());
            if (updateCount == 1) {
                // 锁定任务
                // 获取本次要修复的数据
                sysTaskRecordDao.insertTaskRecord(task.getId(), 1, 0, "开始执行修正入房信息");
                int hasBringIn = 1;
                int limit = 1000;
                if (!StringUtils.isEmpty(task.getParams())) {
                    JSONObject jsonObject = JSONObject.parseObject(task.getParams());
                    hasBringIn = jsonObject.containsKey("hasBringIn") ? jsonObject.getInteger("hasBringIn") : 1;
                    limit = jsonObject.containsKey("limit") ? jsonObject.getInteger("limit") : 1000;
                }
                int count = sysUpdateTaskDao.countUserJoinNeedUpdate(hasBringIn);
                List<UserSeatLeaveTime> seatLeaveTimeList = sysUpdateTaskDao.getSeatTimeList(hasBringIn, limit);
                if (seatLeaveTimeList.size() > 0) {
                    int sum = seatLeaveTimeList.stream().mapToInt(seat -> sysUpdateTaskDao.updateUserJoinRoomSeatTime(seat.getUserId(), seat.getRoomId(), seat.getFirstSeatTime(), seat.getLastSeatTime(), seat.getLastLeaveTime() != null ? seat.getLastLeaveTime() : seat.getUpdatedTime())).sum();
                    if (sum > 0) {
                        sysTaskRecordDao.insertTaskRecord(task.getId(), 2, sum, "执行修正入房信息结束");
                    } else {
                        sysTaskRecordDao.insertTaskRecord(task.getId(), 3, sum, "执行修正入房信息结束");
                    }
                    if (count <= sum) {
                        // 状态 任务已完成
                        sysUpdateTaskDao.updateTaskSuccess(task.getId(), "修正入房信息任务已完成");
                    } else {
                        // 状态 待处理
                        sysUpdateTaskDao.updateTaskWait(task.getId(), "修正入房信息任务已处理部分");
                    }
                } else {
                    // 状态 任务已完成
                    sysUpdateTaskDao.updateTaskSuccess(task.getId(), "修正入房信息任务已完成");
                    sysTaskRecordDao.insertTaskRecord(task.getId(), 2, 0, "执行修正入房信息结束,没有可执行数据");
                }
            }
        }
    }
}
