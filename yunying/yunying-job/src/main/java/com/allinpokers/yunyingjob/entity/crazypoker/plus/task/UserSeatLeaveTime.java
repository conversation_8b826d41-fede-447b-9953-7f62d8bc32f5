package com.allinpokers.yunyingjob.entity.crazypoker.plus.task;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserSeatLeaveTime {
    @ApiModelProperty(value = "用户id")
    private Integer userId;
    @ApiModelProperty(value = "房间id")
    private Integer roomId;
    @ApiModelProperty(value = "房间用户id")
    private String roomUserId;
    @ApiModelProperty(value = "首次上桌时间")
    private LocalDateTime firstSeatTime;
    @ApiModelProperty(value = "最后一次上桌时间")
    private LocalDateTime lastSeatTime;
    @ApiModelProperty(value = "离桌时间")
    private LocalDateTime lastLeaveTime;
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

}
