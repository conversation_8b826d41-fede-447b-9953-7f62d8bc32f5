package com.allinpokers.yunyingjob.dao.crazypoker;

import org.apache.ibatis.annotations.*;

/**
 * 定时处理任务记录  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SysTaskRecordDao {

    @Insert("insert into crazy_poker.sys_task_record (sys_task_id, task_status, execute_num, msg) " +
            "values (#{taskId}, #{taskStatus}, #{executeNum}, #{msg})")
    int insertTaskRecord(@Param("taskId") Integer taskId, @Param("taskStatus") Integer taskStatus, @Param("executeNum") Integer executeNum, @Param("msg") String msg);
}