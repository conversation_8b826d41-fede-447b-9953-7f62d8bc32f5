package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.redis.RedisLockConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * RedisLockConfigDao
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
@Mapper
public interface RedisLockConfigDao {


    @Select({
            "select expire_time,time_unit,retry_count,retry_interval_ms from redis_lock_config where code = #{code} limit 1"
    })
    RedisLockConfig findConfig(@Param("code") String code);

}
