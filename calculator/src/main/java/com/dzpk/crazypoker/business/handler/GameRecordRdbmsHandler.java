package com.dzpk.crazypoker.business.handler;


import com.dzpk.crazypoker.business.config.MongoCollections;
import com.dzpk.crazypoker.business.handler.bean.GameRecord;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataDetail;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataProfit;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataRoom;
import com.dzpk.crazypoker.business.repositories.mysql.GameRecordRdbmsDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * GameRecordRdbmsHandler
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@Slf4j
@Service
public class GameRecordRdbmsHandler {

    @Resource
    private GameRecordRdbmsDao gameRecordRdbmsDao;

    @Resource
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Transactional
    public Boolean handle(GameRecord param) {
        try {
            checkParams(param);
            // game_data_room
            syncGameDataRoom(param.getRoomId());
            // game_data_detail
            syncGameDataDetail(param.getRoomId());
            // game_data_profit
            syncGameDataProfit(param.getRoomId());
            return true;
        } catch (Exception e) {
            log.error("[GameRecordRdbms] Unexpected error, param: {}", param, e);
            return false;
        }
    }

    /**
     * 校验参数
     * @param message
     */
    private void checkParams(GameRecord message) {
        // 转成json
        Assert.notNull(message, "params is null, GAME_RECORD_RDBMS failed.");
        // 校验参数
        Assert.notNull(message.getRoomId(), "roomId is null, GAME_RECORD_RDBMS failed.");
    }

    /**
     * 同步游戏数据房间
     * @param roomId 房间ID
     */
    @Transactional
    public void syncGameDataRoom(Integer roomId) {
        // 查询房间数据
        Query query = new Query();
        query.addCriteria(Criteria.where("room_id").is(roomId));
        // 先查询
        List<GameDataRoom> gameDataRoomList = reactiveMongoTemplate.find(query, GameDataRoom.class, MongoCollections.GAME_DATA_ROOM).collectList().block();
        if (!CollectionUtils.isEmpty(gameDataRoomList)) {
            // 先删除，后插入
            gameRecordRdbmsDao.deleteGameDataRoomByRoomId(roomId);
            gameDataRoomList.forEach(gameDataRoom -> {
                try {
                    // 插入数据库
                    gameRecordRdbmsDao.insertGameDataRoom(gameDataRoom);
                    // 更新rdbms_sync_at
                    Query updateQuery = Query.query(Criteria.where("_id").is(gameDataRoom.getId()));
                    Update update = new Update().set("rdbms_sync_at", System.currentTimeMillis());
                    reactiveMongoTemplate
                            .updateFirst(updateQuery, update, GameDataRoom.class)
                            .subscribe(result -> {
                                if (result.getModifiedCount() > 0) {
                                    log.info("[GameRecordRdbms] update game_data_room success, roomId: {} userId: {}", roomId, gameDataRoom.getUserId());
                                } else {
                                    log.error("[GameRecordRdbms] update game_data_room failed, roomId: {} userId: {}", roomId, gameDataRoom.getUserId());
                                }
                            }, error -> {
                                log.error("[GameRecordRdbms] update game_data_room error, roomId: {} userId: {}", roomId, gameDataRoom.getUserId(), error);
                            });
                } catch (Exception exception) {
                    log.error("[GameRecordRdbms] Unexpected error, roomId: {} userId: {}", roomId, gameDataRoom.getUserId(), exception);
                }
            });
        }
    }

    /**
     * 同步游戏数据详情
     * @param roomId 房间ID
     */
    @Transactional
    public void syncGameDataDetail(Integer roomId) {
        // 查询房间数据
        Query query = new Query();
        query.addCriteria(Criteria.where("room_id").is(roomId));
        // 先查询
        List<GameDataDetail> gameDataDetailList = reactiveMongoTemplate.find(query, GameDataDetail.class, MongoCollections.GAME_DATA_DETAIL).collectList().block();
        if (!CollectionUtils.isEmpty(gameDataDetailList)) {
            // 先删除，后插入
            gameRecordRdbmsDao.deleteGameDataDetailByRoomId(roomId);
            gameDataDetailList.forEach(gameDataDetail -> {
                try {
                    // 插入数据库
                    gameRecordRdbmsDao.insertGameDataDetail(gameDataDetail);
                    // 更新rdbms_sync_at
                    Query updateQuery = Query.query(Criteria.where("_id").is(gameDataDetail.getId()));
                    Update update = new Update().set("rdbms_sync_at", System.currentTimeMillis());
                    reactiveMongoTemplate
                            .updateFirst(updateQuery, update, GameDataDetail.class)
                            .subscribe(result -> {
                                if (result.getModifiedCount() > 0) {
                                    log.info("[GameRecordRdbms] update game_data_detail success, roomId: {} userId: {}", roomId, gameDataDetail.getUserId());
                                } else {
                                    log.error("[GameRecordRdbms] update game_data_detail failed, roomId: {} userId: {}", roomId, gameDataDetail.getUserId());
                                }
                            }, error -> {
                                log.error("[GameRecordRdbms] update game_data_detail error, roomId: {} userId: {}", roomId, gameDataDetail.getUserId(), error);
                            });

                } catch (Exception exception) {
                    log.error("[GameRecordRdbms] Unexpected error, roomId: {} userId: {}", roomId, gameDataDetail.getUserId(), exception);
                }
            });
        }
    }

    /**
     * 同步游戏数据盈利
     * @param roomId 房间ID
     */
    @Transactional
    public void syncGameDataProfit(Integer roomId) {
        List<Integer> userIds = gameRecordRdbmsDao.selectUserIdByRoomId(roomId);
        if (!CollectionUtils.isEmpty(userIds)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("user_id").in(userIds));
            // 先查询
            List<GameDataProfit> gameDataProfitList = reactiveMongoTemplate.find(query, GameDataProfit.class, MongoCollections.GAME_DATA_PROFIT).collectList().block();
            if (!CollectionUtils.isEmpty(gameDataProfitList)) {
                // 先删除，后插入
                for (GameDataProfit gameDataProfit : gameDataProfitList) {
                    try {
                        // 先删除
                        gameRecordRdbmsDao.deleteGameDataProfitByUserId(gameDataProfit.getUserId());
                        // 插入数据库
                        gameRecordRdbmsDao.insertGameDataProfit(gameDataProfit);
                        // 更新rdbms_sync_at
                        Query updateQuery = Query.query(Criteria.where("_id").is(gameDataProfit.getId()));
                        Update update = new Update().set("rdbms_sync_at", System.currentTimeMillis());
                        reactiveMongoTemplate
                                .updateFirst(updateQuery, update, GameDataProfit.class)
                                .subscribe(result -> {
                                    if (result.getModifiedCount() > 0) {
                                        log.info("[GameRecordRdbms] update game_data_profit success, roomId: {} userId: {}", roomId, gameDataProfit.getUserId());
                                    } else {
                                        log.error("[GameRecordRdbms] update game_data_profit failed, roomId: {} userId: {}", roomId, gameDataProfit.getUserId());
                                    }
                                }, error -> {
                                    log.error("[GameRecordRdbms] update game_data_profit error, roomId: {} userId: {}", roomId, gameDataProfit.getUserId(), error);
                                });

                    } catch (Exception exception) {
                        log.error("[GameRecordRdbms] Unexpected error, roomId: {} userId: {}", roomId, gameDataProfit.getUserId(), exception);
                    }
                }
            }
        }
    }


}
