package com.dzpk.crazypoker.business.start;


import com.dzpk.crazypoker.business.config.SingleTaskStartLockStatus;
import com.dzpk.crazypoker.business.handler.bean.SingleTaskStartLock;
import com.dzpk.crazypoker.business.repositories.mysql.SingleTaskStartLockDao;
import com.dzpk.crazypoker.business.task.BatchCoinsRecyclingTask;
import com.dzpk.crazypoker.business.util.DateTimeFormatUtils;
import com.dzpk.crazypoker.business.util.LocalIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * BatchCoinsRecyclingTaskStart
 *
 * <AUTHOR>
 * @since 2025/6/3
 */
@Slf4j
@Component
public class BatchCoinsRecyclingTaskStart {

    /**
     * 批量发放回收币
     */
    private static final String BATCH_COINS_RECYCLING_TASK_KEY = "BATCH_COINS_RECYCLING_TASK";

    private final Environment environment;

    public BatchCoinsRecyclingTaskStart(Environment environment) {
        this.environment = environment;
    }

    @Resource
    SingleTaskStartLockDao singleTaskStartLockDao;

    @Resource
    BatchCoinsRecyclingTask batchCoinsRecyclingTask;

    @PostConstruct
    public void start() {
        SingleTaskStartLock singleTaskStartLock = SingleTaskStartLock.builder()
                .taskKey(BATCH_COINS_RECYCLING_TASK_KEY)
                .build();
        try {
            // 获取IP地址
            String ip = LocalIpUtils.getLocalIP();
            // 获取端口号（处理默认值8080）
            String port = environment.getProperty("server.port", "8080");
            // 获取应用名称
            String appName = environment.getProperty("spring.application.name", "未命名应用");
            log.info("应用启动信息 - IP: {}, 端口: {}, 应用名称: {}", ip, port, appName);

            // 直接插入
            String uniqueId = generateTaskUniqueId();

            singleTaskStartLock.setIp(ip);
            singleTaskStartLock.setPort(Integer.parseInt(port));
            singleTaskStartLock.setServiceName(appName);
            singleTaskStartLock.setUniqueId(uniqueId);

            // 插入单任务启动锁，同一分钟内只有一个实例可以执行
            singleTaskStartLockDao.insert(singleTaskStartLock);

            try {
                // 创建检查配置的定时任务
                batchCoinsRecyclingTask.createCheckConfigTask();
                // 更新状态
                singleTaskStartLockDao.updateStatus(SingleTaskStartLock.builder()
                        .status(SingleTaskStartLockStatus.START_SUCCESS)
                        .uniqueId(uniqueId)
                        .build());
            } catch (Exception e) {
                log.error("初始化任务失败：key={} , message:{}", BATCH_COINS_RECYCLING_TASK_KEY, e.getMessage(), e);
                // 更新状态
                singleTaskStartLockDao.updateStatus(SingleTaskStartLock.builder()
                        .status(SingleTaskStartLockStatus.START_FAIL)
                        .uniqueId(uniqueId)
                        .build());
            }

        } catch (Exception e) {
            singleTaskStartLock.setUniqueId(generateTaskUniqueId()+":error:"+System.currentTimeMillis());
            singleTaskStartLock.setStatus(SingleTaskStartLockStatus.START_FAIL);
            singleTaskStartLockDao.insertFull(singleTaskStartLock);
            log.error("初始化任务失败：key={} , message:{}", BATCH_COINS_RECYCLING_TASK_KEY, e.getMessage(), e);
        }
    }

    /**
     * 生成唯一任务ID
     * @return 唯一任务ID
     */
    private String generateTaskUniqueId() {
        return BATCH_COINS_RECYCLING_TASK_KEY + ":" + DateTimeFormatUtils.formatShortMinute(System.currentTimeMillis());
    }

}
