package com.dzpk.crazypoker.business.handler.bean.mongo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * UserTribeGameDataDaily
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "user_tribe_game_data_daily")
public class UserTribeGameDataDaily {

    @Id
    @Field("_id")
    private ObjectId id;

    @Field("af_rate")
    private Double afRate;

    @Field("ai")
    private Boolean ai;

    @Field("allin_hand")
    private Integer allinHand;

    @Field("allin_win_hand")
    private Integer allinWinHand;

    @Field("allin_win_rate")
    private Double allinWinRate;

    @Field("at_id")
    private Long atId;

    @Field("avg_bring_in")
    private Double avgBringIn;

    @Field("avg_earn")
    private Double avgEarn;

    @Field("bring_in")
    private Long bringIn;

    @Field("call_hand")
    private Integer callHand;

    @Field("call_rate")
    private Double callRate;

    @Field("cbet_hand")
    private Integer cbetHand;

    @Field("cbet_rate")
    private Double cbetRate;

    @Field("game_cnt")
    private Integer gameCnt;

    @Field("nickname")
    private String nickname;

    @Field("pfr_hand")
    private Integer pfrHand;

    @Field("pfr_rate")
    private Double pfrRate;

    @Field("pool_hand")
    private Integer poolHand;

    @Field("pool_rate")
    private Double poolRate;

    @Field("pool_win_hand")
    private Integer poolWinHand;

    @Field("pool_win_rate")
    private Double poolWinRate;

    @Field("raise_hand")
    private Integer raiseHand;

    @Field("raise_rate")
    private Double raiseRate;

    @Field("showdown_hand")
    private Integer showdownHand;

    @Field("showdown_rate")
    private Double showdownRate;

    @Field("showdown_win_hand")
    private Integer showdownWinHand;

    @Field("showdown_win_rate")
    private Double showdownWinRate;

    @Field("tbet_hand")
    private Integer tbetHand;

    @Field("tbet_rate")
    private Double tbetRate;

    @Field("total_earn")
    private Long totalEarn;

    @Field("total_hand")
    private Integer totalHand;

    @Field("tribe_id")
    private Integer tribeId;

    @Field("tribe_name")
    private String tribeName;

    @Field("tribe_random_id")
    private Integer tribeRandomId;

    @Field("user_id")
    private Integer userId;

    @Field("user_random_num")
    private String userRandomNum;

    @Field("win_hand")
    private Integer winHand;

    @Field("win_rate")
    private Double winRate;

    @Field("last_sync_at")
    private Long lastSyncAt;

    @Field("last_sync_day")
    private Integer lastSyncDay;

    @Field("created_at")
    private Long createdAt;

    @Field("updated_at")
    private Long updatedAt;

}
