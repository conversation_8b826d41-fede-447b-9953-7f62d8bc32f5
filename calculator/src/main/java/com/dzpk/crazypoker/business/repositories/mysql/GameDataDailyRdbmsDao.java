package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.mongo.UserGameDataDaily;
import com.dzpk.crazypoker.business.handler.bean.mongo.UserTribeGameDataDaily;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * GameDataDailyRdbmsDao
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Mapper
public interface GameDataDailyRdbmsDao {

    @Delete("delete from user_game_data_daily where last_sync_day = #{lastSyncDay}")
    void deleteUserGameDataDailyByLastSyncDay(@Param("lastSyncDay") Integer lastSyncDay);

    @Delete("delete from user_tribe_game_data_daily where last_sync_day = #{lastSyncDay}")
    void deleteUserTribeGameDataDailyByLastSyncDay(@Param("lastSyncDay") Integer lastSyncDay);

    @Insert({
            "INSERT INTO user_tribe_game_data_daily (",
            "af_rate, ai, allin_hand, allin_win_hand, allin_win_rate, at_id, avg_bring_in, avg_earn,",
            "bring_in, call_hand, call_rate, cbet_hand, cbet_rate, game_cnt, nickname, pfr_hand,",
            "pfr_rate, pool_hand, pool_rate, pool_win_hand, pool_win_rate, raise_hand, raise_rate,",
            "showdown_hand, showdown_rate, showdown_win_hand, showdown_win_rate, tbet_hand, tbet_rate,",
            "total_earn, total_hand, tribe_id, tribe_name, tribe_random_id, user_id, user_random_num,",
            "win_hand, win_rate, last_sync_day",
            ") VALUES (",
            "#{afRate}, #{ai}, #{allinHand}, #{allinWinHand}, #{allinWinRate}, #{atId}, #{avgBringIn}, #{avgEarn},",
            "#{bringIn}, #{callHand}, #{callRate}, #{cbetHand}, #{cbetRate}, #{gameCnt}, #{nickname}, #{pfrHand},",
            "#{pfrRate}, #{poolHand}, #{poolRate}, #{poolWinHand}, #{poolWinRate}, #{raiseHand}, #{raiseRate},",
            "#{showdownHand}, #{showdownRate}, #{showdownWinHand}, #{showdownWinRate}, #{tbetHand}, #{tbetRate},",
            "#{totalEarn}, #{totalHand}, #{tribeId}, #{tribeName}, #{tribeRandomId}, #{userId}, #{userRandomNum},",
            "#{winHand}, #{winRate}, #{lastSyncDay}",
            ")"
    })
    void insertUserTribeGameDataDaily(UserTribeGameDataDaily data);

    @Insert({
            "INSERT INTO user_game_data_daily (",
            "af_rate, ai, allin_hand, allin_win_hand, allin_win_rate, at_id, avg_bring_in, avg_earn,",
            "bring_in, call_hand, call_rate, cbet_hand, cbet_rate, game_cnt, nickname, pfr_hand,",
            "pfr_rate, pool_hand, pool_rate, pool_win_hand, pool_win_rate, raise_hand, raise_rate,",
            "showdown_hand, showdown_rate, showdown_win_hand, showdown_win_rate, tbet_hand, tbet_rate,",
            "total_earn, total_hand, user_id, user_random_num,",
            "win_hand, win_rate, last_sync_day",
            ") VALUES (",
            "#{afRate}, #{ai}, #{allinHand}, #{allinWinHand}, #{allinWinRate}, #{atId}, #{avgBringIn}, #{avgEarn},",
            "#{bringIn}, #{callHand}, #{callRate}, #{cbetHand}, #{cbetRate}, #{gameCnt}, #{nickname}, #{pfrHand},",
            "#{pfrRate}, #{poolHand}, #{poolRate}, #{poolWinHand}, #{poolWinRate}, #{raiseHand}, #{raiseRate},",
            "#{showdownHand}, #{showdownRate}, #{showdownWinHand}, #{showdownWinRate}, #{tbetHand}, #{tbetRate},",
            "#{totalEarn}, #{totalHand}, #{userId}, #{userRandomNum},",
            "#{winHand}, #{winRate}, #{lastSyncDay}",
            ")"
    })
    void insertUserGameDataDaily(UserGameDataDaily data);

}
