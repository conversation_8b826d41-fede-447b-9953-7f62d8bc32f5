package com.dzpk.crazypoker.business.redis;


import com.dzpk.crazypoker.business.repositories.mysql.RedisLockConfigDao;
import com.dzpk.crazypoker.business.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.TimeUnit;

/**
 * RedisDistributedLockGenerator
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
@Slf4j

public class RedisDistributedLockGenerator {

    /**
     * 查询配置
     * @param code 配置代码
     * @return RedisLockConfig
     */
    private static RedisLockConfig findConfig(String code) {
        try {
            RedisLockConfigDao redisLockConfigDao = SpringContextUtils.getBean(RedisLockConfigDao.class);
            return redisLockConfigDao.findConfig(code);
        } catch (Exception e) {
            log.error("Failed to find Redis lock config for code: {}", code, e);
        }
        return null;
    }

    /**
     * 生成锁
     * @param code
     * @param lockKey
     * @return
     */
    public static RedisDistributedLock generate(String code, String lockKey) {
        RedisLockConfig dbConfig = findConfig(code);
        if (dbConfig == null) {
            dbConfig = findConfig(RedisLockConfigCode.DEFAULT);
        }
        // 默认配置
        RedisDistributedLock lock = new RedisDistributedLock(
                lockKey,
                1,
                TimeUnit.SECONDS,
                1,
                1000);

        if (dbConfig != null) {
            lock = new RedisDistributedLock(
                    lockKey,
                    dbConfig.getExpireTime(),
                    TimeUnit.valueOf(dbConfig.getTimeUnit().toUpperCase()),
                    dbConfig.getRetryCount(),
                    dbConfig.getRetryIntervalMs()
            );
        }
        log.info("Generated lock: {}", lock);
        return lock;
    }





}
