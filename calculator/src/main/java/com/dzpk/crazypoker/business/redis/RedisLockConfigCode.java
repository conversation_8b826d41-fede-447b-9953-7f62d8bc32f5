package com.dzpk.crazypoker.business.redis;


/**
 * RedisLockConfig
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
public interface RedisLockConfigCode {

    String DEFAULT = "default";

    String COINS_RECYCLING_AHEAD = "coins_recycling:ahead";

    String COINS_RECYCLING_BATCH = "coins_recycling:batch";

    String COINS_RECYCLING_AHEAD_CLUB = "coins_recycling:ahead:club";

    String COINS_RECYCLING_AHEAD_USER = "coins_recycling:ahead:user";

    String COINS_RECYCLING_BATCH_CLUB = "coins_recycling:batch:club";

    String COINS_RECYCLING_BATCH_USER = "coins_recycling:batch:user";

}
