package com.dzpk.crazypoker.business.handler.bean.mongo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * GameDataRoom
 *
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "game_data_room")
public class GameDataRoom {

    @Id
    @Field("_id")
    private ObjectId id;

    @Field("ai")
    private Boolean ai;

    @Field("allin_hand")
    private Integer allinHand;

    @Field("allin_win_hand")
    private Integer allinWinHand;

    @Field("ante")
    private Integer ante;

    @Field("at_id")
    private Integer atId;

    @Field("bcbet_hand")
    private Integer bcbetHand;

    @Field("bet_hand")
    private Integer betHand;

    @Field("big_blind_hand")
    private Integer bigBlindHand;

    @Field("big_blind_win_hand")
    private Integer bigBlindWinHand;

    @Field("blind")
    private String blind;

    @Field("blind_hand")
    private Integer blindHand;

    @Field("blind_win_hand")
    private Integer blindWinHand;

    @Field("bring_in")
    private Integer bringIn;

    @Field("call_hand")
    private Integer callHand;

    @Field("cbet_hand")
    private Integer cbetHand;

    @Field("check_hand")
    private Integer checkHand;

    @Field("club_id")
    private Integer clubId;

    @Field("club_name")
    private String clubName;

    @Field("club_random_id")
    private Integer clubRandomId;

    @Field("earn")
    private Integer earn;

    @Field("flop_hand")
    private Integer flopHand;

    @Field("flop_win_hand")
    private Integer flopWinHand;

    @Field("fold_hand")
    private Integer foldHand;

    @Field("game_type")
    private Integer gameType;

    @Field("hand_first_time")
    private Long handFirstTime;

    @Field("hand_last_time")
    private Long handLastTime;

    @Field("hands")
    private String hands;

    @Field("nickname")
    private String nickname;

    @Field("pfr_hand")
    private Integer pfrHand;

    @Field("pool_hand")
    private Integer poolHand;

    @Field("pool_win_hand")
    private Integer poolWinHand;

    @Field("raise_hand")
    private Integer raiseHand;

    @Field("river_hand")
    private Integer riverHand;

    @Field("river_win_hand")
    private Integer riverWinHand;

    @Field("room_end_time")
    private Long roomEndTime;

    @Field("room_id")
    private Integer roomId;

    @Field("room_name")
    private String roomName;

    @Field("room_start_time")
    private Long roomStartTime;

    @Field("room_type")
    private Integer roomType;

    @Field("room_user_id")
    private String roomUserId;

    @Field("service_fee")
    private Integer serviceFee;

    @Field("showdown_hand")
    private Integer showdownHand;

    @Field("showdown_win_hand")
    private Integer showdownWinHand;

    @Field("small_blind_hand")
    private Integer smallBlindHand;

    @Field("small_blind_win_hand")
    private Integer smallBlindWinHand;

    @Field("superior_nickname")
    private String superiorNickname;

    @Field("superior_random_num")
    private String superiorRandomNum;

    @Field("superior_rebate_rate")
    private Double superiorRebateRate;

    @Field("superior_user_id")
    private Integer superiorUserId;

    @Field("tbet_hand")
    private Integer tbetHand;

    @Field("total_hand")
    private Integer totalHand;

    @Field("tribe_id")
    private Integer tribeId;

    @Field("tribe_name")
    private String tribeName;

    @Field("tribe_random_id")
    private Integer tribeRandomId;

    @Field("turn_hand")
    private Integer turnHand;

    @Field("turn_win_hand")
    private Integer turnWinHand;

    @Field("user_id")
    private Integer userId;

    @Field("user_random_num")
    private String userRandomNum;

    @Field("win_chips")
    private Integer winChips;

    @Field("win_hand")
    private Integer winHand;

    @Field("room_club_id")
    private Integer roomClubId;

    @Field("room_club_random_id")
    private Integer roomClubRandomId;

    @Field("room_club_name")
    private String roomClubName;

    @Field("room_tribe_id")
    private Integer roomTribeId;

    @Field("room_tribe_name")
    private String roomTribeName;

    @Field("last_sync_at")
    private Long lastSyncAt;

}
