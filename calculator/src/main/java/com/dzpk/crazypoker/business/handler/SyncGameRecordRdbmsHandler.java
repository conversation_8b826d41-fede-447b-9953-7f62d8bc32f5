package com.dzpk.crazypoker.business.handler;

import com.dzpk.crazypoker.business.config.BusinessRabbitMqConfig;
import com.dzpk.crazypoker.business.config.MongoCollections;
import com.dzpk.crazypoker.business.handler.bean.SyncGameRecordRdbms;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataDetail;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataProfit;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataRoom;
import com.dzpk.crazypoker.business.handler.bean.mongo.UserGameDataDaily;
import com.dzpk.crazypoker.business.handler.bean.mongo.UserTribeGameDataDaily;
import com.dzpk.crazypoker.business.receiver.AbstractBusinessReceiver;
import com.dzpk.crazypoker.business.redis.LockedActuator;
import com.dzpk.crazypoker.business.redis.RedisDistributedLock;
import com.dzpk.crazypoker.business.repositories.mysql.GameDataDailyRdbmsDao;
import com.dzpk.crazypoker.business.repositories.mysql.GameRecordRdbmsDao;
import com.dzpk.crazypoker.business.util.JSONObjectUtils;
import com.dzpk.crazypoker.business.util.task.TimerUtils;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
@Service
public class SyncGameRecordRdbmsHandler {

    @Resource
    private GameDataDailyRdbmsDao gameDataDailyRdbmsDao;

    @Resource
    private GameRecordRdbmsDao gameRecordRdbmsDao;

    @Resource
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * task prefix
     */
    private static final String BATCH_COINS_RECYCLING_TASK_ID = "SYNC_GAME_RECORD_RDBMS:KEY:";

    /**
     * 生成任务ID
     * @param key 任务key
     * @return 任务ID
     */
    private String generateTaskId(String key) {
        return BATCH_COINS_RECYCLING_TASK_ID + key;
    }

    /**
     * 处理消息
     * @param message 消息
     */
    public void handle(SyncGameRecordRdbms message) {
        if (message.getEnable()) {
            generateTask(message);
        } else {
            cancelTask(message);
        }
    }

    /**
     * 生成任务
     * @param message 消息
     */
    private void generateTask(SyncGameRecordRdbms message) {
        String taskId = generateTaskId(message.getKey());
        log.info("generate task id:{}", taskId);
        switch (message.getKey()) {
            case MongoCollections.GAME_DATA_ROOM:
                sync(taskId, message, GameDataRoom.class, gameRecordRdbmsDao::insertGameDataRoom);
                break;
            case MongoCollections.GAME_DATA_DETAIL:
                sync(taskId, message, GameDataDetail.class, gameRecordRdbmsDao::insertGameDataDetail);
                break;
            case MongoCollections.GAME_DATA_PROFIT:
                sync(taskId, message, GameDataProfit.class, gameRecordRdbmsDao::insertGameDataProfit);
                break;
            case MongoCollections.USER_GAME_DATA_DAILY:
                sync(taskId, message, UserGameDataDaily.class, gameDataDailyRdbmsDao::insertUserGameDataDaily);
                break;
            case MongoCollections.USER_TRIBE_GAME_DATA_DAILY:
                sync(taskId, message, UserTribeGameDataDaily.class, gameDataDailyRdbmsDao::insertUserTribeGameDataDaily);
                break;
            default:
                log.error("Unknown task key: {}", message.getKey());
        }
    }

    /**
     * 取消任务
     * @param message 消息
     */
    private void cancelTask(SyncGameRecordRdbms message) {
        String taskId = generateTaskId(message.getKey());
        log.info("cancel task id:{}", taskId);
        TimerUtils.TaskState taskState = TimerUtils.getTaskState(taskId);
        if (taskState != TimerUtils.TaskState.NOT_FOUND && taskState != TimerUtils.TaskState.RUNNING) {
            TimerUtils.cancel(taskId);
            log.info("Task removed: taskId={}", taskId);
        }
    }


    /**
     * 同步数据
     * @param taskId 任务ID
     * @param message 消息
     * @param clazz 实体类
     * @param inserter 插入方法
     * @param <T> 实体类类型
     */
    private <T> void sync(String taskId, SyncGameRecordRdbms message, Class<T> clazz, Consumer<T> inserter) {
        LockedActuator.withLock(() -> {
            try {
                // 先查询没有同步的数据
                List<T> records = findNotSyncedRecords(message.getLimit(), clazz);
                // 如果没有数据，则直接取消任务
                if (CollectionUtils.isEmpty(records)) {
                    TimerUtils.cancel(taskId);
                } else {
                    // 如果有数据，则开始插入
                    for (T record : records) {
                        try {
                            inserter.accept(record);
                            // 插入完成后更新同步时间
                            updateSyncTime(record, clazz);
                        } catch (Exception e) {
                            log.error("[SyncGameRecordRdbms] Insert or update error: {}", record, e);
                        }
                    }
                    log.info("[SyncGameRecordRdbms] sync success, size: {}, taskId: {}, message: {}", records.size(), taskId, message);
                    // 发送延迟消息，继续下一次
                    sendMessage(message);
                }
            } catch (Exception e) {
                log.error("[SyncGameRecordRdbms] Sync error, taskId: {}, message: {}", taskId, message, e);
            }
        }, new RedisDistributedLock(taskId, 1, TimeUnit.SECONDS, 1, 500));
    }

    /**
     * 查询没有同步的数据
     * @param limit 查询数量
     * @param clazz 实体类
     * @return 数据列表
     */
    private <T> List<T> findNotSyncedRecords(Integer limit, Class<T> clazz) {
        Query query = notSyncQuery(limit);
        return reactiveMongoTemplate.find(query, clazz).collectList().block();
    }

    /**
     * 查询没有同步的数据
     * @param limit 查询数量
     * @return 查询条件
     */
    private Query notSyncQuery(Integer limit) {
        Criteria criteria = new Criteria().orOperator(
                Criteria.where("rdbms_sync_at").is(null),
                Criteria.where("rdbms_sync_at").exists(false)
        );
        // 如果分页是-1，则查询所有数据
        return limit == -1 ? Query.query(criteria) : new Query(criteria).limit(limit);
    }

    /**
     * 更新同步时间
     * @param record 记录
     * @param clazz 实体类
     * @param <T> 实体类类型
     */
    private <T> void updateSyncTime(T record, Class<T> clazz) {
        ObjectId id = getId(record);
        if (id != null) {
            Query query = Query.query(Criteria.where("_id").is(id));
            Update update = new Update().set("rdbms_sync_at", System.currentTimeMillis());
            reactiveMongoTemplate.updateFirst(query, update, clazz)
                    .subscribe(result -> {
                        if (result.getModifiedCount() > 0) {
                            log.info("[SyncGameRecordRdbms] update {} success, id: {}", clazz.getSimpleName(), id);
                        } else {
                            log.error("[SyncGameRecordRdbms] update {} failed, id: {}", clazz.getSimpleName(), id);
                        }
                    }, error -> {
                        log.error("[SyncGameRecordRdbms] update {} error, id: {}", clazz.getSimpleName(), id, error);
                    });
        }
    }

    /**
     * 获取记录的ID
     * @param record 记录
     * @return ID
     * @param <T> 实体类类型
     */
    private <T> ObjectId getId(T record) {
        try {
            return (ObjectId) record.getClass().getMethod("getId").invoke(record);
        } catch (Exception e) {
            log.warn("Failed to get ID from record: {}", record, e);
            return null;
        }
    }

    /**
     * 延迟发送继续下一次
     * @param message 消息
     */
    public void sendMessage(SyncGameRecordRdbms message) {
        // 计算下一次执行的时间
        long triggerTime = System.currentTimeMillis() + message.getMinute() * 60 * 1000;
        // 延迟发送
        TimerUtils.scheduleAtTime(TimerUtils.generateTaskId(), () -> {
            try {
                rabbitTemplate.convertAndSend(
                        BusinessRabbitMqConfig.Exchange.SYNC_GAME_RECORD_RDBMS,
                        BusinessRabbitMqConfig.RoutingKey.SYNC_GAME_RECORD_RDBMS,
                        message,
                        new CorrelationData(AbstractBusinessReceiver.SNOWFLAKE_ID_GENERATOR.nextCode()));
                log.info("Message sent: {}", JSONObjectUtils.toJsonString(message));
            } catch (Exception e) {
                log.error("Message send failed: {}", JSONObjectUtils.toJsonString(message), e);
            }
        }, triggerTime);
    }
}