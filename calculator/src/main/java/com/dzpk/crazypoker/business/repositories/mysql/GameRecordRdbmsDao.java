package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataDetail;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataProfit;
import com.dzpk.crazypoker.business.handler.bean.mongo.GameDataRoom;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * GameRecordRdbmsDao
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@Mapper
public interface GameRecordRdbmsDao {

    @Delete("delete from game_data_room where room_id = #{roomId}")
    void deleteGameDataRoomByRoomId(@Param("roomId") Integer roomId);

    @Delete("delete from game_data_detail where room_id = #{roomId}")
    void deleteGameDataDetailByRoomId(@Param("roomId") Integer roomId);

    @Delete("delete from game_data_profit where user_id = #{userId}")
    void deleteGameDataProfitByUserId(@Param("userId") Integer userId);

    @Insert({
            "INSERT INTO game_data_room (",
            "ai, allin_hand, allin_win_hand, ante, at_id, bcbet_hand, bet_hand,",
            "big_blind_hand, big_blind_win_hand, blind, blind_hand, blind_win_hand,",
            "bring_in, call_hand, cbet_hand, check_hand, club_id, club_name,",
            "club_random_id, earn, flop_hand, flop_win_hand, fold_hand, game_type,",
            "hand_first_time, hand_last_time, hands, nickname, pfr_hand, pool_hand,",
            "pool_win_hand, raise_hand, river_hand, river_win_hand, room_club_id,",
            "room_club_name, room_club_random_id, room_end_time, room_id, room_name,",
            "room_start_time, room_type, room_user_id, service_fee, showdown_hand,",
            "showdown_win_hand, small_blind_hand, small_blind_win_hand, superior_nickname,",
            "superior_random_num, superior_rebate_rate, superior_user_id, tbet_hand,",
            "total_hand, tribe_id, tribe_name, tribe_random_id, turn_hand, turn_win_hand,",
            "user_id, user_random_num, win_chips, win_hand",
            ") VALUES (",
            "#{ai}, #{allinHand}, #{allinWinHand}, #{ante}, #{atId}, #{bcbetHand}, #{betHand},",
            "#{bigBlindHand}, #{bigBlindWinHand}, #{blind}, #{blindHand}, #{blindWinHand},",
            "#{bringIn}, #{callHand}, #{cbetHand}, #{checkHand}, #{clubId}, #{clubName},",
            "#{clubRandomId}, #{earn}, #{flopHand}, #{flopWinHand}, #{foldHand}, #{gameType},",
            "FROM_UNIXTIME(#{handFirstTime}), FROM_UNIXTIME(#{handLastTime}), #{hands}, #{nickname}, #{pfrHand}, #{poolHand},",
            "#{poolWinHand}, #{raiseHand}, #{riverHand}, #{riverWinHand}, #{roomClubId},",
            "#{roomClubName}, #{roomClubRandomId}, FROM_UNIXTIME(#{roomEndTime}/1000), #{roomId}, #{roomName},",
            "FROM_UNIXTIME(#{roomStartTime}/1000), #{roomType}, #{roomUserId}, #{serviceFee}, #{showdownHand},",
            "#{showdownWinHand}, #{smallBlindHand}, #{smallBlindWinHand}, #{superiorNickname},",
            "#{superiorRandomNum}, #{superiorRebateRate}, #{superiorUserId}, #{tbetHand},",
            "#{totalHand}, #{tribeId}, #{tribeName}, #{tribeRandomId}, #{turnHand}, #{turnWinHand},",
            "#{userId}, #{userRandomNum}, #{winChips}, #{winHand}",
            ")"
    })
    void insertGameDataRoom(GameDataRoom gameDataRoom);


    @Insert({
            "INSERT INTO game_data_detail (",
            "ai, ante, at_id, blind, bring_in, bring_out, club_id, club_insurance_fee_rate,",
            "club_insurance_ratio, club_insurance_share, club_lose_rebate_rate, club_name,",
            "club_random_id, club_win_rebate_rate, final_earn, game_duration, game_type,",
            "hand_first_time, hand_last_time, initial_earn, insurance_buy, insurance_income,",
            "insurance_total, leave_at, nickname, platform_fee_rate, platform_fee_rate_multiplier,",
            "platform_insurance_share, platform_service_share, pool_hand, pool_win_hand,",
            "room_club_id, room_club_name, room_club_random_id, room_end_time, room_id,",
            "room_name, room_start_time, room_type, room_user_id, service_charge, service_fee,",
            "sit_at, superior_nickname, superior_random_num, superior_rebate_rate, superior_user_id,",
            "total_hand, tribe_fee_rate, tribe_id, tribe_insurance_share, tribe_name,",
            "tribe_platform_fee_rate, tribe_platform_insurance_fee_rate, tribe_random_id,",
            "tribe_service_share, user_id, user_random_num, water_bottom_rebate, water_top_rebate,",
            "win_chips, win_hand",
            ") VALUES (",
            "#{ai}, #{ante}, #{atId}, #{blind}, #{bringIn}, #{bringOut}, #{clubId}, #{clubInsuranceFeeRate},",
            "#{clubInsuranceRatio}, #{clubInsuranceShare}, #{clubLoseRebateRate}, #{clubName},",
            "#{clubRandomId}, #{clubWinRebateRate}, #{finalEarn}, #{gameDuration}, #{gameType},",
            "FROM_UNIXTIME(#{handFirstTime}), FROM_UNIXTIME(#{handLastTime}), #{initialEarn}, #{insuranceBuy}, #{insuranceIncome},",
            "#{insuranceTotal}, FROM_UNIXTIME(#{leaveAt}), #{nickname}, #{platformFeeRate}, #{platformFeeRateMultiplier},",
            "#{platformInsuranceShare}, #{platformServiceShare}, #{poolHand}, #{poolWinHand},",
            "#{roomClubId}, #{roomClubName}, #{roomClubRandomId}, FROM_UNIXTIME(#{roomEndTime}/1000), #{roomId},",
            "#{roomName}, FROM_UNIXTIME(#{roomStartTime}/1000), #{roomType}, #{roomUserId}, #{serviceCharge}, #{serviceFee},",
            "FROM_UNIXTIME(#{sitAt}), #{superiorNickname}, #{superiorRandomNum}, #{superiorRebateRate}, #{superiorUserId},",
            "#{totalHand}, #{tribeFeeRate}, #{tribeId}, #{tribeInsuranceShare}, #{tribeName},",
            "#{tribePlatformFeeRate}, #{tribePlatformInsuranceFeeRate}, #{tribeRandomId},",
            "#{tribeServiceShare}, #{userId}, #{userRandomNum}, #{waterBottomRebate}, #{waterTopRebate},",
            "#{winChips}, #{winHand}",
            ")"
    })
    void insertGameDataDetail(GameDataDetail gameDataDetail);


    @Select("select user_id from user_integral_fee_contribution where room_id = #{roomId} group by user_id")
    List<Integer> selectUserIdByRoomId(@Param("roomId") Integer roomId);


    @Insert({
            "INSERT INTO game_data_profit (",
            "af_rate, ai, allin_hand, allin_win_hand, allin_win_rate, at_id, avg_bring_in, avg_earn,",
            "bring_in, call_hand, call_rate, cbet_hand, cbet_rate, game_cnt, gold_avg_bring_in,",
            "gold_avg_earn, gold_bring_in, gold_earn, gold_game_cnt, last_sync_day, last_sync_month_day,",
            "last_sync_week_day, month_af_rate, month_allin_hand, month_allin_win_hand, month_allin_win_rate,",
            "month_avg_bring_in, month_avg_earn, month_bring_in, month_call_hand, month_call_rate,",
            "month_cbet_hand, month_cbet_rate, month_game_cnt, month_gold_avg_bring_in, month_gold_avg_earn,",
            "month_gold_bring_in, month_gold_earn, month_gold_game_cnt, month_pfr_hand, month_pfr_rate,",
            "month_pool_hand, month_pool_rate, month_pool_win_hand, month_pool_win_rate, month_raise_hand,",
            "month_raise_rate, month_showdown_hand, month_showdown_rate, month_showdown_win_hand,",
            "month_showdown_win_rate, month_tbet_hand, month_tbet_rate, month_total_earn, month_total_hand,",
            "month_win_hand, month_win_rate, nickname, pfr_hand, pfr_rate, pool_hand, pool_rate,",
            "pool_win_hand, pool_win_rate, raise_hand, raise_rate, showdown_hand, showdown_rate,",
            "showdown_win_hand, showdown_win_rate, tbet_hand, tbet_rate, total_earn, total_hand, user_id,",
            "user_random_num, week_af_rate, week_allin_hand, week_allin_win_hand, week_allin_win_rate,",
            "week_avg_bring_in, week_avg_earn, week_bring_in, week_call_hand, week_call_rate,",
            "week_cbet_hand, week_cbet_rate, week_game_cnt, week_gold_avg_bring_in, week_gold_avg_earn,",
            "week_gold_bring_in, week_gold_earn, week_gold_game_cnt, week_pfr_hand, week_pfr_rate,",
            "week_pool_hand, week_pool_rate, week_pool_win_hand, week_pool_win_rate, week_raise_hand,",
            "week_raise_rate, week_showdown_hand, week_showdown_rate, week_showdown_win_hand,",
            "week_showdown_win_rate, week_tbet_hand, week_tbet_rate, week_total_earn, week_total_hand,",
            "week_win_hand, week_win_rate, win_hand, win_rate",
            ") VALUES (",
            "#{afRate}, #{ai}, #{allinHand}, #{allinWinHand}, #{allinWinRate}, #{atId}, #{avgBringIn}, #{avgEarn},",
            "#{bringIn}, #{callHand}, #{callRate}, #{cbetHand}, #{cbetRate}, #{gameCnt}, #{goldAvgBringIn},",
            "#{goldAvgEarn}, #{goldBringIn}, #{goldEarn}, #{goldGameCnt}, #{lastSyncDay}, #{lastSyncMonthDay},",
            "#{lastSyncWeekDay}, #{monthAfRate}, #{monthAllinHand}, #{monthAllinWinHand}, #{monthAllinWinRate},",
            "#{monthAvgBringIn}, #{monthAvgEarn}, #{monthBringIn}, #{monthCallHand}, #{monthCallRate},",
            "#{monthCbetHand}, #{monthCbetRate}, #{monthGameCnt}, #{monthGoldAvgBringIn}, #{monthGoldAvgEarn},",
            "#{monthGoldBringIn}, #{monthGoldEarn}, #{monthGoldGameCnt}, #{monthPfrHand}, #{monthPfrRate},",
            "#{monthPoolHand}, #{monthPoolRate}, #{monthPoolWinHand}, #{monthPoolWinRate}, #{monthRaiseHand},",
            "#{monthRaiseRate}, #{monthShowdownHand}, #{monthShowdownRate}, #{monthShowdownWinHand},",
            "#{monthShowdownWinRate}, #{monthTbetHand}, #{monthTbetRate}, #{monthTotalEarn}, #{monthTotalHand},",
            "#{monthWinHand}, #{monthWinRate}, #{nickname}, #{pfrHand}, #{pfrRate}, #{poolHand}, #{poolRate},",
            "#{poolWinHand}, #{poolWinRate}, #{raiseHand}, #{raiseRate}, #{showdownHand}, #{showdownRate},",
            "#{showdownWinHand}, #{showdownWinRate}, #{tbetHand}, #{tbetRate}, #{totalEarn}, #{totalHand}, #{userId},",
            "#{userRandomNum}, #{weekAfRate}, #{weekAllinHand}, #{weekAllinWinHand}, #{weekAllinWinRate},",
            "#{weekAvgBringIn}, #{weekAvgEarn}, #{weekBringIn}, #{weekCallHand}, #{weekCallRate},",
            "#{weekCbetHand}, #{weekCbetRate}, #{weekGameCnt}, #{weekGoldAvgBringIn}, #{weekGoldAvgEarn},",
            "#{weekGoldBringIn}, #{weekGoldEarn}, #{weekGoldGameCnt}, #{weekPfrHand}, #{weekPfrRate},",
            "#{weekPoolHand}, #{weekPoolRate}, #{weekPoolWinHand}, #{weekPoolWinRate}, #{weekRaiseHand},",
            "#{weekRaiseRate}, #{weekShowdownHand}, #{weekShowdownRate}, #{weekShowdownWinHand},",
            "#{weekShowdownWinRate}, #{weekTbetHand}, #{weekTbetRate}, #{weekTotalEarn}, #{weekTotalHand},",
            "#{weekWinHand}, #{weekWinRate}, #{winHand}, #{winRate}",
            ")"
    })
    void insertGameDataProfit(GameDataProfit gameDataProfit);

}
