package com.dzpk.crazypoker.business.receiver;


import com.dzpk.crazypoker.business.config.BusinessRabbitMqConfig;
import com.dzpk.crazypoker.business.handler.GameDataDailyRdbmsHandler;
import com.dzpk.crazypoker.business.handler.bean.GameDataDaily;
import com.dzpk.crazypoker.business.receiver.bean.DelayRetryConfig;
import com.dzpk.crazypoker.business.redis.LockedActuator;
import com.dzpk.crazypoker.business.redis.RedisDistributedLock;
import com.dzpk.crazypoker.business.redis.RedisLockKeyGenerator;
import com.dzpk.crazypoker.business.util.JSONObjectUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * GameDataDailyRdbmsReceiver
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Slf4j
@Component
public class GameDataDailyRdbmsReceiver extends AbstractBusinessReceiver {

    private static final DelayRetryConfig DELAY_RETRY;

    @Override
    public DelayRetryConfig getDelayRetryConfig() {
        return DELAY_RETRY;
    }

    static {
        DELAY_RETRY = DelayRetryConfig.builder()
                .maxRetryCount(20)
                .baseInterval(1500)
                .backoffStrategy(DelayRetryConfig.BackoffStrategy.EXPONENTIAL)
                .backoffFactor(1.2)
                .dataTTL(120)
                .queue(BusinessRabbitMqConfig.Queue.GAME_DATA_DAILY_RDBMS)
                .exchange(BusinessRabbitMqConfig.Exchange.GAME_DATA_DAILY_RDBMS)
                .routingKey(BusinessRabbitMqConfig.RoutingKey.GAME_DATA_DAILY_RDBMS)
                .build();
    }

    @Resource
    GameDataDailyRdbmsHandler gameDataDailyRdbmsHandler;

    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(
                            value = BusinessRabbitMqConfig.Queue.GAME_DATA_DAILY_RDBMS,
                            durable = "true"
                    ),
                    exchange = @Exchange(
                            value = BusinessRabbitMqConfig.Exchange.GAME_DATA_DAILY_RDBMS,
                            type = ExchangeTypes.TOPIC
                    ),
                    key = BusinessRabbitMqConfig.RoutingKey.GAME_DATA_DAILY_RDBMS
            ),
            concurrency = "5-10"
    )
    public void receive(
            @Payload GameDataDaily message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        try {
            log.info("GAME_DATA_DAILY_RDBMS receive message : {}", message);
            // 转成json
            Assert.notNull(message, "params is null, GAME_DATA_DAILY_RDBMS failed.");
            // 校验参数
            Assert.notNull(message.getLastSyncDay(), "lastSyncDay is null, GAME_DATA_DAILY_RDBMS failed.");

            AtomicReference<Boolean> result = new AtomicReference<>(false);

            // 加锁执行
            LockedActuator.withLock(() -> {
                // 处理消息
                result.set(gameDataDailyRdbmsHandler.handle(message));

            },  new RedisDistributedLock(
                    RedisLockKeyGenerator.generateGameDataDailyRdbmsLock(message.getLastSyncDay()),
                    5,
                    TimeUnit.SECONDS,
                    3,
                    500));

            if (result.get()) {
                log.info("GAME_DATA_DAILY_RDBMS success, params: {}", JSONObjectUtils.toJsonString(message));
                ack(channel, deliveryTag);
            } else {
                log.error("GAME_DATA_DAILY_RDBMS failed, params: {}", JSONObjectUtils.toJsonString(message));
                errorRetry(channel, deliveryTag, "GAME_DATA_DAILY_RDBMS failed.", message, generateRetryId(message));
            }

        } catch (Exception e) {
            errorAck(channel, deliveryTag, e.getMessage());
        }
    }

    /**
     * 生成重试ID
     * @param message 参数
     * @return 重试ID
     */
    private String generateRetryId(GameDataDaily message) {
        // retry:message:GAME_DATA_DAILY_RDBMS:lastSyncDay
        return String.format("retry:message:GAME_DATA_DAILY_RDBMS:%s", message.getLastSyncDay());
    }

}
