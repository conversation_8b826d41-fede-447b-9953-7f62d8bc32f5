package com.dzpk.crazypoker.business.handler.bean;


import lombok.*;

import java.sql.Timestamp;

/**
 * SingleTaskStartLock
 *
 * <AUTHOR> @since 2025/6/3
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class SingleTaskStartLock {
    /**
     * ID
     */
    private Long id;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 任务key
     */
    private String taskKey;

    /**
     * 唯一ID，格式:task_key:yyyymmhhmm
     */
    private String uniqueId;

    /**
     * 创建时间
     */
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    private Timestamp updatedAt;
}
