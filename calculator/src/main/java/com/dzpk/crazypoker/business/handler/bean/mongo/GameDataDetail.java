package com.dzpk.crazypoker.business.handler.bean.mongo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * GameDataDetail
 *
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "game_data_detail")
public class GameDataDetail {

    @Id
    @Field("_id")
    private ObjectId id;

    @Field("ai")
    private Boolean ai;

    @Field("at_id")
    private Integer atId;

    @Field("room_user_id")
    private String roomUserId;

    @Field("user_id")
    private Integer userId;

    @Field("user_random_num")
    private String userRandomNum;

    @Field("nickname")
    private String nickname;

    @Field("room_id")
    private Integer roomId;

    @Field("room_name")
    private String roomName;

    @Field("game_type")
    private Integer gameType;

    @Field("start_hand")
    private Integer startHand;

    @Field("end_hand")
    private Integer endHand;

    @Field("sit_at")
    private Long sitAt;

    @Field("leave_at")
    private Long leaveAt;

    @Field("game_duration")
    private Integer gameDuration;

    @Field("club_id")
    private Integer clubId;

    @Field("club_random_id")
    private Integer clubRandomId;

    @Field("club_name")
    private String clubName;

    @Field("tribe_id")
    private Integer tribeId;

    @Field("tribe_random_id")
    private Integer tribeRandomId;

    @Field("tribe_name")
    private String tribeName;

    @Field("ante")
    private Integer ante;

    @Field("blind")
    private String blind;

    @Field("total_hand")
    private Integer totalHand;

    @Field("pool_hand")
    private Integer poolHand;

    @Field("rebate_rate")
    private Integer rebateRate;

    @Field("bring_in")
    private Long bringIn;

    @Field("initial_earn")
    private Long initialEarn;

    @Field("bring_out")
    private Long bringOut;

    @Field("final_earn")
    private Long finalEarn;

    @Field("service_fee")
    private Long serviceFee;

    @Field("room_type")
    private Integer roomType;

    @Field("platform_service_share")
    private Long platformServiceShare;

    @Field("club_service_share")
    private Long clubServiceShare;

    @Field("insurance_buy")
    private Long insuranceBuy;

    @Field("insurance_income")
    private Long insuranceIncome;

    @Field("insurance_total")
    private Long insuranceTotal;

    @Field("platform_insurance_share")
    private Integer platformInsuranceShare;

    @Field("club_insurance_share")
    private Long clubInsuranceShare;

    @Field("tribe_insurance_share")
    private Long tribeInsuranceShare;

    @Field("water_top_rebate")
    private Long waterTopRebate;

    @Field("water_bottom_rebate")
    private Long waterBottomRebate;

    @Field("last_sync_at")
    private Long lastSyncAt;

    @Field("superior_random_num")
    private String superiorRandomNum;

    @Field("room_start_time")
    private Long roomStartTime;

    @Field("room_end_time")
    private Long roomEndTime;

    @Field("room_club_id")
    private Integer roomClubId;

    @Field("room_club_random_id")
    private Integer roomClubRandomId;

    @Field("room_club_name")
    private String roomClubName;

    @Field("room_tribe_id")
    private Integer roomTribeId;

    @Field("room_tribe_name")
    private String roomTribeName;

    @Field("club_insurance_fee_rate")
    private Double clubInsuranceFeeRate;

    @Field("club_insurance_ratio")
    private Double clubInsuranceRatio;

    @Field("club_lose_rebate_rate")
    private Double clubLoseRebateRate;

    @Field("club_win_rebate_rate")
    private Double clubWinRebateRate;

    @Field("platform_fee_rate")
    private Double platformFeeRate;

    @Field("platform_fee_rate_multiplier")
    private Integer platformFeeRateMultiplier;

    @Field("superior_rebate_rate")
    private Double superiorRebateRate;

    @Field("tribe_fee_rate")
    private Double tribeFeeRate;

    @Field("tribe_platform_fee_rate")
    private Double tribePlatformFeeRate;

    @Field("tribe_platform_insurance_fee_rate")
    private Double tribePlatformInsuranceFeeRate;

    @Field("service_charge")
    private Double serviceCharge;

    @Field("hand_first_time")
    private Long handFirstTime;

    @Field("hand_last_time")
    private Long handLastTime;

    @Field("superior_nickname")
    private String superiorNickname;

    @Field("superior_user_id")
    private Integer superiorUserId;

    @Field("win_chips")
    private Long winChips;

    @Field("win_hand")
    private Integer winHand;

    @Field("pool_win_hand")
    private Integer poolWinHand;

    @Field("tribe_service_share")
    private Long tribeServiceShare;


}
