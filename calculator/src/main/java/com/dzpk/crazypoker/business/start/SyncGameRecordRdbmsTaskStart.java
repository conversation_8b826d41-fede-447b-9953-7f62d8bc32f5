package com.dzpk.crazypoker.business.start;


import com.dzpk.crazypoker.business.config.MongoCollections;
import com.dzpk.crazypoker.business.handler.SyncGameRecordRdbmsHandler;
import com.dzpk.crazypoker.business.handler.bean.SyncGameRecordRdbms;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * SyncGameRecordRdbmsTaskStart
 *
 * <AUTHOR>
 * @since 2025/5/23
 */
//@Slf4j
//@Component
public class SyncGameRecordRdbmsTaskStart {

//    @Resource
//    SyncGameRecordRdbmsHandler syncGameRecordRdbmsHandler;
//
//    @PostConstruct
//    public void start() {
//        log.info("SyncGameRecordRdbmsTaskStart start...");
//        // 直接发送MQ启动任务
//        syncGameRecordRdbmsHandler.sendMessage(SyncGameRecordRdbms.builder()
//                .key(MongoCollections.GAME_DATA_ROOM)
//                .enable(true)
//                .limit(2000)
//                .minute(6)
//                .build());
//        syncGameRecordRdbmsHandler.sendMessage(SyncGameRecordRdbms.builder()
//                .key(MongoCollections.GAME_DATA_DETAIL)
//                .enable(true)
//                .limit(2000)
//                .minute(6)
//                .build());
//        syncGameRecordRdbmsHandler.sendMessage(SyncGameRecordRdbms.builder()
//                .key(MongoCollections.GAME_DATA_PROFIT)
//                .enable(true)
//                .limit(-1)
//                .minute(10)
//                .build());
//        syncGameRecordRdbmsHandler.sendMessage(SyncGameRecordRdbms.builder()
//                .key(MongoCollections.USER_GAME_DATA_DAILY)
//                .enable(true)
//                .limit(2000)
//                .minute(5)
//                .build());
//        syncGameRecordRdbmsHandler.sendMessage(SyncGameRecordRdbms.builder()
//                .key(MongoCollections.USER_TRIBE_GAME_DATA_DAILY)
//                .enable(true)
//                .limit(2000)
//                .minute(5)
//                .build());
//
//        log.info("SyncGameRecordRdbmsTaskStart end...");
//    }

}
