package com.dzpk.crazypoker.business.handler;


import com.dzpk.crazypoker.business.config.MongoCollections;
import com.dzpk.crazypoker.business.handler.bean.GameDataDaily;
import com.dzpk.crazypoker.business.handler.bean.mongo.UserGameDataDaily;
import com.dzpk.crazypoker.business.handler.bean.mongo.UserTribeGameDataDaily;
import com.dzpk.crazypoker.business.repositories.mysql.GameDataDailyRdbmsDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * GameDataDailyRdbmsHandler
 *
 * <AUTHOR>
 * @since 2025/5/19
 */
@Slf4j
@Service
public class GameDataDailyRdbmsHandler {

    @Resource
    private GameDataDailyRdbmsDao gameDataDailyRdbmsDao;

    @Resource
    private ReactiveMongoTemplate reactiveMongoTemplate;

    @Transactional
    public Boolean handle(GameDataDaily param) {
        try {
            checkParams(param);
            syncUserGameDataDaily(param.getLastSyncDay());
            syncUserTribeGameDataDaily(param.getLastSyncDay());
            return true;
        } catch (Exception e) {
            log.error("[GameDataDailyRdbms] Unexpected error, param: {}", param, e);
            return false;
        }
    }

    /**
     * 校验参数
     * @param message
     */
    private void checkParams(GameDataDaily message) {
        // 转成json
        Assert.notNull(message, "params is null, GAME_DATA_DAILY_RDBMS failed.");
        // 校验参数
        Assert.notNull(message.getLastSyncDay(), "lastSyncDay is null, GAME_DATA_DAILY_RDBMS failed.");
    }

    @Transactional
    public void syncUserGameDataDaily(Integer lastSyncDay) {
        // 查询房间数据
        Query query = new Query();
        query.addCriteria(Criteria.where("last_sync_day").is(lastSyncDay));
        List<UserGameDataDaily> userGameDataDailyList = reactiveMongoTemplate.find(query, UserGameDataDaily.class, MongoCollections.USER_GAME_DATA_DAILY).collectList().block();

        if (!CollectionUtils.isEmpty(userGameDataDailyList)) {
            // 先删除，后插入
            gameDataDailyRdbmsDao.deleteUserGameDataDailyByLastSyncDay(lastSyncDay);
            userGameDataDailyList.forEach(userGameDataDaily -> {
                // 插入数据库
                gameDataDailyRdbmsDao.insertUserGameDataDaily(userGameDataDaily);
                // 更新rdbms_sync_at
                Query updateQuery = Query.query(Criteria.where("_id").is(userGameDataDaily.getId()));
                Update update = new Update().set("rdbms_sync_at", System.currentTimeMillis());
                reactiveMongoTemplate
                        .updateFirst(updateQuery, update, UserGameDataDaily.class)
                        .subscribe(result -> {
                            if (result.getModifiedCount() > 0) {
                                log.info("[GameDataDailyRdbms] update user_game_data_daily success, lastSyncDay: {} userId: {}", lastSyncDay, userGameDataDaily.getUserId());
                            } else {
                                log.error("[GameDataDailyRdbms] update user_game_data_daily failed, lastSyncDay: {} userId: {}", lastSyncDay, userGameDataDaily.getUserId());
                            }
                        }, error -> {
                            log.error("[GameDataDailyRdbms] update user_game_data_daily error, lastSyncDay: {} userId: {}", lastSyncDay, userGameDataDaily.getUserId(), error);
                        });
            });
        }
    }

    @Transactional
    public void syncUserTribeGameDataDaily(Integer lastSyncDay) {
        // 查询房间数据
        Query query = new Query();
        query.addCriteria(Criteria.where("last_sync_day").is(lastSyncDay));
        List<UserTribeGameDataDaily> userTribeGameDataDailyList = reactiveMongoTemplate.find(query, UserTribeGameDataDaily.class, MongoCollections.USER_TRIBE_GAME_DATA_DAILY).collectList().block();

        if (!CollectionUtils.isEmpty(userTribeGameDataDailyList)) {
            // 先删除，后插入
            gameDataDailyRdbmsDao.deleteUserTribeGameDataDailyByLastSyncDay(lastSyncDay);
            userTribeGameDataDailyList.forEach(userTribeGameDataDaily -> {
                // 插入数据库
                gameDataDailyRdbmsDao.insertUserTribeGameDataDaily(userTribeGameDataDaily);
                // 更新rdbms_sync_at
                Query updateQuery = Query.query(Criteria.where("_id").is(userTribeGameDataDaily.getId()));
                Update update = new Update().set("rdbms_sync_at", System.currentTimeMillis());
                reactiveMongoTemplate
                        .updateFirst(updateQuery, update, UserTribeGameDataDaily.class)
                        .subscribe(result -> {
                            if (result.getModifiedCount() > 0) {
                                log.info("[GameDataDailyRdbms] update user_tribe_game_data_daily success, lastSyncDay: {} userId: {}", lastSyncDay, userTribeGameDataDaily.getUserId());
                            } else {
                                log.error("[GameDataDailyRdbms] update user_tribe_game_data_daily failed, lastSyncDay: {} userId: {}", lastSyncDay, userTribeGameDataDaily.getUserId());
                            }
                        }, error -> {
                            log.error("[GameDataDailyRdbms] update user_tribe_game_data_daily error, lastSyncDay: {} userId: {}", lastSyncDay, userTribeGameDataDaily.getUserId(), error);
                        });
            });
        }
    }

}
