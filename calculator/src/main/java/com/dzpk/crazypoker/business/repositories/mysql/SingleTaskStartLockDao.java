package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.SingleTaskStartLock;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * SingleTaskStartLockDao
 *
 * <AUTHOR>
 * @since 2025/6/3
 */
@Mapper
public interface SingleTaskStartLockDao {

    @Insert("INSERT INTO crazy_poker.single_task_start_lock " +
            "(ip, port, service_name, task_key, unique_id) " +
            "VALUES (#{ip}, #{port}, #{serviceName}, #{taskKey}, #{uniqueId})")
    void insert(SingleTaskStartLock singleTaskStartLock);

    @Insert("INSERT INTO crazy_poker.single_task_start_lock " +
            "(ip, port, service_name, status, task_key, unique_id) " +
            "VALUES (#{ip}, #{port}, #{serviceName}, #{status}, #{taskKey}, #{uniqueId})")
    void insertFull(SingleTaskStartLock singleTaskStartLock);

    @Update("UPDATE crazy_poker.single_task_start_lock " +
            "SET status = #{status} " +
            "WHERE unique_id = #{uniqueId}")
    void updateStatus(SingleTaskStartLock singleTaskStartLock);

}
