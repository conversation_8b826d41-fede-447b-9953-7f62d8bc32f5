package com.dzpk.crazypoker.business.receiver;


import com.dzpk.crazypoker.business.config.BusinessRabbitMqConfig;
import com.dzpk.crazypoker.business.handler.SyncGameRecordRdbmsHandler;
import com.dzpk.crazypoker.business.handler.bean.SyncGameRecordRdbms;
import com.dzpk.crazypoker.business.receiver.bean.DelayRetryConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * SyncGameRecordRdbmsReceiver
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Slf4j
@Component
public class SyncGameRecordRdbmsReceiver extends AbstractBusinessReceiver {

    @Override
    public DelayRetryConfig getDelayRetryConfig() {
        // ignore retry
        return null;
    }

    @Resource
    SyncGameRecordRdbmsHandler syncGameRecordRdbmsHandler;


    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(
                            value = BusinessRabbitMqConfig.Queue.SYNC_GAME_RECORD_RDBMS,
                            durable = "true"
                    ),
                    exchange = @Exchange(
                            value = BusinessRabbitMqConfig.Exchange.SYNC_GAME_RECORD_RDBMS,
                            type = ExchangeTypes.TOPIC
                    ),
                    key = BusinessRabbitMqConfig.RoutingKey.SYNC_GAME_RECORD_RDBMS
            ),
            concurrency = "5-10"
    )
    public void receive(
            @Payload SyncGameRecordRdbms message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {

        try {
            log.info("SYNC_GAME_RECORD_RDBMS receive message : {}", message);
            // 转成json
            Assert.notNull(message, "params is null, SYNC_GAME_RECORD_RDBMS failed.");
            // 校验参数
            Assert.notNull(message.getEnable(), "enable is null, SYNC_GAME_RECORD_RDBMS failed.");
            Assert.notNull(message.getMinute(), "minute is null, SYNC_GAME_RECORD_RDBMS failed.");
            Assert.notNull(message.getLimit(), "limit is null, SYNC_GAME_RECORD_RDBMS failed.");
            Assert.notNull(message.getKey(), "key is null, SYNC_GAME_RECORD_RDBMS failed.");

            // 处理
            syncGameRecordRdbmsHandler.handle(message);

        } catch (Exception e) {
            errorAck(channel, deliveryTag, e.getMessage());
        }
    }



}
