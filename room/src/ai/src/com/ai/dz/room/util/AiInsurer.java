package com.ai.dz.room.util;

import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.AiRuleTemplate;
import com.ai.dz.config.cache.IAiRuleConfigCache;
import com.ai.dz.config.constant.EInsuranceConfig;
import com.ai.dz.room.constant.AiConstant;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.insurance.Holder;
import com.dzpk.insurance.HolderPool;
import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;

import com.i366.cache.Cache;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.pocer.PocerLink;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Ai保险相关
 * Created by peterguo on 2018/10/10.
 */
public class AiInsurer {

    private static final Logger logger = LogUtil.getLogger(AiInsurer.class);

    /**
     * ai购买保险策略
     *  1）当outs数量大于10的时候，不可以购买保险。如果可以购买的情况下, 买保险的概率为40%、不买保险的概率为60%
     *  2）如果购买保险，则选择全部outs、保本 （随机买3分1 或 2分1pot，待定）
     *  3) 玩家当手如果触发了2次保险，第一次选择购买时，第二次必须强制背保（原有规则，ai玩家也需要遵守）
     *
     * @param outsArr outs列表
     * @param room
     * @param holder 投保人
     * @param minInsuranceArr 最小投保额
     * @param maxInsuranceArr 最大投保额
     * @param safeInsuranceArr 保本投保额
     * @param poolIndexArr 分池索引
     */
    public static void insurerStrategy(Integer[] outsArr, Room room, Holder holder, Integer[] minInsuranceArr, Integer[] maxInsuranceArr, Integer[] safeInsuranceArr, Integer[] poolIndexArr){

        logger.debug("mins:{},maxs:{},safes:{},poolIndex:{}",Arrays.toString(minInsuranceArr),Arrays.toString(maxInsuranceArr),Arrays.toString(safeInsuranceArr),Arrays.toString(poolIndexArr));

        int opTime = AiRuleTemplate.insurerRandomOpTime();//调用生成随机操作时间的方法
        int insuranceActiveCount = room.getInsurer().getInsuranceActiveCount();  //0转牌 1河牌
        int outNum = outsArr.length;  //out数量
        int buyInsurer = 1;  //默认购买
        int buyInsureChip = 0; //购买保险值

        RoomPersion aiRoomPersion = room.getAudMap().get(holder.getUserId());
        logger.debug("insuranceActiveCount: " + insuranceActiveCount + " getAiInsurerBuyTimes: " + aiRoomPersion.getAiInsurerBuyTimes());
        EInsuranceConfig eInsuranceConfig = null;
        if(insuranceActiveCount == 0){
            eInsuranceConfig = EInsuranceConfig.turn;
        }else{
            if(aiRoomPersion.getAiInsurerBuyTimes() == 1){ //河牌时玩家已经购买过转牌的保险
                eInsuranceConfig = EInsuranceConfig.riverButTurnPurchased;
            }else{
                eInsuranceConfig = EInsuranceConfig.river;
            }
        }
        logger.debug("eInsuranceConfig: " + eInsuranceConfig.name());

        // >0,购买额比例  <=0,不购买
        int buyInsurerRatio = AiRoomManager.canBuyInsurance(eInsuranceConfig,outNum,room.getRoomId(),holder.getUserId());
        buyInsurer = buyInsurerRatio > 0  ? 1 : 0;

        /**
         * 随机到购买,计算购买的保险值
         * 与最小购买值作比较,如果小于的话取最小购买值
         */
        if(buyInsurer == 1){
            buyInsureChip = Helper.multiplyByPercentage(maxInsuranceArr[0],buyInsurerRatio);
            logger.debug("need to calculate buyInsureChip,minInsuranceChip:{},maxInsuranceChip:{},buyInsurerRatio:{},result:{}",minInsuranceArr[0],maxInsuranceArr[0],buyInsurerRatio,buyInsureChip);
            buyInsureChip = Math.max(buyInsureChip, minInsuranceArr[0]);
        }

        logger.debug("insurance opTime: " + opTime + " ,buyInsurer: " + buyInsurer + " ,outs: " + Arrays.toString(outsArr) + " ,buyInsureChip: " + buyInsureChip);
        Map<Integer, Object> map = new HashMap<Integer, Object>();
        map.put(1, holder.getUserId());
        map.put(2, buyInsurer);         // 是否购买保险 0不买 1购买
        map.put(3, outsArr);            // out列表
        map.put(4, buyInsureChip);      // 购买保险值
        map.put(5, poolIndexArr);


        Task task = new Task(AiConstant.TASK_AI_AUTO_INSURER, map, room.getRoomId(), room.getRoomPath(), holder.getUserId()); //20241118 add userId
        WorkThreadService.submitDelayTask(room.getRoomId(), task, opTime * 1000L);
        room.roomProcedure.delayTaskMap.put(task.getId(),task);

        logger.debug("new task ai auto buy insucer !!!");
    }

    public static class PocerLinkInterceptor implements PocerLink.PocerLinkInterceptor {
        @Override
        public boolean onInsureNext(HolderPool holderPool, Room room) {
            if (!enableAdaptiveProtection()) {
                logger.debug("R-{}:{} ai 保险爆牌控制 - off", room.getRoomId(), room.getStage());
                return true;
            }
            logger.debug("R-{}:{} ai 保险爆牌控制 - on", room.getRoomId(), room.getStage());
            // 保险爆牌控制 - 如果 ai 比牌結果是 win 而且類型在指定配置中 則不發動
            for (RoomPersion rp : room.getRoomPersions()) {
                if (rp == null) continue;
                RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
                int aiType = roomPlayer.getAiPlayer().getAiOpreateType();
                IAiRuleConfigCache cache = IAiRuleConfigCache.getInstance();
                List<Integer> disableAntiBustForTypes = cache.getCommonInsuranceConfig().getDisableAntiBustForTypes();
                if (rp.getStatus() >= 1 && rp.isAiWin() && disableAntiBustForTypes.contains(aiType)) {
                    logger.debug("R-{}:{} U-{}:{} ai 比牌 win 且符合指定配置類型 {}, 保险爆牌控制不發動",
                            room.getRoomId(), room.getStage(), rp.getUserId(), rp.getUserInfo().getNikeName(),
                            disableAntiBustForTypes);
                    return false;
                }
            }
            return true;
        }

        private boolean enableAdaptiveProtection() {
            return Boolean.parseBoolean(Cache.p.getProperty("ai.insurer.adaptiveProtection", "false"));
        }
    }
}
