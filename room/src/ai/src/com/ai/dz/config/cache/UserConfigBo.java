package com.ai.dz.config.cache;

import com.ai.dz.config.constant.EAiType;
import com.ai.dz.config.constant.EUserStatus;
import com.ai.dz.config.constant.EVisitLevel;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Getter
public class UserConfigBo extends UserOptypeConfigBo {
    private EUserStatus status;
    private String blindCode;
    private List<Integer> tierIds;
    private List<String> allowRoomMatching;

    private ForceStandUpConfig forceStandUp;


    public UserConfigBo(int userId,EUserStatus status,EAiType userType,EVisitLevel visitLevel,String blindCode,List<Integer> tierIds,List<String>allowRoomMatching,ForceStandUpConfig forceStandUpConfig){
        super(userId,userType,visitLevel);
        this.status = status;
        this.blindCode = blindCode;
        this.allowRoomMatching = allowRoomMatching;
        this.forceStandUp = forceStandUpConfig;
        this.tierIds = tierIds;
    }


    @Data
    @Slf4j
    @ToString
    public static class ForceStandUpConfig implements Cloneable {
        public ForceStandUpConfig(int maxHandCnt, int maxBringInCnt, int maxBringIn, boolean enableEarningNeverLeaving, boolean enableOverNeverLeaving,boolean enableLeavingRandomHand,
                                  int standingConfigId, boolean enableHasPlayerOverLeaving,boolean enableEarningConfig,int handCntOfEarningLeaving) {
            this.maxHandCnt = maxHandCnt;
            this.maxBringInCnt = maxBringInCnt;
            this.maxBringIn = maxBringIn;
            this.enableEarningNeverLeaving = enableEarningNeverLeaving;
            this.enableOverNeverLeaving = enableOverNeverLeaving;
            this.enableLeavingRandomHand = enableLeavingRandomHand;
            this.standingConfigId = standingConfigId;
            this.enableHasPlayerOverLeaving = enableHasPlayerOverLeaving;

            this.enableEarningConfig = enableEarningConfig;
            this.handCntOfEarningLeaving = handCntOfEarningLeaving;
        }

        /*
        * 最大允许游戏手数
        * */
        private int maxHandCnt;

        /*
         * 最大允许带入次数
         * */
        private int maxBringInCnt;

        /*
         * 最大允许带入总量
         * */
        private int maxBringIn;

        /*
         * 盈利时不离桌设定
         * [PP36-FE1]若盈利达到设定手数则离桌 Deprecated
         * */
        @Deprecated
        private boolean enableEarningNeverLeaving;

        /*
         * 是否开启盈利配置
         * [PP36-FE1]若盈利达到设定手数则离桌
         * */
        private boolean enableEarningConfig;
        /*
         * 盈利时玩多几手离桌设定
         * [PP36-FE1]若盈利达到设定手数则离桌
         * */
        private int handCntOfEarningLeaving;

        /*
         * 是否开启满员不离桌
         * */
        private boolean enableOverNeverLeaving;
        /*
         * 是否开启觸發离桌设定後多玩幾手開關
         * */
        private boolean enableLeavingRandomHand;

        /*
        * 房间指定派遣配置中对应的离桌设定ID
        * */
        private int standingConfigId;
        /*
         * 是否开启有真人才触发满员离桌
         * */
        private boolean enableHasPlayerOverLeaving;


        @Override
        public ForceStandUpConfig clone() {
            try {
                return (ForceStandUpConfig) super.clone();
            } catch (CloneNotSupportedException e) {
                return null;
            }
        }

    }
}


