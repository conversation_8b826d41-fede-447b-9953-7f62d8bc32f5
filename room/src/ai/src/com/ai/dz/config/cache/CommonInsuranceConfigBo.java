package com.ai.dz.config.cache;

import lombok.Getter;
import lombok.ToString;

import java.util.List;

@Getter
@ToString
public class CommonInsuranceConfigBo {

    /**
     * 解除保險防爆的 ai types
     */
    private final List<Integer> disableAntiBustForTypes;

    public CommonInsuranceConfigBo(List<Integer> disableAntiBustForTypes) {
        this.disableAntiBustForTypes = disableAntiBustForTypes;
    }
}
