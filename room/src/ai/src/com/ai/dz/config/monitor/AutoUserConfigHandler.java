package com.ai.dz.config.monitor;

import com.ai.dz.config.cache.AutoUserTimeRangeConfigBo;
import com.ai.dz.config.cache.UserConfigBo;
import com.ai.dz.config.cache.impl.AiRuleConfigJsonCacheImpl;
import com.ai.dz.config.constant.EAiMode;
import com.ai.dz.config.constant.EAiType;
import com.ai.dz.config.constant.EUserStatus;
import com.ai.dz.config.constant.EVisitLevel;
import com.dzpk.common.utils.GsonHelper;
import com.dzpk.common.utils.Helper;
import com.dzpk.component.file.IFileChangedHandler;
import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.*;

@Slf4j
public class AutoUserConfigHandler extends AbstractFileChangedHandler
        implements IFileChangedHandler {
    private EAiMode mode;
    private String fileName;

    public String fileName(){
        return this.fileName;
    }

    public AutoUserConfigHandler(AiRuleConfigJsonCacheImpl cache){
        super(cache);
        this.mode = EAiMode.auto;
        this.fileName = String.format("%s_user_config.json",this.mode.name());
    }

    public void handle(Path filePath){
        if(null == filePath)
            return;

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                if(log.isDebugEnabled())
                    log.debug("【{}-User配置】file content is empty -> {}!",this.mode,filePath);
                return;
            }

            json = json.trim();
            Type type = new TypeToken<List<TimeRangeConfigVo>>(){}.getType();
            List<TimeRangeConfigVo> dataLst = this.parseJson(json,type);
            if(null == dataLst || dataLst.isEmpty()) {
                if(log.isDebugEnabled())
                    log.debug("【{}-User配置】json is empty -> {}!",this.mode,filePath);
                return;
            }

            // userId -> config
            Map<Integer, UserConfigBo> userConfigMap = new HashMap<>();
            // 时间段配置
            AutoUserTimeRangeConfigBo[] timeRangeArr = this.parseTimeRange(dataLst,
                    userConfigMap,mode,filePath);

             this.cache.reloadAutoUserConfig(timeRangeArr,userConfigMap);
        }catch (Exception ex){
            log.warn("Handled 【%s-User配置】failed:{} -> filePath={}",
                    this.mode,ex.getMessage(),filePath);
            if(log.isDebugEnabled())
                log.debug("",ex);
        }
    }

    private AutoUserTimeRangeConfigBo[] parseTimeRange(List<TimeRangeConfigVo> timeRangeList,
                                                           Map<Integer,UserConfigBo> userConfigMap,
                                                           EAiMode mode , Path filePath){
        AutoUserTimeRangeConfigBo[] resultArr = null;
        List<AutoUserTimeRangeConfigBo> resultLst = null;
        if(null == timeRangeList || timeRangeList.isEmpty()){
            log.warn("【{}-User配置】timeRange empty,skipped! -> {}!",
                    mode,filePath);
            return resultArr;
        }

        for(TimeRangeConfigVo time : timeRangeList) {
            Long startTime = Helper.parseLongWithHHmm(time.getStartTime(), false);
            Long endTime = Helper.parseLongWithHHmm(time.getEndTime(), false);
            if (null == startTime && null == endTime) {
                log.warn("【{}-User配置】timeRange( {}={} - {}={} ) invalid format(HH:mm),skipped! -> {}!",
                        mode,
                        time.getStartTime(), startTime,
                        time.getEndTime(),endTime,
                        filePath);
                continue;
            }

            /**
             * 06:00 ~ 14:00
             * 14:00 ~ 22:00
             * 22:00 ~ 06:00(第2天)
             */
            if (null != startTime && null != endTime &&
                    startTime == endTime) { // 00 ~ 24 ,半区间，不能相等
                log.warn("【{}-User配置】timeRange( {}={} - {}={} ) invalid:start=end,skipped! -> {}!",
                        mode,
                        time.getStartTime(), startTime,
                        time.getEndTime(),endTime,
                        filePath);
                continue;
            }

            // blindCode -> user list
            Map<String,List<UserConfigBo>> userMap = this.parseUserByBlindCode(time.getUserList(),userConfigMap,mode,
                    time.getStartTime(), startTime,
                    time.getEndTime(),endTime,filePath);
            if(log.isTraceEnabled())
                log.trace("【{}-User配置】timeRange( {}={} - {}={} ).userList: {} -> {}!",
                        mode,
                        time.getStartTime(), startTime,
                        time.getEndTime(),endTime,
                        GsonHelper.toJson(userMap,false),
                        filePath);
            if(null == userMap || userMap.isEmpty()){
                continue;
            }

            if (null == resultLst) {
                resultLst = new ArrayList<>();
            }
            if(null != startTime && null != endTime &&
                    startTime>endTime){ // 00 ~ 24
                AutoUserTimeRangeConfigBo timeRangeConfigBo = new AutoUserTimeRangeConfigBo(startTime, null, userMap);
                resultLst.add(timeRangeConfigBo);
                timeRangeConfigBo = new AutoUserTimeRangeConfigBo(null, endTime, userMap);
                resultLst.add(timeRangeConfigBo);
            }else {
                AutoUserTimeRangeConfigBo timeRangeConfigBo = new AutoUserTimeRangeConfigBo(startTime, endTime, userMap);
                resultLst.add(timeRangeConfigBo);
            }
        }

        // 排序
        if(null !=resultLst && resultLst.size()>0) {
            resultArr = new AutoUserTimeRangeConfigBo[resultLst.size()];
            resultArr = resultLst.toArray(resultArr);
            Arrays.sort(resultArr);
        }

        return resultArr;
    }
    private Map<String,List<UserConfigBo>> parseUserByBlindCode(List<PlayerConfigVo> userLst,
                                  Map<Integer,UserConfigBo> userConfigMap,
                                  EAiMode mode ,
                                  String startTimeStr, Long startTime,
                                  String endTimeStr,Long endTime,
                                  Path filePath){

        if(null == userLst || userLst.isEmpty()) {
            log.warn("【{}-User配置】timeRange( {}={} - {}={}).userList empty,skipped! -> {}!",
                    mode,
                    startTimeStr,startTime,
                    endTimeStr,endTime,
                    filePath);
            return null;
        }

        Map<String,List<UserConfigBo>> resultMap = null;
        for(int i=0;i<userLst.size();i++) {
            PlayerConfigVo user = userLst.get(i);
            if (null == user)
                continue;

            // userId
            int userId = user.getUserId();
            if(userId<=0 || userConfigMap.containsKey(userId)){
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].userId[{}] invalid or existing,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,userId,
                        filePath);
                continue;
            }

            // opType
            if(null == user.getOpType() ||
                    "".equals(user.getOpType().trim())) {
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].opType[{}] empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,user.getOpType(),
                        filePath);
                continue;
            }

            EAiType userType = EAiType.fromValue(user.getOpType().trim());
            if(null == userType){
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].opType[{}] invalid,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,user.getOpType(),
                        filePath);
                continue;
            }

            // visitLevel
            if(null == user.getVisitLevel() ||
                    "".equals(user.getVisitLevel().trim())) {
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].visitLevel[{}] empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,user.getVisitLevel(),
                        filePath);
                continue;
            }

            EVisitLevel visitLevel = EVisitLevel.of(user.getVisitLevel().trim());
            if(null == userType){
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].visitLevel[{}] invalid,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,user.getVisitLevel(),
                        filePath);
                continue;
            }

            // status
            if(null == user.getStatus() ||
                    "".equals(user.getStatus().trim())) {
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].status[{}] empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,user.getStatus(),
                        filePath);
                continue;
            }

            EUserStatus userStatus = EUserStatus.of(user.getStatus().trim());
            if(null == userType){
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].status[{}] empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,user.getStatus(),
                        filePath);
                continue;
            }

            // blindCode
            if (null == user.getBlindCode() || user.getBlindCode().isEmpty()) {
                log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].blinkCode empty,skipped! -> {}!",
                        user.getUserId(), mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,
                        filePath);
                continue;
            }

            List<String> allowRoomMatching = user.getAllowRoomMatching();
            if (null == allowRoomMatching||allowRoomMatching.isEmpty()) {
                log.debug("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].allowRoomMatching:is empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,
                        filePath);
                continue;
            }

            //PP92 房间难度层级，此tierId是CMS中的难度层级ID
            List<Integer> tierIds = user.getTierIds();
            if (null == tierIds||tierIds.isEmpty()) {
                log.debug("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].tierIds:is empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,
                        filePath);
                continue;
            }

            //获取强制离桌的配置
            UserConfigBo.ForceStandUpConfig standUpConfig = user.getForceStandUp();
            if (null == standUpConfig) {
                log.debug("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].forceStandUpConfig :is empty,skipped! -> {}!",
                        user.getUserId(),mode,
                        startTimeStr,startTime,
                        endTimeStr,endTime,i+1,
                        filePath);
                continue;
            }
            log.debug("【U-{}】【{}-User配置】【JYM】timeRange( {}={} - {}={}).userList[{}].userid={},allowRoomMatching={}  -> {}!",
                    user.getUserId(),mode,
                    startTimeStr,startTime,
                    endTimeStr,endTime,i+1,
                    userId,
                    allowRoomMatching,
                    filePath);

            if(null == resultMap) {
                resultMap = new HashMap<>();
            }

            List<String> blindCodes = user.getBlindCode();
            for (String blindCode: blindCodes) {

                blindCode = blindCode.replace(" ", "");
                int pos = blindCode.indexOf('/');
                if (pos == -1) {
                    log.warn("【U-{}】【{}-User配置】timeRange( {}={} - {}={}).userList[{}].blinkCode[{}] invalid format(小盲/大盲),skipped! -> {}!",
                            user.getUserId(),
                            mode,
                            startTimeStr,startTime,
                            endTimeStr,endTime,i+1,blindCode,
                            filePath);
                    continue;
                }


                List<UserConfigBo> userConfigLst =resultMap.get(blindCode);
                if(null == userConfigLst){
                    userConfigLst = new ArrayList<>();
                    resultMap.put(blindCode,userConfigLst);
                }

                UserConfigBo userConfig = new UserConfigBo(userId,userStatus,userType,visitLevel,blindCode,tierIds,allowRoomMatching,standUpConfig);
                userConfigLst.add(userConfig);
                userConfigMap.put(userId,userConfig);
            }

        }

        return resultMap;
    }
    @Getter
    @Setter
    private static class TimeRangeConfigVo{
        /**
         * 时间范围，半区间
         * 格式: HH:mm
         */
        private String startTime;
        private String endTime;

        private List<PlayerConfigVo> userList;
    }
    @Getter
    @Setter
    private static class PlayerConfigVo {
        /** 用户ID */
        private int userId;

        /**
         * 用户类型
         * 取值范围参照AiType枚举
         */
        private String opType;

        /**
         *  大小盲注
         *  格式:小盲/大盲
         */
        private List<String> blindCode;

        /**
         * 可见级别
         * normal - 普通用户
         * operator  - 超级账号
         */
        private String visitLevel;

        /**
         * 关停状态
         * off - 终止
         * on  - 启用
         */
        private String status;

        /**
         * AI用户可以进入的房间
         * 1 - 联盟房
         * 0 - 金币房
         */
        private List<String> allowRoomMatching;

        /**
         * AI用户强制离开桌面的配置
         */
        private UserConfigBo.ForceStandUpConfig forceStandUp;

        /**
         * 房间难度层级
         */
        private List<Integer> tierIds;
    }
}
