package com.ai.dz.config.cache.impl;

import com.ai.dz.config.AiGamePlanSettingLoader;
import com.ai.dz.config.cache.*;
import com.ai.dz.config.constant.*;
import com.ai.dz.config.monitor.*;
import com.dzpk.common.utils.GsonHelper;
import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogHelper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.file.ConfigAutoLoadService;
import com.i366.model.room.Room;
import com.i366.room.RoomSettingFactory;
import lombok.Getter;
import org.apache.commons.collections.map.HashedMap;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

public class AiRuleConfigJsonCacheImpl
        implements IAiRuleConfigCache {
    /** 日志服务 */
    private static final Logger logger = LogUtil.getLogger(AiRuleConfigJsonCacheImpl.class);

    /** 服务实例 */
    private static AiRuleConfigJsonCacheImpl _instance;

    public static AiRuleConfigJsonCacheImpl getInstance(){
        if(null == _instance){
            synchronized (AiRuleConfigJsonCacheImpl.class){
                if(null == _instance){
                    _instance = new AiRuleConfigJsonCacheImpl();
                }
            }
        }
        return _instance;
    }

    /** 权重配置 */
    private PocerWeightConfigMapBo pocerWeightConfigMapByType = null;


    // 新的加載方法來處理新結構的JSON
    public void reloadPocerWeightConfigMap(Map<String, List<PocerWeightConfigBo>> configMap) {
        pocerWeightConfigMapByType = new PocerWeightConfigMapBo(configMap, this::orderPocers);
        logger.info("Reloaded PocerWeightConfig with new structure");
    }

    // 新的查詢方法，根據 aiType 來獲取配置
    public PocerWeightConfigBo getPocerWeightConfig(String key, String aiType) {
        AutoOverallConfigBo overallConfig =  this.autoOverallConfig;
        if( null == overallConfig ){
            logger.warn("overallConfig is null");
            return null;
        }
        if (null == key || key.trim().isEmpty()) {
            logger.warn("key is null or empty");
            return null;
        }

        // ai 權重配置
        Map<String, String> aiTypeMapping = overallConfig.getWeightMap();
        // 默認值為 "default"
        String mappedType = aiTypeMapping.getOrDefault(aiType, "default");
        logger.debug("aiType: {} aiTypeMapping: {} mappedType: {}", aiType, aiTypeMapping, mappedType);

        // 检查 mappedType 是否存在于配置表中
        if (!pocerWeightConfigMapByType.containsKey(mappedType)) {
            logger.warn("{} not found in type map, using default", mappedType);
        }

        // 如果映射表中找不到，則使用 default 配置
        Map<String, PocerWeightConfigBo> configMap = pocerWeightConfigMapByType.getOrDefault(mappedType, pocerWeightConfigMapByType.get("default"));

        if (configMap == null || configMap.isEmpty()) {
            logger.warn("config not found for aiType: {} and key: {}", aiType, key);
            return null;
        }

        // 查詢手牌配置
        key = orderPocers(key.trim());
        PocerWeightConfigBo config = configMap.get(key);
        LogHelper.log("%s   【手牌&牌型的权重配置】: aiType=%s 手牌=%s -> %s",
                System.lineSeparator(), aiType, key, GsonHelper.toJson(config,false));
        return config;
    }


    public String orderPocers(String porcers){
        if(null == porcers || porcers.length() == 0)
            return porcers;

        byte[] pocerArr = porcers.getBytes();
        Arrays.sort(pocerArr);

        return new String(pocerArr);
    }

    /** 流程Action配置 */
    private Map<String,FlowCaseActionConfigBo> flowActionConfigMap = null;
    private String genFlowActionKey(EPerHandPhase phase,boolean useSlave){
        return String.format("%s-%s",phase.name(),useSlave?"slave":"master");
    }
    public FlowCaseActionConfigBo getFlowActionConfig(EPerHandPhase phase,boolean useSlave){
        if(null == phase)
            return null;

        String key = this.genFlowActionKey(phase,useSlave);
        return flowActionConfigMap.get(key);
    }

    /** bet配置 */
    private Map<String,BetConfigBo> betConfigMap = null;
    private String betConfigKeyPattern = "%s_%s";
    public BetConfigBo getBetConfig(EFlowCase flowCase,int weight){
        BetConfigBo result = null;

        EUseCase useCase = null;
        String key = null;
        try {
            if (null == flowCase)
                return result;
            if (null == betConfigMap || betConfigMap.isEmpty())
                return result;

            if(flowCase == EFlowCase.flopWin) {
                useCase = EUseCase.flopWin;
            }else if (flowCase == EFlowCase.flopLoss)
                useCase = EUseCase.flopLoss;
            else
                useCase = EUseCase.preflop;

            key = genBetConfigKey(useCase, weight);
            result = betConfigMap.get(key);
        }finally {
            LogHelper.log("%s   【Bet配置】: flowCase=%s, weight=%s -> useCase=%s,key=%s -> %s",
                    System.lineSeparator(), flowCase, weight,useCase,key,GsonHelper.toJson(result,false));
        }
        return result;
    }
    private String genBetConfigKey(EUseCase useCase,int weight){
        return String.format(betConfigKeyPattern,
                useCase.value(),weight);
    }
    private int getUseCase(String configKey){
        if(null == configKey || "".equals(configKey.trim()))
            return 0;

        configKey = configKey.trim();
        String[] fields = configKey.split("_");
        if(fields.length!=2)
            return 0;

        return Helper.parseInt(fields[0],0);
    }
    /** bet chip range配置 */
    private Map<String,BetChipRangeConfigBo> betChipRangeConfigMap = null;
    private String betChipRangeConfigKeyPattern = "bet_chip_range_round_%s_aiType_%s_weight_%s";
    public BetChipRangeConfigBo getBetChipRangeConfig(EPerHandPhase round,EAiType aiType,int weight){
        BetChipRangeConfigBo result = null;
        String key = null;
        try {
            if (null == betChipRangeConfigMap || betChipRangeConfigMap.isEmpty())
                return result;

            key = genBetChipRangeConfigKey(round.value(),aiType.value(),weight);
            result = betChipRangeConfigMap.get(key);
        }finally {
            LogHelper.log("%s   【BetChipRange配置】【JYM】:  round=%s -> ,key=%s -> %s",
                    System.lineSeparator(),  round.value(),key,GsonHelper.toJson(result,false));
        }
        return result;
    }
    private String genBetChipRangeConfigKey(int round,int aiType, int weight){
        return String.format(betChipRangeConfigKeyPattern,round,aiType,weight);
    }

    /** af fold action range配置 */
    private Map<String,AFFoldActionRangeConfigBo> foldActionRangeConfigMap = null;
    private String afFoldActionRangeConfigKeyPattern = "af_fold_action_range_round_%s_aiType_%s_weight_%s";
    public AFFoldActionRangeConfigBo getAFFoldActionRangeConfig(EPerHandPhase round,EAiType aiType,int weight){
        AFFoldActionRangeConfigBo result = null;
        String key = null;
        try {
            if (null == foldActionRangeConfigMap || foldActionRangeConfigMap.isEmpty())
                return result;

            key = genAFFoldActionRangeConfigKey(round.value(),aiType.value(),weight);
            result = foldActionRangeConfigMap.get(key);
        }finally {
            LogHelper.log("%s   【AFFoldActionRange配置】【JYM】:round=%s, aiType=%s, weight=%s -> ,key=%s -> %s",
                    System.lineSeparator(),  round.value(),aiType.value(),weight,key,GsonHelper.toJson(result,false));
        }
        return result;
    }
    private String genAFFoldActionRangeConfigKey(int round,int aiType, int weight){
        return String.format(afFoldActionRangeConfigKeyPattern,round,aiType,weight);
    }

    /** insurance超时配置 */
    private Map<EOpTime,List<OpTimeConfigBo>> opTimeConfigMap = null;
    public Integer getOpTime(EOpTime opTime){
        Integer result = null;
        int selector = -1;
        OpTimeConfigBo temp = null;
        try {
            if (null == opTime)
                return result;

            if (null == opTimeConfigMap || opTimeConfigMap.isEmpty())
                return result;

            List<OpTimeConfigBo> configLst = opTimeConfigMap.get(opTime);

            for (OpTimeConfigBo bo : configLst) {
                if (selector == -1)
                    selector = Helper.randomIntBy(0, bo.getMaxRangeValue() - 1);

                if (bo.getMinRatio() <= selector && selector <= bo.getMaxRatio()) {
                    int time = Helper.randomIntBy(bo.getMinTime(), bo.getMaxTime());
                    result = time;
                    temp = bo;
                }
            }

            return result;
        }finally {
            if(logger.isDebugEnabled()){
                logger.debug("Random op time : type={},selector={} ,result={}-> {}",
                        opTime,selector,result,GsonHelper.toJson(temp,false));
            }
        }
    }

    /** action思考时间配置 */
    private List<ActionOptimeConfigBaseBo> actionOptimeConfigLst = null;
    /**
     * 行动思考时间,单位：秒
     *
     * @param isBBOrStradle  是否【BB或stradle】,必填
     * @param checkChip      待跟注筹码，必填
     * @param bbChip         牌局的大盲注,必填
     * @param potChip        牌局的底池，必填
     * @param action         当前确定行动，必填
     * @param betChip        当前行动对应的下注筹码，必填
     *
     * @return   行动思考时间
     *    <0     表示配置缺失或配置错误
     *    >=0    表示确定的思考时间
     */
    public int getOptimeOfAction(boolean isBBOrStradle, int checkChip, int bbChip,
                                int potChip, EAction action, int betChip){
        int result = -1;
        ActionOptimeConfigBaseBo matchConfig = null;

        try {
            if (null == this.actionOptimeConfigLst || this.actionOptimeConfigLst.isEmpty()) {
                LogHelper.log("%s   行动思考时间配置不存在",System.lineSeparator());
                return result;
            }

            for (ActionOptimeConfigBaseBo config : this.actionOptimeConfigLst) {
                if (null == config)
                    continue;

                Boolean matched = config.checkIfMatch(isBBOrStradle, checkChip, bbChip, potChip, action, betChip);
                if (null == matched)
                    continue;

                if (matched) {
                    matchConfig = config;
                    break;
                }
            }
            if (null == matchConfig) {
                LogHelper.log("%s   无匹配的行动思考时间配置",System.lineSeparator());
                return result;
            }

            result = matchConfig.random();
            return result;
        }finally {
            LogHelper.log("%s   匹配配置: %s ",System.lineSeparator(),matchConfig);
        }
    }

    /** 用户类型配置 */
    private Map<Integer,UserOptypeConfigBo> userConfigMap = null;
    public UserOptypeConfigBo getUserTypeConfig(int userId){
        if(null == this.userConfigMap || this.userConfigMap.isEmpty())
            return null;

        return this.userConfigMap.get(userId);
    }

    /** 手动模式-综合配置 */
    @Getter
    private ManualOverallConfigBo manualOverallConfig;
    public ManualStandupConfigBo getManualStandupConfig(){
        ManualStandupConfigBo result = null;
        if(null == manualOverallConfig)
            return result;

        result = manualOverallConfig.getStandupConfig();
        return result;
    }

    /** 自动模式-综合配置 */
    @Getter
    private AutoOverallConfigBo autoOverallConfig;

    /** 自动模式-用户配置 */
    // 以时间范围为维度的用户列表
    private AutoUserTimeRangeConfigBo[] autoUserArrayByTimeRange = null;
    // userId -> config
    private Map<Integer,UserConfigBo> autoUserConfigMap = null;
    public UserConfigBo getAutoUserConfig(int userId){
        UserConfigBo result = null;
        if(null != autoUserConfigMap && autoUserConfigMap.size()>0)
            result = autoUserConfigMap.get(userId);
        return result;
    }
    public List<UserConfigBo> getConfigUserIfMatch(String blindCode, long sysTime, Room room){
        List<UserConfigBo> resultMap = null;
        if(null == blindCode || "".equals(blindCode.trim()))
            return resultMap;
        if(null == this.autoUserArrayByTimeRange || this.autoUserArrayByTimeRange.length==0)
            return resultMap;

        // 时段范围
        AutoUserTimeRangeConfigBo timeRangeConfig = null;
        for(AutoUserTimeRangeConfigBo config : this.autoUserArrayByTimeRange){
            if(null == config)
                continue;

            if(!config.checkIfTimeMatch(sysTime))
                continue;

            timeRangeConfig = config;
            break;
        }
        if(null == timeRangeConfig)
            return null;

        // 盲注级别
        List<UserConfigBo> userConfigLst = timeRangeConfig.getUserConfig(blindCode);
        if(null == userConfigLst || userConfigLst.isEmpty())
            return null;

        // 如果当前房间类型不是俱乐部/联盟房
        if (room.getClubRoomType() != 1) {
            return null;
        }

        // 满足条件的用户配置
        for(UserConfigBo user : userConfigLst){
            if(null == user)
                continue;

            //PP92 房间难度层级配置匹配
            //根据需求AI玩家与普通玩家匹配方式不一样：AI玩家是根据分配的等级ID进入对应难度的房间
            if (user.getTierIds().isEmpty() ||
                    //假设当前AI玩家不在当前房间难度层级范围内，则忽略
                    !user.getTierIds().contains(room.getTierId())) {
                logger.trace("[R-{}][U-{}] AI玩家的房间难度层级({})不在当前房间难度层级({})分配范围内",
                        room.getRoomId(),user.getUserId(),user.getTierIds(),room.getTierId());
                continue;
            }

            //如果配置包含房间配置
            if (user.getAllowRoomMatching().isEmpty() ||
                //假设当前AI玩家不在当前房间分配范围内，则乎略
                !user.getAllowRoomMatching().contains(String.valueOf(room.getTribeRoomType()))) {
                logger.trace("[R-{}][U-{}] AI玩家({})不在当前房间类型({})分配范围内",
                        room.getRoomId(),user.getUserId(),user.getAllowRoomMatching(),room.getTribeRoomType());
                continue;
            }
            //PP50 需要满足了当前离桌设定列表的AI才允许派遣
            if (room.getAtGamePlan() != null && !room.getAtGamePlan().getStandingConfig().isEmpty()) {
                 if (!room.getAtGamePlan().getStandingConfig().contains(user.getForceStandUp().getStandingConfigId())) {
                    logger.trace("[R-{}][U-{}][房间指定派遣配置] 不满足当前离桌设定列表 跳过忽略", room.getRoomId(), user.getUserId());
                    continue;
                }
                logger.trace("[R-{}][U-{}][房间指定派遣配置] 满足当前离桌设定列表：{}", room.getRoomId(), user.getUserId(), room.getAtGamePlan().getStandingConfig());
            }

            if(null == resultMap)
                resultMap = new ArrayList<>();
            resultMap.add(user);
        }
        return resultMap;
    }
    public List<Integer> getAllUserOfAt(){
        if(null == this.autoUserConfigMap || this.autoUserConfigMap.isEmpty())
            return null;
        //PP80 条件1. 所有啟用AI都能被派遣至牌局打牌/成為觀眾
        return this.autoUserConfigMap.entrySet().stream()
                .filter(entry -> entry.getValue().getStatus() == EUserStatus.on)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /** 功能开关 */
    private FunctionSwitchConfigBo switchConfigBo = null;
    public FunctionSwitchConfigBo getSwitchConfig(){
        return this.switchConfigBo;
    }

    /** 保险购买配置 */
    private Map<EInsuranceConfig, List<InsuanceBuyRuleItemBo>> insuranceConfigMap;
    public List<InsuanceBuyRuleItemBo> getInsuanceBuyRuleConfig(EInsuranceConfig config){
        List<InsuanceBuyRuleItemBo> list = null;
        if(null == config)
            return list;

        if(this.insuranceConfigMap==null || this.insuranceConfigMap.isEmpty())
            return list;

        list = this.insuranceConfigMap.get(config);
        return list;
    }

    /** Common配置 */

    @Getter
    private CommLeftInAdvanceConfigBo leftInAdvanceConfig = null;

    @Getter
    private CommonDispatchConfigBo commonDispatchConfig = null;

    @Getter
    private CommonStandupConfigBo commonStandupConfig = null;

    @Getter
    private CommonBringinConfigBo commonBringinConfig = null;

    private Map<EAiType,BringinPerUserTypeConfigBo> commonBringinPerTypeConfigMap = null;

    @Getter
    private CommonIdleModeConfigBo commonIdleModeConfigBo = null;

    @Getter
    private CommonInsuranceConfigBo commonInsuranceConfig;

    private boolean enableBipai;

    public BringinPerUserTypeConfigBo getBringinPerUserTypeConfig(EAiType userType){
        BringinPerUserTypeConfigBo result = null;

        if(null == userType)
            return result;

        if(null != this.commonBringinPerTypeConfigMap &&
                this.commonBringinPerTypeConfigMap.size()>0){
            result = this.commonBringinPerTypeConfigMap.get(userType);
        }

        return result;
    }

    @Override
    public boolean isEnableBipai() {
        return enableBipai;
    }

    /** 爆牌模式的配置 */
    private  List<ExplosionTableConfigBo> explosionTableConfig = null;

    /**
     * 获取时间区间的爆桌配置
     *
     * @return
     */
    public ExplosionTableConfigBo getExplosionTableConfig(long sysTimes){
        ExplosionTableConfigBo result = null;

        if(null == this.explosionTableConfig ||
                this.explosionTableConfig.isEmpty())
            return result;

        for(ExplosionTableConfigBo c : this.explosionTableConfig){
            if(c.checkIfMatch(sysTimes)){
                result = c;
                break;
            }
        }

        return result;
    }

    /** 实时战况观众不可见白名单 */
    private List<Integer> roomViewerWhitelistConfig = null;
    public Boolean checkIfRoomViewerWhitelist(int userId){
        Boolean result = null;

        if(null != this.roomViewerWhitelistConfig &&
                !this.roomViewerWhitelistConfig.isEmpty())
            result = this.roomViewerWhitelistConfig.contains(userId);

        return result;
    }

    //PP67 俱乐部CMS补充AI登入信息
    private String aiIpGpsAdditionalConfigKeyPattern = "ai_%s_ip_gps_config";
    private Map<String,AiIPGPSConfigBo> aiIPGPSConfigMap = null;
    private List<AiIPGPSConfigBo> allAiIpGpsInfo = null;
    public AiIPGPSConfigBo getAutoUserIpGpsAdditionalInfo(int userId) {
        AiIPGPSConfigBo result = null;
        String key = this.genAiIpGpsConfigKey(userId);
        if(this.aiIPGPSConfigMap !=null && !this.aiIPGPSConfigMap.isEmpty()) {
            result = this.aiIPGPSConfigMap.get(key);
        }
        if (result == null) {
            if (allAiIpGpsInfo != null && !allAiIpGpsInfo.isEmpty()) {
                //获取缓存中的IP/GPS信息
                result = allAiIpGpsInfo.get(0);
                if(null == result){
                    return null;
                }

                if (this.aiIPGPSConfigMap == null) {
                    this.aiIPGPSConfigMap = new HashMap<>();
                }

                this.aiIPGPSConfigMap.put(key,result);
                //获取一个移除一个
                allAiIpGpsInfo.remove(0);
            }
        }
        return result;
    }
    private String genAiIpGpsConfigKey(int userId){
        return String.format(aiIpGpsAdditionalConfigKeyPattern,userId);
    }
    public void setAllAiIpGpsInfo(List<AiIPGPSConfigBo> dataLst){
        try {
            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【AI IP GPS配置】【JYM】不存在！");
                return;
            }
            this.allAiIpGpsInfo = dataLst;
            logger.info("成功加载【AI IP GPS配置】【JYM】：cache-size={}",
                    allAiIpGpsInfo.size());

        }catch (Exception ex){
            logger.warn("加载【AI IP GPS配置】【JYM】失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    /** 配置文件监控服务 */
    private ConfigAutoLoadService watchedService;

    private AiRuleConfigJsonCacheImpl(){
        if(logger.isDebugEnabled()){
            logger.debug("初始化配置缓存服务......");
        }
        this.watchedService = new ConfigAutoLoadService("resource/appserver/at");

//        //2024/09/15 因需求1-2废除该实现
//        BetConfigHandler perflopBetHandler = new BetConfigHandler(this,EUseCase.preflop);
//        BetConfigHandler flopWinBetHandler = new BetConfigHandler(this,EUseCase.flopWin);
//        BetConfigHandler flopLossBetHandler = new BetConfigHandler(this,EUseCase.flopLoss);

        PocerWeightConfigHandler weightConfigHandler = new PocerWeightConfigHandler(this);

        ActionOpTimeConfigHandler actionOpTimeHandler = new ActionOpTimeConfigHandler(this);
        OpTimeConfigHandler insuranceOpTimeHandler = new OpTimeConfigHandler(this,EOpTime.insurance);

        FlowControlConfigHandler preflopWinFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.preflopWin,true);
        FlowControlConfigHandler preflopLossFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.preflopLoss,true);
        FlowControlConfigHandler flopWinFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.flopWin,true);
        FlowControlConfigHandler flopLossFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.flopLoss,true);
        FlowControlConfigHandler turnWinFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.turnWin,true);
        FlowControlConfigHandler turnLossFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.turnLoss,true);
        FlowControlConfigHandler riverWinFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.riverWin,true);
        FlowControlConfigHandler riverLossFcHandler = new FlowControlConfigHandler(this,EPerHandPhase.riverLoss,true);

        FlowControlConfigHandler preflopWinFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.preflopWin,false);
        FlowControlConfigHandler preflopLossFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.preflopLoss,false);
        FlowControlConfigHandler flopWinFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.flopWin,false);
        FlowControlConfigHandler flopLossFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.flopLoss,false);
        FlowControlConfigHandler turnWinFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.turnWin,false);
        FlowControlConfigHandler turnLossFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.turnLoss,false);
        FlowControlConfigHandler riverWinFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.riverWin,false);
        FlowControlConfigHandler riverLossFcHandlerSlave = new FlowControlConfigHandler(this,EPerHandPhase.riverLoss,false);

        UserOpTypeConfigHandler userOpTypeConfigHandler = new UserOpTypeConfigHandler(this);

        ManualOverallConfigHandler manualOverallConfigHandler = new ManualOverallConfigHandler(this);
        AutoOverallConfigHandler autoOverallConfigHandler = new AutoOverallConfigHandler(this);
        AutoUserConfigHandler autoUserConfigHandler = new AutoUserConfigHandler(this);

        FunctionSwitchConfigHandler switchConfigHandler = new FunctionSwitchConfigHandler(this);

        InsuaranceBuyRuleConfigHandler insuaranceBuyRuleConfigHandler = new InsuaranceBuyRuleConfigHandler(this);

        CommonConfigHandler commonConfigHandler = new CommonConfigHandler(this);
        BringBlackListHandler bringBlackListHandler = new BringBlackListHandler(this);
        RequestRecordUserInfoHandler requestRecordUserInfoHandler = new RequestRecordUserInfoHandler(this);

        ExplosionTableConfigHandler explosionTableConfigHandler = new ExplosionTableConfigHandler(this);

        RoomViewerWhitelistConfigHandler roomViewerWhitelistConfigHandler = new RoomViewerWhitelistConfigHandler(this);
        BetChipRangeConfigHandler betChipRangeConfigHandler = new BetChipRangeConfigHandler(this);
        AFFoldActionRangeConfigHandler afFoldActionRangeConfigHandler = new AFFoldActionRangeConfigHandler(this);
        AiTypeConfigHandler aiTypeConfigHandler = new AiTypeConfigHandler(this);
        AiIPGPSConfigHandler aiIPGPSConfigHandler = new AiIPGPSConfigHandler(this);

        VigilanteConfigHandler vigilanteConfigHandler = new VigilanteConfigHandler(this);

//        //2024/09/15 因需求1-2废除该实现
//        this.watchedService.addHandler(perflopBetHandler.fileName(),perflopBetHandler);
//        this.watchedService.addHandler(flopWinBetHandler.fileName(),flopWinBetHandler);
//        this.watchedService.addHandler(flopLossBetHandler.fileName(),flopLossBetHandler);

        this.watchedService.addHandler(weightConfigHandler.fileName(),weightConfigHandler);

        this.watchedService.addHandler(actionOpTimeHandler.fileName(),actionOpTimeHandler);
        this.watchedService.addHandler(insuranceOpTimeHandler.fileName(),insuranceOpTimeHandler);

        this.watchedService.addHandler(preflopWinFcHandler.fileName(),preflopWinFcHandler);
        this.watchedService.addHandler(preflopLossFcHandler.fileName(),preflopLossFcHandler);
        this.watchedService.addHandler(flopWinFcHandler.fileName(),flopWinFcHandler);
        this.watchedService.addHandler(flopLossFcHandler.fileName(),flopLossFcHandler);
        this.watchedService.addHandler(turnWinFcHandler.fileName(),turnWinFcHandler);
        this.watchedService.addHandler(turnLossFcHandler.fileName(),turnLossFcHandler);
        this.watchedService.addHandler(riverWinFcHandler.fileName(),riverWinFcHandler);
        this.watchedService.addHandler(riverLossFcHandler.fileName(),riverLossFcHandler);

        this.watchedService.addHandler(preflopWinFcHandlerSlave.fileName(),preflopWinFcHandlerSlave);
        this.watchedService.addHandler(preflopLossFcHandlerSlave.fileName(),preflopLossFcHandlerSlave);
        this.watchedService.addHandler(flopWinFcHandlerSlave.fileName(),flopWinFcHandlerSlave);
        this.watchedService.addHandler(flopLossFcHandlerSlave.fileName(),flopLossFcHandlerSlave);
        this.watchedService.addHandler(turnWinFcHandlerSlave.fileName(),turnWinFcHandlerSlave);
        this.watchedService.addHandler(turnLossFcHandlerSlave.fileName(),turnLossFcHandlerSlave);
        this.watchedService.addHandler(riverWinFcHandlerSlave.fileName(),riverWinFcHandlerSlave);
        this.watchedService.addHandler(riverLossFcHandlerSlave.fileName(),riverLossFcHandlerSlave);

        this.watchedService.addHandler(userOpTypeConfigHandler.fileName(),userOpTypeConfigHandler);

        this.watchedService.addHandler(manualOverallConfigHandler.fileName(),manualOverallConfigHandler);
        this.watchedService.addHandler(autoOverallConfigHandler.fileName(),autoOverallConfigHandler);
        this.watchedService.addHandler(autoUserConfigHandler.fileName(),autoUserConfigHandler);

        this.watchedService.addHandler(switchConfigHandler.fileName(),switchConfigHandler);

        this.watchedService.addHandler(insuaranceBuyRuleConfigHandler.fileName(),insuaranceBuyRuleConfigHandler);

        this.watchedService.addHandler(commonConfigHandler.fileName(),commonConfigHandler);

        this.watchedService.addHandler(bringBlackListHandler.fileName(),bringBlackListHandler);
        this.watchedService.addHandler(requestRecordUserInfoHandler.fileName(),requestRecordUserInfoHandler);
        this.watchedService.addHandler(explosionTableConfigHandler.fileName(),explosionTableConfigHandler);
        this.watchedService.addHandler(betChipRangeConfigHandler.fileName(),betChipRangeConfigHandler);
        this.watchedService.addHandler(afFoldActionRangeConfigHandler.fileName(),afFoldActionRangeConfigHandler);
        this.watchedService.addHandler(aiTypeConfigHandler.fileName(),aiTypeConfigHandler);

        this.watchedService.addHandler(vigilanteConfigHandler.fileName(),vigilanteConfigHandler);

        this.watchedService.addHandler(roomViewerWhitelistConfigHandler.fileName(),
                roomViewerWhitelistConfigHandler);
        this.watchedService.addHandler(aiIPGPSConfigHandler.fileName(),aiIPGPSConfigHandler);

        this.watchedService.initialize();

        RoomSettingFactory.register(new AiGamePlanSettingLoader());
        if(logger.isDebugEnabled()){
            logger.debug("成功初始化配置缓存服务......");
        }
    }

    public void reloadFlowActionConfig(boolean isMaster,EPerHandPhase phase,FlowCaseActionConfigBo flowActionConfigBo,String symbol){
        try {
            if(null == phase)
                return;

            if(null == flowActionConfigBo)
            {
                logger.warn("【{}-Action配置】不存在！",symbol);
                return;
            }

            if(null == this.flowActionConfigMap){
                this.flowActionConfigMap = new HashMap<>();
            }

            String key = this.genFlowActionKey(phase,!isMaster);
            FlowCaseActionConfigBo oldConfig = this.flowActionConfigMap.get(key);
            this.flowActionConfigMap.put(key,flowActionConfigBo);

            logger.info("成功加载【{}-Action配置】：cached-size={} , new-size={}",
                    symbol,null==oldConfig?0:oldConfig.size(),flowActionConfigBo.size());
        }catch (Exception ex){
            logger.warn("加载【{}-Action配置】失败：{}",symbol,ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadBetConfig(EUseCase useCase,List<BetConfigBo> dataLst){
        try {
            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【{}-Bet配置】不存在！",useCase);
                return;
            }

            Map<String,BetConfigBo> map = new HashedMap();
            if(null != this.betConfigMap && this.betConfigMap.size()>0) {
                for (String key : this.betConfigMap.keySet()){
                    int uCase = getUseCase(key);
                    if(uCase == useCase.value())
                        continue;

                    map.put(key,this.betConfigMap.get(key));
                }
            }
            int useCasesize=0;
            for(BetConfigBo bo : dataLst){
                if(null == bo){
                    continue;
                }

                String key = this.genBetConfigKey(useCase,bo.getWeight());
                map.put(key,bo);
                useCasesize ++;
            }

            int oldSize = null == this.betConfigMap?0:this.betConfigMap.size();
            this.betConfigMap = map;

            logger.info("成功加载【{}-Bet配置】：cache-size={} , new-size={} , useCaseSize={}",
                    useCase,
                    oldSize,map.size(),
                    useCasesize);

        }catch (Exception ex){
            logger.warn("加载【{}-Bet配置】失败：{}",useCase,ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadBetChipRangeConfig(List<BetChipRangeConfigBo> dataLst){
        try {
            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【BetChipRange配置】【JYM】不存在！");
                return;
            }

            Map<String,BetChipRangeConfigBo> map = new HashedMap();
            int useCaseSize=0;
            for(BetChipRangeConfigBo bo : dataLst){
                if(null == bo){
                    continue;
                }

                String key = this.genBetChipRangeConfigKey(bo.getRound().value(), bo.getAiType().value(),bo.getWeight());
                map.put(key,bo);
                useCaseSize ++;
            }

            int oldSize = null == this.betChipRangeConfigMap?0:this.betChipRangeConfigMap.size();
            this.betChipRangeConfigMap = map;

            logger.info("成功加载【BetChipRange配置】【JYM】：cache-size={} , new-size={} , useCaseSize={}",
                    oldSize,map.size(),
                    useCaseSize);

        }catch (Exception ex){
            logger.warn("加载【BetChipRange配置配置】【JYM】失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    public void reloadAFFoldActionRangeConfig(List<AFFoldActionRangeConfigBo> dataLst){
        try {
            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【AFFoldActionRange配置】【JYM】不存在！");
                return;
            }

            Map<String,AFFoldActionRangeConfigBo> map = new HashedMap();
            int useCaseSize=0;
            for(AFFoldActionRangeConfigBo bo : dataLst){
                if(null == bo){
                    continue;
                }

                String key = this.genAFFoldActionRangeConfigKey(bo.getRound().value(),bo.getAiType().value(),bo.getWeight());
                map.put(key,bo);
                useCaseSize ++;
            }

            int oldSize = null == this.foldActionRangeConfigMap?0:this.foldActionRangeConfigMap.size();
            this.foldActionRangeConfigMap = map;

            logger.info("成功加载【AFFoldActionRange配置】【JYM】：cache-size={} , new-size={} , useCaseSize={}",
                    oldSize,map.size(),
                    useCaseSize);

        }catch (Exception ex){
            logger.warn("加载【AFFoldActionRange配置】【JYM】失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    @Getter
    private VigilanteConfigBo vigilanteConfig;

    public void reloadVigilanteConfig(VigilanteConfigBo configBo) {
        try {
            if(null == configBo) {
                logger.info("【Vigilante配置】不存在！");
                return;
            }

            VigilanteConfigBo old = this.vigilanteConfig;
            this.vigilanteConfig = configBo;

            logger.info("成功加载【Vigilante配置】：cached-config={} , new-config={}",
                    GsonHelper.toJson(old, false),
                    GsonHelper.toJson(this.vigilanteConfig, false));
        } catch (Exception ex) {
            logger.warn("加载【Vigilante配置】失败：{}", ex.getMessage());
            if(logger.isDebugEnabled()) {
                logger.debug(ex);
            }
        }
    }

    public void reloadAiTypeConfig(List<AiTypeConfigBo> dataLst){
        try {
            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【AiType配置】【JYM】不存在！");
                return;
            }

            int useCaseSize=0;
            int oldSize = EAiType.TYPE_MAP.size();
            EAiType.TYPE_MAP.clear();
            for(AiTypeConfigBo bo : dataLst){
                if(null == bo){
                    continue;
                }
                EAiType.TYPE_MAP.put(bo.getValue(),bo.getType());
                useCaseSize ++;
            }

            logger.info("成功加载【AiType配置】【JYM】：cache-size={} , new-size={} , useCaseSize={}",
                    oldSize,EAiType.TYPE_MAP.size(),
                    useCaseSize);

        }catch (Exception ex){
            logger.warn("加载【AiType配置】【JYM】失败：{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadOpTimeConfig(EOpTime opTime,List<OpTimeConfigBo> dataLst){
        try {
            if(null == opTime)
                return;

            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【{}-OpTime配置】不存在！",opTime);
                return;
            }

            if(null == this.opTimeConfigMap){
                this.opTimeConfigMap = new HashMap<>();
            }

            List<OpTimeConfigBo> oldLst = this.opTimeConfigMap.get(opTime);
            int oldSize = oldLst==null?0:oldLst.size();
            this.opTimeConfigMap.put(opTime,dataLst);

            logger.info("成功加载【{}-OpTime配置】：cached-size={} , new-size={}",
                    opTime,oldSize,dataLst.size());

        }catch (Exception ex){
            logger.warn("加载【{}-OpTime配置】失败："+ex.getMessage(),opTime);
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadUserTypeConfig(Map<Integer,UserOptypeConfigBo> configBoMap){
        try {
            if(null == configBoMap || configBoMap.isEmpty())
            {
                logger.info("【User-OpType配置】不存在！",configBoMap);
                return;
            }

            int oldSize =  this.userConfigMap==null?0: this.userConfigMap.size();
            this.userConfigMap = configBoMap;

            logger.info("成功加载【User-OpType配置】：cached-size={} , new-size={}",
                    oldSize,this.userConfigMap.size());
        }catch (Exception ex){
            logger.warn("加载【User-OpType配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadManualOverallConfig(ManualOverallConfigBo overallConfig){
        try {
            if(null == overallConfig)
            {
                logger.info("【manual-overall配置】不存在！");
                return;
            }

            ManualOverallConfigBo old =  this.manualOverallConfig;
            this.manualOverallConfig = overallConfig;

            logger.info("成功加载【manual-overall配置】：cached-config={} , new-config={}",
                    GsonHelper.toJson(old,false),
                    GsonHelper.toJson(this.manualOverallConfig,false));
        }catch (Exception ex){
            logger.warn("加载【manual-overall配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadAutoOverallConfig(AutoOverallConfigBo overallConfig){
        try {
            if(null == overallConfig)
            {
                logger.info("【auto-overall配置】不存在！");
                return;
            }

            AutoOverallConfigBo old =  this.autoOverallConfig;
            this.autoOverallConfig = overallConfig;

            if(logger.isTraceEnabled()) {
                logger.trace("成功加载【auto-overall配置】：cached-config={} , new-config={}",
                        GsonHelper.toJson(old, false), GsonHelper.toJson(this.autoOverallConfig, false));
            }
        }catch (Exception ex){
            logger.warn("加载【auto-overall配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadAutoUserConfig(AutoUserTimeRangeConfigBo[] timeRangeArr,Map<Integer,UserConfigBo> userConfigMap){
        try {
            AutoUserTimeRangeConfigBo[] userConfigTimeRange = this.autoUserArrayByTimeRange;
            Map<Integer,UserConfigBo> userConfigOld = this.autoUserConfigMap;

            this.autoUserArrayByTimeRange = timeRangeArr;
            this.autoUserConfigMap = userConfigMap;

            logger.info("成功加载【auto-User配置】：cached-user-size={} , new-user-size={} , cached-timeRange-size={} , new-timeRange-size={}",
                    null == userConfigOld?0:userConfigOld.size(),
                    null == this.autoUserConfigMap?0:this.autoUserConfigMap.size(),
                    null == userConfigTimeRange?0:userConfigTimeRange.length,
                    null == this.autoUserArrayByTimeRange?0:this.autoUserArrayByTimeRange.length);
        }catch (Exception ex){
            logger.warn("加载【auto-User配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadFunctionSwitchConfig(FunctionSwitchConfigBo configBo){
        try {
            if(null == configBo)
            {
                logger.info("【功能开关配置】不存在！");
                return;
            }

            Boolean autoModeOn = null;
            Boolean manualModeOn = null;
            Boolean etModeOn = null;
            if(null != this.switchConfigBo){
                autoModeOn = this.switchConfigBo.isAutoModeOn();
                manualModeOn = this.switchConfigBo.isManualModeOn();
                etModeOn = this.switchConfigBo.isExplosionTableOn();
            }
            this.switchConfigBo = configBo;

            logger.info("成功加载【功能开关配置】：oldSwitch:auto={},manual={},et={}-> newSwitch:auto={},manual={},et={}",
                    autoModeOn,manualModeOn,etModeOn,
                    this.switchConfigBo.isAutoModeOn(),
                    this.switchConfigBo.isManualModeOn(),
                    this.switchConfigBo.isExplosionTableOn());
        }catch (Exception ex){
            logger.warn("加载【功能开关配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
    public void reloadInsuranceBuyRuleConfig(Map<EInsuranceConfig, List<InsuanceBuyRuleItemBo>> config){
        try {
            if(null == config)
            {
                logger.info("【insurance-buyrule配置】不存在！");
                return;
            }

            Map<EInsuranceConfig, List<InsuanceBuyRuleItemBo>> oldConfig = this.insuranceConfigMap;
            this.insuranceConfigMap = config;

            logger.info("成功加载【insurance-buyrule配置】：cacheSize={} -> newSize={}",
                    oldConfig==null?0:oldConfig.size(),
                    this.insuranceConfigMap.size());
        }catch (Exception ex){
            logger.warn("加载【功能开关配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    public void reloadCommonConfig(CommLeftInAdvanceConfigBo leftInAdvanceConfig,
                                   CommonDispatchConfigBo dispatchConfig,
                                   CommonStandupConfigBo standupConfig,
                                   CommonBringinConfigBo commonBringinConfig,
                                   Map<EAiType,BringinPerUserTypeConfigBo> bringinPerTypeConfig,
                                   CommonIdleModeConfigBo idleModeConfig,
                                   CommonInsuranceConfigBo commonInsuranceConfigBo,
                                   boolean enableBipai){
        try {
            if(null != leftInAdvanceConfig)
            {
                CommLeftInAdvanceConfigBo old = this.leftInAdvanceConfig;
                this.leftInAdvanceConfig = leftInAdvanceConfig;
                logger.info("成功加载【Common-leftInAdvance配置】：old-cache={} -> new-cache={}",
                        old,
                        this.leftInAdvanceConfig);
            }else{
                logger.info("【Common-leftInAdvance配置】不存在，不更新Cache！");
            }

            if(null != dispatchConfig)
            {
                CommonDispatchConfigBo old = this.commonDispatchConfig;
                this.commonDispatchConfig = dispatchConfig;
                logger.info("成功加载【Common-dispatch配置】：old-cache={} -> new-cache={}",
                        old,
                        this.commonDispatchConfig);
            }else{
                logger.info("【Common-dispatch配置】不存在，不更新Cache！");
            }

            if(null != standupConfig)
            {
                CommonStandupConfigBo old = this.commonStandupConfig;
                this.commonStandupConfig = standupConfig;
                logger.info("成功加载【Common-standup配置】：old-cache={} -> new-cache={}",
                        old,
                        this.commonStandupConfig);
            }else{
                logger.info("【Common-standup配置】不存在，不更新Cache！");
            }

            if(null != commonBringinConfig){
                CommonBringinConfigBo old = this.commonBringinConfig;
                this.commonBringinConfig = commonBringinConfig;
                logger.info("成功加载【Common-bringin配置】：{}cachedConfig={} {} newConfig={}",
                        System.lineSeparator(),
                        old,
                        System.lineSeparator(),
                        this.commonBringinConfig);
            }else{
                logger.info("【Common-bringin配置】不存在，不更新Cache！");
            }

            if(null != bringinPerTypeConfig && !bringinPerTypeConfig.isEmpty()){
                Map<EAiType,BringinPerUserTypeConfigBo> old = this.commonBringinPerTypeConfigMap;
                this.commonBringinPerTypeConfigMap = bringinPerTypeConfig;
                logger.info("成功加载【Common-bringinPerUserType配置】：cacheSize={} -> newSize={}",
                        old==null?0:old.size(),
                        this.commonBringinPerTypeConfigMap.size());
            }else{
                logger.info("【Common-bringinPerUserType配置】不存在，不更新Cache！");
            }

            if(null != idleModeConfig)
            {
                CommonIdleModeConfigBo old = this.commonIdleModeConfigBo;
                this.commonIdleModeConfigBo = idleModeConfig;
                logger.info("成功加载【Common-idle-mode配置】：old-cache={} -> new-cache={}",
                        old,
                        this.commonIdleModeConfigBo);
            }else{
                logger.info("【Common-idle-mode配置】不存在，不更新Cache！");
            }

            if(null != commonInsuranceConfigBo)
            {
                CommonInsuranceConfigBo old = this.commonInsuranceConfig;
                this.commonInsuranceConfig = commonInsuranceConfigBo;
                logger.info("成功加载【Common-insurance配置】：old-cache={} -> new-cache={}",
                        old,
                        this.commonInsuranceConfig);
            }else{
                logger.info("【Common-insurance配置】不存在，不更新Cache！");
            }

            boolean wasEnableBipai = this.enableBipai;
            this.enableBipai = enableBipai;
            logger.info("成功加载【Common-enableBipai配置】：old-status={} -> new-status={}",
                    wasEnableBipai,
                    this.enableBipai);
        }catch (Exception ex){
            logger.warn("加载【Common配置】failed:{}",ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    public void reloadActionOptimeConfig(List<ActionOptimeConfigBaseBo> dataLst){
        try {
            if(null == dataLst || dataLst.isEmpty())
            {
                logger.info("【Action-OpTime配置】不存在！");
                return;
            }

            List<ActionOptimeConfigBaseBo> old = this.actionOptimeConfigLst;
            this.actionOptimeConfigLst = dataLst;

            logger.info("成功加载【Action-OpTime配置】：cached-size={} , new-size={}",
                    old==null?0:old.size(),dataLst.size());

        }catch (Exception ex){
            logger.warn("加载【Action-OpTime配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    public void reloadExplosionTableConfig(List<ExplosionTableConfigBo> configLst){
        try {
            if(null == configLst || configLst.isEmpty())
            {
                logger.info("【爆桌模式配置】不存在！");
                return;
            }

            List<ExplosionTableConfigBo> old = this.explosionTableConfig;
            this.explosionTableConfig = configLst;

            logger.info("成功加载【爆桌模式配置】：cached-size={} , new-size={}",
                    old==null?0:old.size(),configLst.size());

        }catch (Exception ex){
            logger.warn("加载【爆桌模式配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }

    public void reloadRoomViewerWhitelistConfig(List<Integer> configLst){
        try {
            if(null == configLst || configLst.isEmpty())
            {
                logger.info("【实时战况观众不可见白名单配置】不存在！");
                return;
            }

            List<Integer> old = this.roomViewerWhitelistConfig;
            this.roomViewerWhitelistConfig = configLst;

            logger.info("成功加载【实时战况观众不可见白名单配置】：cached-size={} , new-size={}",
                    old==null?0:old.size(),configLst.size());

        }catch (Exception ex){
            logger.warn("加载【实时战况观众不可见白名单配置】失败："+ex.getMessage());
            if(logger.isDebugEnabled())
                logger.debug(ex);
        }
    }
}
