package com.dzpk.component.repositories.redis.lock;


import com.alibaba.fastjson.JSON;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import org.apache.logging.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.concurrent.TimeUnit;

/**
 * RedisDistributedLockGenerator
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
public class RedisDistributedLockGenerator {

    private static final Logger logger = LogUtil.getLogger(RedisDistributedLockGenerator.class);

    /**
     * 查询配置
     * @param code 配置代码
     * @return RedisLockConfig
     */
    private static RedisLockConfig findConfig(String code) {
        String sql = "select expire_time,time_unit,retry_count,retry_interval_ms from redis_lock_config where code = ? limit 1";
        try (Connection connection = DBUtil.getConnection();
             PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setString(1, code);
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return RedisLockConfig.builder()
                        .expireTime(rs.getLong("expire_time"))
                        .timeUnit(rs.getString("time_unit"))
                        .retryCount(rs.getInt("retry_count"))
                        .retryIntervalMs(rs.getLong("retry_interval_ms"))
                        .build();
            }
        } catch (Exception e) {
            logger.error("Failed to find Redis lock config for code: {}", code, e);
        }
        return null;
    }

    /**
     * 生成锁
     * @param code
     * @param lockKey
     * @return
     */
    public static RedisDistributedLock generate(String code, String lockKey) {
        RedisLockConfig dbConfig = findConfig(code);
        if (dbConfig == null) {
            dbConfig = findConfig(RedisLockConfigCode.DEFAULT);
        }
        // 默认配置
        RedisDistributedLock lock = new RedisDistributedLock(
                lockKey,
                1,
                TimeUnit.SECONDS,
                1,
                1000);

        if (dbConfig != null) {
            lock = new RedisDistributedLock(
                    lockKey,
                    dbConfig.getExpireTime(),
                    TimeUnit.valueOf(dbConfig.getTimeUnit().toUpperCase()),
                    dbConfig.getRetryCount(),
                    dbConfig.getRetryIntervalMs()
            );
        }
        logger.info("Generated lock: {}", JSON.toJSONString(lock));
        return lock;
    }





}
