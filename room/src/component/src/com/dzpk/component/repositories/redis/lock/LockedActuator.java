package com.dzpk.component.repositories.redis.lock;


/**
 * LockedActuator
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
public class LockedActuator {

    /**
     * 带锁执行
     * @param operation 操作
     * @param locks 锁
     */
    public static void withLock(LockedOperation operation, RedisDistributedLock... locks) {
        boolean[] lockedFlags = new boolean[locks.length];
        try {
            // 按固定顺序尝试获取所有锁（避免死锁）
            for (int i = 0; i < locks.length; i++) {
                lockedFlags[i] = locks[i].lock();
                if (!lockedFlags[i]) {
                    throw new RuntimeException("Lock busy: " + locks[i]);
                }
            }
            operation.execute();
        } catch (Exception e) {
            throw new RuntimeException("Business error", e);
        } finally {
            // 按获取的逆序释放锁
            for (int i = lockedFlags.length - 1; i >= 0; i--) {
                if (lockedFlags[i]) {
                    locks[i].unlock();
                }
            }
        }
    }

}
