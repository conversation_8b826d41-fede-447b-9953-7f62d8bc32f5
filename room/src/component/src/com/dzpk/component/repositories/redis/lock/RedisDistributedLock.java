package com.dzpk.component.repositories.redis.lock;


import com.dzpk.component.repositories.redis.RedisPoolService;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * DistributedLock
 *
 * <AUTHOR>
 * @since 2025/4/28
 */
@Slf4j
@Getter
@ToString
public class RedisDistributedLock {

    private final String lockKey;
    private final String lockValue;
    private final long expireTime;
    private final TimeUnit timeUnit;
    private final int retryCount;
    private final long retryIntervalMs;

    private Long lockStartTime;

    // 构造函数增加重试次数和重试间隔参数
    public RedisDistributedLock(String lockKey,
                                long expireTime,
                                TimeUnit timeUnit,
                                int retryCount,
                                long retryIntervalMs) {
        this.lockKey = lockKey;
        this.lockValue = UUID.randomUUID().toString();
        this.expireTime = expireTime;
        this.timeUnit = timeUnit;
        this.retryCount = retryCount;
        this.retryIntervalMs = retryIntervalMs;
    }

    /**
     * 获取锁，支持重试机制
     * @return 锁的状态
     */
    public Boolean lock() {
        // 打印获取锁的信息
        log.info("尝试获取 Redis Lock: key={}, value={}, expireTime={}, timeUnit={}", lockKey, lockValue, expireTime, timeUnit);
        int attempts = 0;
        while (attempts < retryCount) {
            if (setIfAbsent()) {
                this.lockStartTime = System.currentTimeMillis();
                return true;  // 成功获取锁
            }
            attempts++;
            try {
                log.info("获取 Redis Lock 失败，Key={}，尝试第 {} 次，等待 {} 毫秒后重试", lockKey, attempts, retryIntervalMs);
                Thread.sleep(retryIntervalMs);  // 等待一段时间再重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;  // 重试次数用尽，返回锁获取失败
    }

    /**
     * 释放锁，原子操作
     */
    public void unlock() {
        String luaScript =
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                        "   return redis.call('del', KEYS[1]) " +
                        "else " +
                        "   return 0 " +
                        "end";
        try (Jedis jedis = RedisPoolService.getJedis()) {
            jedis.eval(luaScript, 1, lockKey, lockValue);
            long lockEndTime = System.currentTimeMillis();
            long duration = lockEndTime - lockStartTime;
            log.info("释放 Redis Lock: key={}, value={}, duration={}ms", lockKey, lockValue, duration);
        } catch (Exception e) {
            throw new RuntimeException("Failed to release lock", e);
        }
    }

    /**
     * 设置值，如果不存在则设置成功
     * @return true-设置成功 false-键已存在
     */
    public boolean setIfAbsent() {
        try (Jedis jedis = RedisPoolService.getJedis()) {
            // 使用 SETNX 命令实现类似 setIfAbsent 的功能
            Long result = jedis.setnx(lockKey, lockValue);
            if (result != null && result == 1) {
                // 如果设置成功，再设置过期时间
                if (TimeUnit.SECONDS.equals(timeUnit)) {
                    jedis.expire(lockKey, (int) expireTime);
                } else if (TimeUnit.MILLISECONDS.equals(timeUnit)) {
                    jedis.pexpire(lockKey, expireTime);
                }
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to set value with expiration", e);
        }
    }

}
