package com.dzpk.commission.repositories.mysql.impl;


import com.dzpk.commission.repositories.mysql.RoomTierDao;
import com.dzpk.commission.repositories.mysql.model.UserTribeRoomTier;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * RoomTierDaoImpl
 *
 * <AUTHOR>
 * @since 2025/6/2
 */
@Slf4j
public class RoomTierDaoImpl implements RoomTierDao {

    @Override
    public UserTribeRoomTier getUserTribeRoomTier(int tribeId, int userId) {
        String sql = "select " +
                "urt.tribe_id, " +
                "urt.user_id, " +
                "urt.tier_id, " +
                "rt.tier_name, " +
                "rt.value " +
                "from user_room_tier urt " +
                "left join room_tier rt on urt.tier_id = rt.id " +
                "where urt.user_id = ? and urt.tribe_id = ? ";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, userId);
            stmt.setInt(2, tribeId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return UserTribeRoomTier.builder()
                        .tribeId(rs.getInt("tribe_id"))
                        .userId(rs.getInt("user_id"))
                        .tierId(rs.getInt("tier_id"))
                        .tierName(rs.getString("tier_name"))
                        .value(rs.getInt("value"))
                        .build();
            } else {
                return null;
            }
        } catch (SQLException ex) {
            log.error("", ex);
        }
        return null;
    }

    @Override
    public Integer getRoomTierValue(int roomId) {
        String sql = "select rt.value from room_tier rt where id = (select gr.tier_id from group_room gr where gr.room_id = ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, roomId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("value");
            } else {
                return null;
            }
        } catch (SQLException ex) {
            log.error("", ex);
        }
        return null;
    }

}
