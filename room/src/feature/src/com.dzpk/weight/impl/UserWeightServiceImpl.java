package com.dzpk.weight.impl;

import com.dzpk.common.utils.Helper;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.db.dao.UserLevelDao;
import com.dzpk.db.imp.UserLevelDaoImp;
import com.dzpk.db.model.UserLevel;
import com.dzpk.dealer.Player;
import com.dzpk.insurance.HolderPool;
import com.dzpk.insurance.OutsCalculator;
import com.dzpk.record.PrevPlayer;
import com.dzpk.weight.IUserWeightService;
import com.dzpk.weight.bo.WeightFlopInterventionConfigBo;
import com.dzpk.weight.bo.WeightPreflopInterventionConfigBo;
import com.dzpk.weight.cache.IUserWeightCache;
import com.dzpk.weight.constant.EAllinInterventionType;
import com.dzpk.weight.constant.WeightCode;
import com.dzpk.weight.constant.WeightContent;
import com.dzpk.weight.flipcard.FlipCardCalculator;
import com.dzpk.weight.flipcard.FlipCardResult;
import com.dzpk.weight.model.*;
import com.dzpk.weight.poker.WeightingPokerUtils;
import com.dzpk.weight.poker.model.CalcResult;
import com.dzpk.weight.poker.model.CalWeightPlayer;
import com.dzpk.weight.repositories.mysql.IUserWeightDao;
import com.dzpk.weight.repositories.mysql.impl.UserWeightDaoImpl;
import com.dzpk.weight.repositories.mysql.model.UserWeight;
import com.dzpk.weight.repositories.mysql.model.UserWeightInterveneLog;
import com.google.common.collect.Lists;
import com.i366.constant.UserLevelCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.pocer.Pocer;
import com.i366.model.room.Room;
import com.i366.util.RoomUtil;
import org.apache.logging.log4j.Logger;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @deprecated
 */
public class UserWeightServiceImpl implements IUserWeightService {

    private Logger logger = LogUtil.getLogger(UserWeightServiceImpl.class);

    private Room room;

    public UserWeightServiceImpl(Room room) {
        this.room = room;
    }

    @Override
    public void bringInCheckUserWeight(int userId) {

        logger.debug("玩家带入检查是否需要加权,rid={},uid={}",room.getRoomId(),userId);

        IUserWeightCache userWeightCache = IUserWeightCache.getInstance();
        if(!userWeightCache.getSwitchConfig()){
            logger.debug("加权开关未开启");
            return;
        }

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        WeightPlayer weightPlayer = roomPlayer.getWeightPlayer();

        IUserWeightDao userWeightDao = new UserWeightDaoImpl();
        UserWeight userWeight = userWeightDao.getUserWeightByUserId(userId);
        if(null != userWeight){
            logger.debug("加权信息={}",userWeight.toString());

            if(userWeight.getWeightCode() != weightPlayer.getWeightCode().getValue()
                    || userWeight.isFinish() != weightPlayer.isFinished()){  //玩家的加权等级或者完成加权更改时均需要更新玩家在内存中的状态

                logger.debug("更新玩家加权信息");

                /**  获取加权信息配置  **/
                WeightPreflopInterventionConfigBo weightPreflopInterventionConfig = userWeightCache.getPreflopInterventionConfig();
                updateUserWeightInfo(weightPlayer,userWeight,weightPreflopInterventionConfig);
            }

            if(roomPlayer.getIsBringIn() == 0){ //每个牌局只有第一次带入才更新计数器
                logger.debug("更新前玩家带入牌局计数器={}",userWeight.getRoomCounter());
                weightPlayer.setRoomCounter(userWeight.getRoomCounter() + 1);
                logger.debug("更新后玩家带入牌局计数器={}",weightPlayer.getRoomCounter());
                userWeightDao.updateUserWeightByUserId(userId); //更新牌局计数器
            }
        }else{
            logger.debug("玩家不在加权名单中,不需要加权");
        }

    }


    @Override
    public PreFlopInterveneResult doPrepFlopIntervene() {
        logger.debug("计算翻前加权情况,rid={},房间手数={}",room.getRoomId(),room.getStage());
        PreFlopInterveneResult result = new PreFlopInterveneResult();

        IUserWeightCache userWeightCache = IUserWeightCache.getInstance();
        if(!userWeightCache.getSwitchConfig()){
            logger.debug("加权开关未开启");
            return result;
        }

        /** 1 检查该手是否有需要干预手牌的加权玩家 **/
        List<WeightPlayer> weightPlayerList = new ArrayList<>(); /** 加权玩家 **/
        List<WeightPlayer> allPlayerList = new ArrayList<>();  /** 所有非弃牌玩家 **/
        for(RoomPersion roomPersion:room.getRoomPersions()){
            if(null != roomPersion && roomPersion.getStatus() != -1){
                int userId = roomPersion.getUserId();
                WeightPlayer weightPlayer = room.getRoomPlayers().get(userId).getWeightPlayer();
                com.dzpk.dealer.Player player = room.getDealer().getPlayers().get(userId);

                if(weightPlayer.isWeighting() && player.getHandCnt() == weightPlayer.getNextInterventionHand()){
                    weightPlayerList.add(weightPlayer);
                }

                allPlayerList.add(weightPlayer);

            }
        }

        if(null == weightPlayerList || weightPlayerList.isEmpty()){
            logger.debug("未找到加权玩家,当手没有满足的加权玩家/没有加权玩家");
            return result;
        }

        if(!checkLowScoreAndOldPlayers(allPlayerList)){
            return result;
        }

        WeightPreflopInterventionConfigBo weightPreflopInterventionConfig = userWeightCache.getPreflopInterventionConfig();

        /** 2 找到符合牌局计数器的加权玩家 **/
        List<Integer> roomCounterList = weightPreflopInterventionConfig.getRoomNos();
        logger.debug("符合的牌局计数器={}",roomCounterList);
        weightPlayerList = weightPlayerList.stream().
                filter(weightPlayer -> roomCounterList.contains(weightPlayer.getRoomCounter())).
                collect(Collectors.toList());

        if(null == weightPlayerList || weightPlayerList.isEmpty()){
            logger.debug("未找到符合牌局计数器的加权玩家");
            return result;
        }

        /** 3 按照加权等级排序后选出一个加权玩家 有多个先按照层级降序筛选，同层级的则随机筛选 这里的层级指的是用户分值 **/
        weightPlayerList.sort(Comparator.comparing(WeightPlayer::getUserScore));
        WeightPlayer weightPlayer = weightPlayerList.stream().findFirst().orElse(null);

        /** 4再次检查该玩家的加权状态 如果不满足则不干预 **/
        boolean checkWeight = sendCardCheckUserWeight(weightPlayer.getUserId());
        logger.debug("玩家发牌前检查加权状态,checkWeight={}",checkWeight);

        if(checkWeight){
            logger.debug("满足翻牌前干预条件,uid={},rid={}",weightPlayer.getUserId(),room.getRoomId());
            Player player = room.getDealer().getPlayers().get(weightPlayer.getUserId());

            if(player.getHandCnt() >= weightPreflopInterventionConfig.getEndHand()){
                logger.debug("当前玩家的手数已经大于干预最大手数,不干预");
                return result;
            }

            Pocer[] interveneHand = userWeightCache.getPreFlopHandCards();

            if(null != interveneHand){
                for(Pocer pocer:interveneHand){
                    logger.debug("干预的牌为={}",pocer.getName() + pocer.getSize2());
                }
            }

            result.setResultCode(1);
            result.setUserId(weightPlayer.getUserId());
            result.setHandPocer(interveneHand);

            weightPlayer.setPreFlopInterventionTimes(weightPlayer.getPreFlopInterventionTimes() + 1);
            /** 5 记录加权信息 **/
            recordInterneneInfo(room,WeightContent.PREFLOP,result.getHandPocer(),weightPlayer.getUserId(),weightPlayer.getPreFlopInterventionTimes(),weightPlayer.getWeightCode());

            /** 6 设置下一手干预手数 **/
            if(weightPlayer.getPreFlopInterventionTimes() < weightPreflopInterventionConfig.getInterveneTimes()){
                weightPlayer.setNextInterventionHand(getNextRandomInterveneHand(player.getHandCnt(),weightPreflopInterventionConfig.getEndHand()));
                logger.debug("当前翻前干预次数={},下一次干预手数={}",weightPlayer.getPreFlopInterventionTimes(),weightPlayer.getNextInterventionHand());
            }else{
                logger.debug("当前翻前干预次数已到达最大值={}",weightPlayer.getPreFlopInterventionTimes());
            }

        }

        return result;
    }

    @Override
    public AfterFlopInterveneResult doAfterFlopIntervene() {
        logger.debug("计算翻后加权情况,rid={},房间手数={}",room.getRoomId(),room.getStage());
        AfterFlopInterveneResult result = new AfterFlopInterveneResult();

        IUserWeightCache userWeightCache = IUserWeightCache.getInstance();
        if (room.getControlPublicCards().size()>0){
            logger.debug("有超级用户干预牌，加权无法启动，加权小于超级用户");
            return result;
        }
        if(!userWeightCache.getSwitchConfig()){
            logger.debug("加权开关未开启");
            return result;
        }

        /** 2 获取一个随机的加权玩家 如果翻前已有加权玩家则直接使用 **/
        int interveneUserId = findWeightPlayer(room.getInsuranceActive());
        if(interveneUserId == -1){
            logger.debug("未找到加权玩家,不干预");
            return result;
        }

        /** 3再次检查该玩家的加权状态 如果不满足则不干预 **/
        boolean checkWeight = sendCardCheckUserWeight(interveneUserId);
        logger.debug("玩家发牌前检查加权状态,checkWeight={}",checkWeight);

        if(checkWeight){

            /** 是否是领先玩家 **/
            boolean isAllinWin = false;
            if(null != room.getInsurer().getHolderByUserId(interveneUserId)){  //投保人列表中有 说明属于领先玩家
                logger.debug("加权玩家属于领先玩家");
                isAllinWin = true;
            }

            EAllinInterventionType eAllinInterventionType = EAllinInterventionType.turn;
            if(room.getInsuranceActive()){
                eAllinInterventionType = isAllinWin == true ? EAllinInterventionType.allin_win: EAllinInterventionType.allin_lose;
            }

            logger.debug("干预的玩家id={},干预阶段={}",interveneUserId,eAllinInterventionType.desc());

            WeightFlopInterventionConfigBo weightFlopInterventionConfig = userWeightCache.getFlopInterventionConfigBo(eAllinInterventionType);

            WeightPlayer weightPlayer = room.getRoomPlayers().get(interveneUserId).getWeightPlayer();
            int flopInterventionTimes = weightPlayer.getFlopInterventionTimes();
            if(flopInterventionTimes >= weightFlopInterventionConfig.getInterventions().size()){
                logger.debug("当前翻后干预次数已到达最大值={}",flopInterventionTimes);
                return result;
            }

            /** 4是否处于allin状态 **/
            if(room.getInsuranceActive()){
                result = interveneAllin(weightFlopInterventionConfig,interveneUserId);
            }else{
                result = interveneNotAllin(weightFlopInterventionConfig,interveneUserId);
            }

            logger.debug("干预结果={}",result.toString());
            /** 5记录干预的玩家id **/
            if(result.getResultCode() == 1){
                int publibPocerNum = RoomUtil.getPublicCardNum(room);
                if(publibPocerNum == 3){

                    WeightContent weightContent = WeightContent.TURN;
                    if(room.getInsuranceActive()){
                        weightContent = isAllinWin == true ? WeightContent.ALLIN_WIN:  WeightContent.ALLIN_LOSE;
                    }

                    recordInterneneInfo(room,weightContent,result.getHandPocer(),interveneUserId,weightPlayer.getFlopInterventionTimes(),weightPlayer.getWeightCode());
                }else if(publibPocerNum == 4){
                    WeightContent weightContent = WeightContent.RIVER;
                    if(room.getInsuranceActive()){
                        weightContent = isAllinWin == true ? WeightContent.ALLIN_WIN:  WeightContent.ALLIN_LOSE;
                    }

                    recordInterneneInfo(room,weightContent,result.getHandPocer(),interveneUserId,weightPlayer.getFlopInterventionTimes(),weightPlayer.getWeightCode());
                }

                Pocer pocer = result.getHandPocer()[0];
                logger.debug("满足翻牌后干预条件,干预的牌为={}",pocer.getName() + pocer.getSize2());
            }

            weightPlayer.setFlopInterventionTimes(weightPlayer.getFlopInterventionTimes() + 1); //不管是否干预 干预次数都需要加1
        }

        return result;
    }

    @Override
    public FlopInterveneResult doFlopIntervene() {
        logger.debug("计算翻牌加权情况,rid={},房间手数={}",room.getRoomId(),room.getStage());
        FlopInterveneResult result = new FlopInterveneResult();

        IUserWeightCache userWeightCache = IUserWeightCache.getInstance();
        if (room.getControlPublicCards().size()>0){
            logger.debug("有超级用户干预牌，加权无法启动，加权小于超级用户");
            return result;
        }
        if(!userWeightCache.getSwitchConfig()){
            logger.debug("加权开关未开启");
            return result;
        }

        WeightFlopInterventionConfigBo weightFlopInterventionConfig = userWeightCache.getFlopInterventionConfigBo();

        int interveneUserId = findWeightPlayer(room.getInsuranceActive());
        if(interveneUserId == -1){
            logger.debug("未找到加权玩家,不干预");
            return result;
        }

        /** 判断是否需要干预 **/
        if(!checkIfIntervene(room,weightFlopInterventionConfig,interveneUserId)){
            return result;
        }

        /** 再次检查该玩家的加权状态 如果不满足则不干预 **/
        boolean checkWeight = sendCardCheckUserWeight(interveneUserId);
        logger.debug("玩家发牌前检查加权状态,checkWeight={}",checkWeight);

        if(checkWeight){

            WeightPlayer weightPlayer = room.getRoomPlayers().get(interveneUserId).getWeightPlayer();
            logger.debug("干预的玩家id={},干预阶段=翻牌,干预次数={}",interveneUserId,weightPlayer.getFlopInterventionTimes());
            int flopInterventionTimes = weightPlayer.getFlopInterventionTimes();
            if(flopInterventionTimes >= weightFlopInterventionConfig.getInterventions().size()){
                logger.debug("当前翻牌干预次数已到达最大值={}",flopInterventionTimes);
                return result;
            }

            RoomPersion roomPersion = room.getAudMap().get(interveneUserId);
            FlipCardResult flipCardResult = FlipCardCalculator.calculate(Arrays.asList(roomPersion.getPocers()),room.getPocerLink().getUnSendCards());

            if(flipCardResult.getSuccess()){

                Pocer[] interveneHand = flipCardResult.getPokers().stream().toArray(Pocer[]::new);
                if(null != interveneHand){
                    for(Pocer pocer:interveneHand){
                        logger.debug("干预的牌为={}",pocer.getName() + pocer.getSize2());
                    }
                }

                result.setResultCode(1);
                result.setHandPocer(interveneHand);

                /** 记录加权信息 **/
                recordInterneneInfo(room,WeightContent.FLOP,result.getHandPocer(),interveneUserId,weightPlayer.getFlopInterventionTimes(),weightPlayer.getWeightCode());
            }

            weightPlayer.setFlopInterventionTimes(weightPlayer.getFlopInterventionTimes() + 1);
        }

        return result;
    }

    @Override
    public void saveInterneneRecord() {


        //最多可能保存4个 手牌  翻牌 转牌 河牌
        IUserWeightDao userWeightDao = new UserWeightDaoImpl();

        UserWeightRecord userWeightRecord = room.getUserWeightRecord();
        if(userWeightRecord.getWeightInterveneRecordMap().size() > 0){
            logger.debug("记录本手干预信息");
            userWeightRecord.getWeightInterveneRecordMap().values().stream()
                    .filter( interveneRecord -> interveneRecord.getInterveneUserId() > -1)
                    .forEach(interveneRecord -> saveEachStepInterneneRecord(userWeightDao,interveneRecord));
        }

    }


    /**
     * 记录每个阶段的干预信息
     * @param userWeightDao
     * @param interveneRecord
     */
    private void saveEachStepInterneneRecord(IUserWeightDao userWeightDao, InterveneRecord interveneRecord){
        int interveneUserId = interveneRecord.getInterveneUserId();
        logger.debug("记录干预信息,rid={},uid={}",room.getRoomId(),interveneUserId);
        WeightPlayer weightPlayer = room.getRoomPlayers().get(interveneUserId).getWeightPlayer();
        PrevPlayer prevPlayer = room.getRoomRecord().getCurrentGame().getPrevPlayers().get(interveneUserId);
        com.dzpk.dealer.Player player = room.getDealer().getPlayers().get(interveneUserId);

        UserWeightInterveneLog userWeightInterveneLog = new UserWeightInterveneLog();
        /** 基本信息 **/
        userWeightInterveneLog.setRoomId(room.getRoomId());
        userWeightInterveneLog.setRoomName(room.getName());
        userWeightInterveneLog.setRoomPath(room.getRoomPath());
        userWeightInterveneLog.setUserId(interveneUserId);
        userWeightInterveneLog.setRoomHandNo(room.getStage());  //房间总手数
        userWeightInterveneLog.setUserHandNo(player.getHandCnt()); //玩家当前手数
        userWeightInterveneLog.setHandPl(prevPlayer.getWinChip()); //玩家当手盈亏

        /** 干预信息 **/
        userWeightInterveneLog.setRoomNo(weightPlayer.getRoomCounter()); //玩家的牌局计数器
        userWeightInterveneLog.setInterventionNum(interveneRecord.getInterventionNum()); //翻牌后干预次数
        userWeightInterveneLog.setInterventionType(interveneRecord.getWeightContent().getValue()); //加权内容
        userWeightInterveneLog.setWeightingType(interveneRecord.getInterventionType());  //加权类型
        userWeightInterveneLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
        //userWeightInterveneLog.setInterventionTime(new Date(System.currentTimeMillis()));
        userWeightInterveneLog.setInterventionPocers(interveneRecord.getInterventionPocer()); //干预的牌型

        userWeightDao.insertUserWeightInterveneLog(userWeightInterveneLog);
    }


    /**
     * 发牌前检查玩家的加权状态 如果处于加权状态 才往下继续走逻辑
     * @param userId
     * @return
     */
    private boolean sendCardCheckUserWeight(int userId) {
        logger.debug("玩家发牌前检查加权状态,rid={},uid={}",room.getRoomId(),userId);

        IUserWeightCache userWeightCache = IUserWeightCache.getInstance();
        if(!userWeightCache.getSwitchConfig()){
            logger.debug("加权开关未开启");
            return false;
        }

        IUserWeightDao userWeightDao = new UserWeightDaoImpl();
        UserWeight userWeight = userWeightDao.getUserWeightByUserId(userId);
        if(null != userWeight){
            logger.debug("加权信息={}",userWeight.toString());
            WeightPlayer weightPlayer = room.getRoomPlayers().get(userId).getWeightPlayer();
            if(userWeight.getWeightCode() != weightPlayer.getWeightCode().getValue()
                    || userWeight.isFinish() != weightPlayer.isFinished()){  //玩家的加权等级或者完成加权更改时均需要更新玩家在内存中的状态

                /**  获取加权信息配置  **/
                WeightPreflopInterventionConfigBo weightPreflopInterventionConfig = userWeightCache.getPreflopInterventionConfig();
                updateUserWeightInfo(weightPlayer,userWeight,weightPreflopInterventionConfig);
            }

            /** 再次检查玩家的加权状态时,如果玩家的加权状态为永久注销加权或者加权状态已经完成的话,不能够进行加权**/
            if(userWeight.getWeightCode() == WeightCode.FOREVER_CANCEL.getValue() || userWeight.isFinish()){ //玩家加权状态需要是非完成
                logger.debug("玩家的加权状态为永久注销加权或者加权状态已经完成的话,不能够进行加权");
                weightPlayer.setWeighting(false);
                return false;
            }

            return true;
        }else{
            logger.debug("玩家不在加权名单中,不需要加权");
            return false;
        }

    }

    /**
     * 获取下一次随机干预手数
     * @param nowHand
     * @param maxHand
     * @return
     */
    private int getNextRandomInterveneHand(int nowHand,int maxHand){

        int nextHand = 0;
        if(nowHand > maxHand){
            logger.debug("获取随机干预手数,当前手数已经大于随机最大手数,nowHand = {} , maxHand = {}", nowHand , maxHand);
            return nextHand;
        }

        nextHand = Helper.randomIntBy(nowHand+1,maxHand);

        logger.debug("生成的随机干预手数={}",nextHand);
        return nextHand;
    }

    /**
     * 更新玩家加权信息
     * @param weightPlayer
     * @param userWeight
     */
    private void updateUserWeightInfo(WeightPlayer weightPlayer,UserWeight userWeight,WeightPreflopInterventionConfigBo weightPreflopInterventionConfig){

        weightPlayer.setWeightCode(WeightCode.valueOf(userWeight.getWeightCode()));
        weightPlayer.setUserScore(userWeight.getUserScore());
        weightPlayer.setFinished(userWeight.isFinish());
        if(userWeight.getWeightCode() != WeightCode.FOREVER_CANCEL.getValue() && !userWeight.isFinish()){ //玩家加权状态需要是非完成
            weightPlayer.setWeighting(true);
            weightPlayer.setNextInterventionHand(getNextRandomInterveneHand(weightPreflopInterventionConfig.getBeginHand(),weightPreflopInterventionConfig.getEndHand()));
        }
    }

    /**
     * 记录干预信息
     * @param room
     * @param weightContent
     * @param pocers
     * @param interveneUserId
     */
    private void recordInterneneInfo(Room room,WeightContent weightContent,
                                     Pocer[] pocers,int interveneUserId,int interveneNum,WeightCode weightCode){

        UserWeightRecord userWeightRecord = room.getUserWeightRecord();
        InterveneRecord interveneRecord = new InterveneRecord();

        String interventionPocer = "";
        for(Pocer pocer:pocers){
            interventionPocer += pocer.getSize1() + ",";
            room.getPocerLink().getIntervenenPocerSet().add(pocer.getSize1());
        }

        interveneRecord.setInterventionPocer(interventionPocer);
        interveneRecord.setWeightContent(weightContent);
        interveneRecord.setInterveneUserId(interveneUserId);
        interveneRecord.setInterventionNum(interveneNum);
        interveneRecord.setInterventionType(weightCode.getValue());
        userWeightRecord.getWeightInterveneRecordMap().put(weightContent,interveneRecord);

    }


    /**
     * 干预allin的情况
     *      当前的ALL IN只有一个加权玩家
     *      当前的底池大于等于50BB
     *      加权的玩家处于领先的一方
     * @return
     */
    private AfterFlopInterveneResult interveneAllin(WeightFlopInterventionConfigBo weightFlopInterventionConfig, int userId){
        AfterFlopInterveneResult result = new AfterFlopInterveneResult();

        /** 判断是否需要干预 **/
        if(!checkIfIntervene(room,weightFlopInterventionConfig,userId)){
            return result;
        }

        HolderPool holderPool = null;
        List<HolderPool> holderPoolList = room.getDealer().getHolderPoolList();
        if(null != holderPoolList && holderPoolList.size() > 0){
            for(HolderPool pool:holderPoolList){
                logger.debug("底池的筹码={},底池人数={}",pool.getPoolChip(),pool.getUserIds().size());
                if(pool.getUserIds().size() == 2){ //选出只有2个人的底池
                    holderPool = pool;
                }
            }
        }


        if(null == holderPool){
            logger.debug("outs等于0不进行干预");
            return result;
        }else{

            logger.debug("底池中的玩家id={}",holderPool.getUserIds());
            if(!holderPool.getUserIds().contains(userId)){
                logger.debug("该底池中不存在加权玩家,不进行干预");
                return result;
            }

            int totalPlayer = holderPool.getUserIds().size();
            if(totalPlayer == 3){
                logger.debug("保险池玩家大于2人不干预");
                return result;
            }

            /** 是否是领先玩家 **/
            boolean isAllinWin = false;
            if(null != room.getInsurer().getHolderByUserId(userId)){  //投保人列表中有 说明属于领先玩家
                logger.debug("加权玩家属于领先玩家");
                isAllinWin = true;
            }
            Pocer pocer = room.getPocerLink().allinInterveneNextPocer(holderPool,isAllinWin,userId,room);
            result.setResultCode(1);
            result.setHandPocer(new Pocer[]{pocer});
        }

        return result;
    }

    /**
     * 干预非allin的情况
     *      底池大于等于35BB
     *      有需要干预玩家，以下任一条件
     *      有且只有一个加权玩家
     *      有多个加权玩家，但其中有且只有一个玩家的层级比其他高（即多个最高同层级玩家不会干预）
     * @return
     */
    private AfterFlopInterveneResult interveneNotAllin(WeightFlopInterventionConfigBo weightFlopInterventionConfig, int userId){
        AfterFlopInterveneResult result = new AfterFlopInterveneResult();

        if(!checkIfIntervene(room,weightFlopInterventionConfig,userId)){
            return result;
        }

        /** 获取已发的牌 手牌+公共牌+未发的牌 **/
        List<CalWeightPlayer> playerList = new ArrayList<>();
        for(RoomPersion roomPersion:room.getRoomPersions()){
            if(null != roomPersion && roomPersion.getStatus() > 0){
                CalWeightPlayer player = new CalWeightPlayer();
                if(roomPersion.getUserId() == userId){  //此时已经确定唯一玩家
                    player.setWeighting(true);
                }

                if(null != roomPersion.getPocers()){
                    player.setHandPokers(Arrays.asList(roomPersion.getPocers()));
                }
                playerList.add(player);
            }
        }

        List<Pocer> publicPocerList = new ArrayList<>();
        for(Pocer pocer:room.getPocer()){
            if(null != pocer){
                publicPocerList.add(pocer);
            }
        }

        logger.debug("非allin翻后干预获取公共牌");
        CalcResult calcResult = WeightingPokerUtils.calcCard(playerList,publicPocerList,room.getPocerLink().getUnSendCards());

        if(calcResult.getStatus() > 0){

            Pocer pocer = calcResult.getPoker();
            if(calcResult.getStatus() == 1){  //走保险逻辑不发outs
                pocer = getIntervenePocerInNoOuts();
            }

            result.setResultCode(1);
            result.setHandPocer(new Pocer[]{pocer});
        }

        return result;
    }

    /**
     * 检查配置表判断是否要干预
     *    按其他玩家维度区分
     *      有且只有一个黑名单玩家
     *      其他情况
     * @param userId
     * @param weightFlopInterventionConfigBo
     * @return
     */
    private boolean checkIfIntervene(Room room,WeightFlopInterventionConfigBo weightFlopInterventionConfigBo,int userId){
        logger.debug("检查配置表判断是否要干预,uid={}",userId);
        boolean isIntervene = false; //默认不干预


        /**  计算底池是否满足 **/
        int totalPoolChip = 0;
        Integer[] poolChips = room.getRoomService().getPoolInfo(true);//计算总底池积分
        for(int chip : poolChips){
            totalPoolChip += chip;
        }

        if(totalPoolChip <= weightFlopInterventionConfigBo.getPotXBB() * room.getDamanzhu()){
            logger.debug("底池不满足大于{}BB,当前底池={},大盲={}",weightFlopInterventionConfigBo.getPotXBB(),totalPoolChip,room.getDamanzhu());
            return isIntervene;
        }

        Set<Integer> userIdSets = new HashSet<>();
        for(RoomPersion roomPersion:room.getRoomPersions()){
            if(null != roomPersion && roomPersion.getStatus() > 0 && roomPersion.getUserId() != userId){
                userIdSets.add(roomPersion.getUserId());
            }
        }

        UserLevelDao userLevelDao = new UserLevelDaoImp();
        List<UserLevel> userLevelList = userLevelDao.getNameList(userIdSets);
        if(null!= userLevelList && !userLevelList.isEmpty()){

            boolean useBlack = false;
            if(userLevelList.size() == 1 && userLevelList.get(0).getLevelCode() == UserLevelCode.BLACK.getCode()){ //有且只有一个黑名单玩家 单挑
                logger.debug("未弃牌玩家有且只有一个为黑名单玩家");
                useBlack = true;
            }

            int inverveneNum = room.getRoomPlayers().get(userId).getWeightPlayer().getAfterFlopInterventionTimes();
            logger.debug("当前翻后干预次数={},是否为唯一黑名单玩家={}",inverveneNum,useBlack);
            isIntervene = weightFlopInterventionConfigBo.randomInvervene(inverveneNum,useBlack);
        }

        return isIntervene;
    }


    /**
     * 加权玩家处于领先,从非outs中发牌
     * @return
     */
    private Pocer getIntervenePocerInNoOuts(){

        logger.debug("加权玩家为当前最大牌型,走不发outs逻辑");
        Set<Integer> notOuts = new HashSet<>();
        List<RoomPersion> roomPersionList = new ArrayList<>();

        for(RoomPersion roomPersion:room.getRoomPersions()){
            if(null != roomPersion && roomPersion.getStatus() > 0){

                if(null != roomPersion.getPocers()){
                    for(Pocer pocer:roomPersion.getPocers()){
                        notOuts.add(pocer.getSize1());
                    }
                }

                roomPersionList.add(roomPersion);
            }
        }

        List<Pocer> publicPocerList = new ArrayList<>();
        for(Pocer pocer:room.getPocer()){
            if(null != pocer){
                notOuts.add(pocer.getSize1());
                publicPocerList.add(pocer);
            }
        }

        Map<Integer, Set<Integer>> outsMap = OutsCalculator.getOuts((roomPersionList.stream().toArray(RoomPersion[]::new)),
                publicPocerList.stream().toArray(Pocer[]::new),notOuts,true);
        return room.getPocerLink().normalInterveneNextPocer(outsMap.keySet());
    }

    /**
     * 检查未弃牌玩家中是否有低分且老玩家
     * @param allPlayerList
     * @return
     */
    private boolean checkLowScoreAndOldPlayers(List<WeightPlayer> allPlayerList){

        /** 参与用户全部为高分(6-9分)/新用户则不干预，即至少有一个非高分用户并且非新用户 **/
        allPlayerList.stream()
                .filter(weightPlayer -> null != weightPlayer.getWeightCode())
                .forEach(weightPlayer -> logger.debug("玩家id={},积分={},等级={}",weightPlayer.getUserId(),weightPlayer.getUserScore(),weightPlayer.getWeightCode().getValue()));

        long lowScoreAndNotNewCount = allPlayerList.stream()
                .filter(weightPlayer -> weightPlayer.getUserScore() < 6)
                .filter(weightPlayer -> null != weightPlayer.getWeightCode())
                .filter(weightPlayer -> weightPlayer.getWeightCode() != WeightCode.NEW_REGISTER)
                .count();

        logger.debug("低分玩家且老玩家={}",lowScoreAndNotNewCount);
        if(lowScoreAndNotNewCount <= 0){
            logger.debug("参与用户全部为高分(6-9分)/新用户则不干预");
            return false;
        }

        return true;
    }

    /**
     * 获取一个随机的加权玩家 如果翻前已有加权玩家且未弃牌则直接使用
     * 翻牌及翻后使用
     *
     * @return -1未找到加权玩家  否则未加权玩家的id
     */
    private int findWeightPlayer(boolean isAllin){

        UserWeightRecord userWeightRecord = room.getUserWeightRecord();

        int interveneUserId = -1;
        if(null != userWeightRecord.getWeightInterveneRecordMap().get(WeightContent.PREFLOP)){
            interveneUserId = userWeightRecord.getWeightInterveneRecordMap().get(WeightContent.PREFLOP).getInterveneUserId(); //翻牌前有加权玩家的话 则翻牌仍为该玩家
        }

        if(interveneUserId == -1){

            List<WeightPlayer> weightPlayerList = new ArrayList<>(); /** 加权玩家 **/
            List<WeightPlayer> allPlayerList = new ArrayList<>();  /** 所有非弃牌玩家 **/
            for(RoomPersion roomPersion:room.getRoomPersions()){
                if(null != roomPersion && roomPersion.getStatus() > 0){
                    RoomPlayer roomPlayer = room.getRoomPlayers().get(roomPersion.getUserId());
                    if(roomPlayer.getWeightPlayer().isWeighting()){
                        weightPlayerList.add(roomPlayer.getWeightPlayer());
                    }

                    allPlayerList.add(roomPlayer.getWeightPlayer());
                }
            }

            if(null == weightPlayerList || weightPlayerList.isEmpty()){
                logger.debug("未找到加权玩家");
                return interveneUserId;
            }

            if(isAllin && weightPlayerList.size() > 1){
                logger.debug("allin状态且加权玩家数量大于1,不进行干预");
                return interveneUserId;
            }

            if(!checkLowScoreAndOldPlayers(allPlayerList)){
                return interveneUserId;
            }

            interveneUserId = findOnlyHigestWeightPlayer(weightPlayerList);

            if(interveneUserId == -1){
                logger.debug("存在多个最高同层级玩家,不干预");
            }
        }else {
            logger.debug("翻牌前已经有玩家干预,uid={}",interveneUserId);
            RoomPersion roomPersion = room.getAudMap().get(interveneUserId);
            if(roomPersion.getStatus() == -3 || roomPersion.getStatus() == -1){
                interveneUserId = -1;
                logger.debug("翻前干预的玩家已经弃牌,同一手只干预一个玩家,不进行干预");
            }
        }


        logger.debug("选出的干预玩家为={}",interveneUserId);

        return interveneUserId;

    }

    /**
     * 获取唯一最高层级的加权玩家
     * 有多个加权玩家，但其中有且只有一个玩家的层级比其他高（即多个最高同层级玩家不会干预） 这里的层级指的是用户分值
     * @param weightPlayerList
     * @return
     */
    private int findOnlyHigestWeightPlayer(List<WeightPlayer> weightPlayerList){

        int interveneUserId = -1;

        Map<Integer, Long> weightPlayerMap =
                weightPlayerList.stream().collect(Collectors.groupingBy(weightPlayer -> weightPlayer.getUserScore(), Collectors.counting()));

        int highestScore = weightPlayerMap.keySet().stream().
                sorted(Comparator.comparing(Integer::intValue).reversed()).findFirst().orElse(null);

        if(null != weightPlayerMap.get(highestScore) && weightPlayerMap.get(highestScore) == 1){
            WeightPlayer weightPlayer = weightPlayerList.stream()
                    .filter(player -> player.getUserScore() == highestScore).findFirst().orElse(null);
            interveneUserId = weightPlayer.getUserId();
        }

        return interveneUserId;
    }
}
