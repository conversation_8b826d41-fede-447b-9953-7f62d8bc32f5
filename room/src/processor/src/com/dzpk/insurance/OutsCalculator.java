package com.dzpk.insurance;

import com.i366.room.BiPai;
import com.i366.model.player.RoomPersion;
import com.i366.model.pocer.Pocer;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

/**
 * outs计算器
 * Created by baidu on 16/8/31.
 *
 * <AUTHOR>
 */
public class OutsCalculator {
    private static final Logger logger = LogUtil.getLogger(OutsCalculator.class);

    /**
     * 枚举计算outs
     *
     * @param rps
     * @param pubPocers
     * @param notOuts
     * @param userOutsMap
     */
    public static Map<Integer, Set<Integer>> getOuts(RoomPersion[] rps, Pocer[] pubPocers, Set<Integer> notOuts, Map<Integer, Set<Integer>> userOutsMap) {

        Map<Integer, Set<Integer>> map = new HashMap<>();
        Set<Integer> knowCards = new HashSet<>();


        // 目前翻了几张公共牌 (flop=3, turn=4)
        int pubSize = 0;
        for (Pocer p : pubPocers) {
            if (p != null) {
                pubSize++;
            }
        }

        // 先找出目前参与比牌用户中最大手牌的人
        int size = 0;
        for (RoomPersion rp : rps) {
            if (rp.getStatus() > 0) {
                Pocer[] pocer = new Pocer[pubSize + 2];

                // 公共牌
                for (int j = 0; j < pubSize; j++) {
                    pocer[j] = pubPocers[j];
                    knowCards.add(pubPocers[j].getSize1());
                }

                // 加上玩家自己的手牌
                pocer[pubSize] = rp.getPocers()[0];
                pocer[pubSize + 1] = rp.getPocers()[1];
                knowCards.add(rp.getPocers()[0].getSize1());
                knowCards.add(rp.getPocers()[1].getSize1());

                Object[] obj = BiPai.zuidapai1(pocer);
                rp.setZuidaPocers((Pocer[]) obj[0]);
                rp.setPocerType((Integer) obj[1]);
                size++;
            }
        }

        RoomPersion[] rps2 = new RoomPersion[size];
        int z = 0;
        for (int i = 0; i < rps.length; i++) {
            if (rps[i].getStatus() > 0) {
                rps2[i] = rps[z++];
            }
        }

        // 冒泡 由大到小
        for (int i = 0; i < rps2.length; i++) {
            for (int j = 0; j < rps2.length - i - 1; j++) {
                if (BiPai.bipai2(rps2[j], rps2[j + 1]) == 1) {
                    swap(rps2, j, j + 1);
                }
            }
        }

        // 最大牌用户 可能有多个一样大
        int sameRankCount = rps2.length;
        for (int i = 0; i < rps2.length - 1; i++) {
            if (BiPai.bipai2(rps2[i], rps2[i + 1]) == 0) {
                sameRankCount = i + 1;
                break;
            }
        }

        for (int i = 0; i < sameRankCount; i++) {
            logger.debug("max card user:{}", rps2[i].getUserId());
        }

        // 列出目前还剩余哪些未知的牌(包括弃牌用户的牌)
        Set<Integer> maybeOutCards = new HashSet<>();
        for (int k = 0; k <= 51; k++) {
            if (!knowCards.contains(k)) {
                maybeOutCards.add(k);
            }
        }

        // 枚举剩余的牌型 遍历其他用户 列出导致最大牌用户outs的牌 合并outs
        for (int y = 0; y < sameRankCount; y++) {
            RoomPersion roomPersionY = rps2[y];
            Set<Integer> outs = new HashSet<>();
            Set<Integer> eqOuts = new HashSet<>();
            for (RoomPersion roomPersionX : rps2) {
                if (roomPersionX.getUserId() == roomPersionY.getUserId()) {
                    continue;
                }

                for (Integer card : maybeOutCards) {
                    Pocer newPocer = new Pocer(card);
                    Pocer[] xpocers = new Pocer[pubSize + 3];       //  当前用户的手牌(包括公共牌)
                    Pocer[] ypocers = new Pocer[pubSize + 3];       //  最大牌用户的手牌(包括公共牌)
                    for (int j = 0; j < pubSize; j++) {
                        xpocers[j] = pubPocers[j];
                        ypocers[j] = pubPocers[j];
                    }
                    xpocers[pubSize] = newPocer;
                    ypocers[pubSize] = newPocer;

                    xpocers[pubSize + 1] = roomPersionX.getPocers()[0];
                    xpocers[pubSize + 2] = roomPersionX.getPocers()[1];
                    Object[] xObj = BiPai.zuidapai1(xpocers);

                    ypocers[pubSize + 1] = roomPersionY.getPocers()[0];
                    ypocers[pubSize + 2] = roomPersionY.getPocers()[1];
                    Object[] yObj = BiPai.zuidapai1(ypocers);

                    RoomPersion xrp = new RoomPersion();
                    xrp.setZuidaPocers((Pocer[]) xObj[0]);
                    xrp.setPocerType((Integer) xObj[1]);

                    RoomPersion yrp = new RoomPersion();
                    yrp.setZuidaPocers((Pocer[]) yObj[0]);
                    yrp.setPocerType((Integer) yObj[1]);

                    int res = BiPai.bipai2(xrp, yrp);
                    int out = newPocer.getSize1();
                    if (res == 0) {
                        // 對手反超
                        if (!notOuts.contains(out)) {
                            outs.add(out);
                            addUserOutsCount(roomPersionX.getUserId(), out, userOutsMap);
                        }
                    } else if (res == -1) {  //-1表示牌力相同
                        eqOuts.add(out);
                        roomPersionY.setEqualOuts(true);
                    }
                }
            }

            logger.debug("user:{} outs({}):{} eqOuts({}):{}", roomPersionY.getUserId(),
                    outs.size(), outs.stream().map(Pocer::new).collect(Collectors.toList()),
                    eqOuts.size(), eqOuts.stream().map(Pocer::new).collect(Collectors.toList()));

            map.put(roomPersionY.getUserId(), outs);
        }

        return map;
    }

    /**
     * @deprecated
     * @param rps
     * @param pubPocers
     * @param notOuts
     * @param isIntervent
     * @return
     */
    public static Map<Integer, Set<Integer>> getOuts(RoomPersion[] rps, Pocer[] pubPocers, Set<Integer> notOuts,boolean isIntervent) {
        return getOuts(rps, pubPocers, notOuts, new HashMap<>());
    }

    /**
     * 换位
     *
     * @param array
     * @param i
     * @param j
     */
    private static void swap(Object[] array, int i, int j) {
        Object temp;
        temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    /**
     * 递增某个人的outs数
     *
     * @param uid
     */
    private static void addUserOutsCount(int uid, int out, Map<Integer, Set<Integer>> userOutsMap) {
        if (userOutsMap.containsKey(uid)) {
            userOutsMap.get(uid).add(out);
        } else {
            Set<Integer> set = new HashSet<>();
            set.add(out);
            userOutsMap.put(uid, set);
        }
    }
}
