package com.dzpk.insurance;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 投保人信息
 */
public class Holder {

    private Logger logger = LogUtil.getLogger(Holder.class);

    private long endTime;                                                                   //  投保人操作剩余时间
    private int insuranceChip = 0;                                                          //  该手已投保险总额(多个分池)
    private List<HolderPool> holderPools = new ArrayList<HolderPool>();                     //  投保人所参与的分池
    private int userId;                                                                     //  userid
    private int status;                                                                     //  投保状态 0投保中 1已投保
    private int seat;                                                                       //  座位号
    private int insuranceDelayTimes = 0;                          //投保人保险延时次数(转牌、河牌单独统计,即每发一张牌需要重置)
    private boolean equalOuts = false;           //河牌购买保险时是否存在平分Outs

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getUserId() {
        return userId;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setInsuranceChip(int insuranceChip) {
        this.insuranceChip = insuranceChip;
    }

    public int getInsuranceChip() {
        return insuranceChip;
    }

    public void addHolderPool(HolderPool holderPool) {
        holderPools.add(holderPool);
    }

    public List<HolderPool> getHolderPools() {
        return holderPools;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setSeat(int seat) {
        this.seat = seat;
    }

    public int getSeat() {
        return seat;
    }

    public int getInsuranceDelayTimes() {
        return insuranceDelayTimes;
    }

    public void setInsuranceDelayTimes(int insuranceDelayTimes) {
        this.insuranceDelayTimes = insuranceDelayTimes;
    }

    public boolean isEqualOuts() {
        return equalOuts;
    }

    public void setEqualOuts(boolean equalOuts) {
        this.equalOuts = equalOuts;
    }
}
