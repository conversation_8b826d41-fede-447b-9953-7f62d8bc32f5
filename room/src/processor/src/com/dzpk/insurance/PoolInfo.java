package com.dzpk.insurance;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;

/**
 * 分池计算器
 * Created by baidu on 16/8/30.
 *
 * <AUTHOR>
 */
public class PoolInfo {
    private final Logger logger = LogUtil.getLogger(PoolInfo.class);
    private final ArrayList<PoolChip> poolList = new ArrayList<>();

    /**
     * 递归产生分池
     *
     * @param betChipsArray
     */
    private void generatePool(ArrayList<Integer[]> betChipsArray) {
        if (!betChipsArray.isEmpty()) {

            // 特殊情况 如果最后只有一人赢牌 无论他下了多少筹码(包括0) 都把所有其他人剩余筹码给他
            int pipaiCnt = 0;
            int oneWinUid = 0;
            int oneWinUnitChip = 0;
            for (Integer[] betChips : betChipsArray) {
                if (betChips[2] == 1) {
                    oneWinUid = betChips[0];
                    oneWinUnitChip = betChips[1];
                    pipaiCnt++;
                }
            }
            if (pipaiCnt == 1) {
                int total = 0;
                for (Integer[] betChips : betChipsArray) {
                    total += betChips[1];
                }
                PoolChip poolChip = new PoolChip();
                poolChip.setTotalChips(total);
                poolChip.setUnitChips(oneWinUnitChip);
                poolChip.getUserIds().add(oneWinUid);
                setPoolList(poolChip);
                logger.debug("generate pool: {}", poolChip);
                return;
            }

            int minChouma = -1;
            int minIndex = -1;
            for (int i = 0; i < betChipsArray.size(); i++) {
                Integer[] betChips = betChipsArray.get(i);
                if (betChips[2] == 1) {
                    minChouma = betChips[1];
                    minIndex = i;
                }
            }

            if (minIndex != -1) {
                for (int i = 0; i < betChipsArray.size(); i++) {
                    Integer[] betChips = betChipsArray.get(i);
                    if (betChips[2] == 1 && betChips[1] < minChouma) {
                        minChouma = betChips[1];
                        minIndex = i;
                    }
                }

                // logger.debug(minChouma);

                PoolChip poolChip = new PoolChip();
                ArrayList<Integer[]> newArray = new ArrayList<Integer[]>();
                int pool = minChouma;
                poolChip.getUserIds().add(betChipsArray.get(minIndex)[0]);
                for (int i = 0; i < betChipsArray.size(); i++) {
                    Integer[] betChips = betChipsArray.get(i);
                    if (i != minIndex) {
                        if (betChips[2] == 1) {
                            pool += minChouma;
                            poolChip.getUserIds().add(betChips[0]);
                            if (betChips[1] > minChouma) {
                                newArray.add(new Integer[]{betChips[0],
                                        betChips[1] - minChouma, 1});
                            }
                        } else {
                            if (betChips[1] <= minChouma) {
                                pool += betChips[1];
                            } else {
                                pool += minChouma;
                                newArray.add(new Integer[]{betChips[0],
                                        betChips[1] - minChouma, 0});
                            }
                        }
                    }
                }

                poolChip.setTotalChips(pool);
                poolChip.setUnitChips(minChouma);
                setPoolList(poolChip);

                logger.debug("generate pool: {}", poolChip);

                if (!newArray.isEmpty()) {
                    generatePool(newArray);
                }
            }
        }
    }

    /**
     * 设置边池
     *
     * @param poolChip
     */
    private void setPoolList(PoolChip poolChip) {
        poolList.add(poolChip);
    }

    /**
     * 清空边池
     */
    public void cleanPoolList() {
        poolList.clear();
    }

    /**
     * 获取边池列表
     *
     * @param betChipsArray
     * @return
     */
    public ArrayList<PoolChip> getPoolList(ArrayList<Integer[]> betChipsArray) {
        generatePool(betChipsArray);
        return poolList;
    }
}
