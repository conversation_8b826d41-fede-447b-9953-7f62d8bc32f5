package com.dzpk.insurance;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@ToString
public class PoolChip {

    private final Set<Integer> userIds;   // 占有边池玩家id集合
    private int totalChips;         // 边池总筹码数
    private int unitChips;          // 边池单位筹码数
    private int unit;               //是否为主池1是0否
    public PoolChip() {
        userIds = new HashSet<>();
        totalChips = 0;
        unitChips = 0;
        unit=0;
    }

}
