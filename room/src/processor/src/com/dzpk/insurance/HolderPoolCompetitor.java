package com.dzpk.insurance;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投保人分池的竞争对手
 * <p>
 * Created by baidu on 17/1/20.
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class HolderPoolCompetitor {
    private int userId;                                 //  userid
    private String nickName;                            //  昵称
    private Set<Integer> outs = new HashSet<>();        //  outs列表
    private int firstCard;                              //  第一张手牌
    private int secondCard;                             //  第二张手牌
    private int poolChip;                               //  池里有多少筹码

    public void setOuts(Set<Integer> outs) {
        if (outs != null) {
            this.outs.addAll(outs);
        }
    }
}
