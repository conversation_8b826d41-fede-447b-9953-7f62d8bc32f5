package com.dzpk.insurance;

import com.ai.dz.config.AiRuleTemplate;
import com.alibaba.fastjson.JSON;
import com.dzpk.record.AnteAction;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.constant.Constant;
import com.ai.dz.room.util.AiInsurer;
import com.i366.room.BiPai;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.i366.model.pocer.Pocer;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.dzpk.common.utils.LogUtil;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 保险人
 * Created by baidu on 16/9/2.
 *
 * <AUTHOR>
 */
@Getter
public class Insurer {
    private static final Logger logger = LogUtil.getLogger(Insurer.class);
    private static final int maxBuyOutsLimit = 16;                                                   //  允许保险购买的outs上限数

    private int showInsuranceCount = 1;                                                 //  保险动画播放次数
    private int insuranceActiveCount = 0;                                               //  该手中,保险人激活保险的次数
    private int insuranceCalCount = 0;                                                  //  本手计算保险的次数

    @Setter
    private boolean showCards = false;                                                  //  保险亮牌

    private final Map<Integer, Holder> holderMap = new HashMap<>();            //  参保人列表(每一次翻牌后清空)
    private final Set<Integer> mustInsureUser = new HashSet<>();                       //  本轮还需要买保险的用户名单

    @Getter(AccessLevel.NONE)
    private final Map<Integer, Integer> holderChipMap = new HashMap<>();      //  参保人投保总额列表(当手有效)

    private final Map<Integer, Integer> turnChipMap = new HashMap<>();        //  参保人转牌购买的保险额(当手有效)
    private final Map<Integer, Integer> holderFeeMap = new HashMap<>();       //  参保人投保后保险不中需要支付的总额

    //  转牌每个用户每个池投保不中需要计入的成本(用来计算河牌保本)
    @Getter(AccessLevel.NONE)
    private final Map<Integer, Map<Integer, Integer>> turnInsuranceFeeMap = new HashMap<>();

    //  背保用户及转牌投保额
    @Getter(AccessLevel.NONE)
    private final Map<Integer, Map<Integer, Integer>> holderPayMap = new HashMap<>();

    @Getter(AccessLevel.NONE)
    private final Map<Integer, RoomPersion> persionMap = new HashMap<>(); //  房间打牌人的映射

    // uid - outs数量映射
    private final Map<Integer, Set<Integer>> userOutsMap = new HashMap<>();

    private static final Map<Integer, Double> oddsMap = new HashMap<Integer, Double>() {             //  保险人的赔率表
        {
            put(0, 0.0);
            put(1, 30.0);
            put(2, 16.0);
            put(3, 10.0);
            put(4, 8.0);
            put(5, 6.0);
            put(6, 5.0);
            put(7, 4.0);
            put(8, 3.5);
            put(9, 3.0);
            put(10, 2.5);
            put(11, 2.2);
            put(12, 2.0);
            put(13, 1.8);
            put(14, 1.6);
            put(15, 1.4);
            put(16, 1.3);

            //  以下为背保赔率
            put(17, 1.2);
            put(18, 1.1);
            put(19, 1.0);
            put(20, 0.8);
        }
    };

    /**
     * 通过userId获取投保人
     *
     * @param userId
     * @return
     */
    public Holder getHolderByUserId(int userId) {
        return holderMap.getOrDefault(userId, null);
    }

    public void addMustInsureUser(int userId) {
        mustInsureUser.add(userId);
    }

    public void removeMustInsureUser(int userId) {
        mustInsureUser.remove(userId);
    }

    public void removeAllMustInsureUser() {
        mustInsureUser.clear();
    }

    /**
     * 判断保险是否激活
     *
     * @param room
     * @return 0 不可以投保 1 可以投保
     */
    public int calculate(Room room) {
        logger.debug("insurer calculate R-{}:{} status={}", room.getRoomId(), room.getStage(), room.getRoomStatus());
        int status = 0;
        try {
            removeAllMustInsureUser();

            if (persionMap.isEmpty()) {
                for (RoomPersion rp : room.getRoomPersions()) {
                    if (rp != null) {
                        rp.setEqualOuts(false);
                        persionMap.put(rp.getUserId(), rp);
                    }
                }
            }

            ArrayList<PoolChip> poolList = room.getRoomService().getPoolList(true);

            //  统计所有参与比牌的用户ID(亮牌用户ID) outs中不能包含他们的手牌
            Set<Integer> showCardUserIds = new HashSet<>();
            for (PoolChip poolChip : poolList) {
                showCardUserIds.addAll(poolChip.getUserIds());
            }

            //  统计不该算入outs的牌
            Set<Integer> notOuts = new HashSet<>();
            for (RoomPersion rp : room.getRoomPersions()) {
                if (rp != null && showCardUserIds.contains(rp.getUserId())) {
                    notOuts.add(rp.getPocers()[0].getSize1());
                    notOuts.add(rp.getPocers()[1].getSize1());
                }
            }

            int poolIndex = 0;
            for (PoolChip poolChip : poolList) {
                int poolUserCnt = poolChip.getUserIds().size();
                //  池里有2或3个用户参与时才可以买保险
                if (poolUserCnt == 2 || poolUserCnt == 3) {
                    RoomPersion[] rps = new RoomPersion[poolUserCnt];

                    int j = 0;
                    for (RoomPersion rp : room.getRoomPersions()) {
                        if (rp != null && poolChip.getUserIds().contains(rp.getUserId())) {
                            rps[j++] = rp;
                        }
                    }

                    Map<Integer, Set<Integer>> outsMap = OutsCalculator.getOuts(rps, room.getPocer(), notOuts, userOutsMap);
                    logger.debug("outsMap: {} persionMap: {} pool: {}", outsMap.keySet(), persionMap.keySet(), poolChip);

                    // 看最大牌用户的outs在不在合理范围内
                    for (Integer userId : outsMap.keySet()) {

                        Holder holder;
                        if (holderMap.containsKey(userId)) {
                            holder = holderMap.get(userId);
                        } else {
                            holder = new Holder();
                            holder.setUserId(userId);

                            //  设置投保状态
                            holder.setStatus(0);

                            //  座位号
                            holder.setSeat(persionMap.get(userId).getSize());

                            //  设置投保人的剩余操作时间
                            holder.setEndTime(System.currentTimeMillis() + Constant.INSURANCE_OPERATE_TIME);

                            if (persionMap.get(userId).isEqualOuts()) {
                                holder.setEqualOuts(true);
                            }

                            holderMap.put(userId, holder);
                        }

                        Set<Integer> outs = outsMap.get(userId);

                        //  设置可投保分池下标
                        HolderPool holderPool = new HolderPool(poolIndex++);

                        //  设置池子的操作状态
                        holderPool.setStatus(0);

                        //  设置用户的下注筹码
                        holderPool.setUserChip(poolChip.getUnitChips());

                        //  设置整个池总筹码数
                        holderPool.setPoolChip(poolChip.getTotalChips());

                        //  设置outs的牌型
                        holderPool.setOuts(outs);

                        //  添加池子的竞争对手
                        for (int uid : poolChip.getUserIds()) {
                            RoomPersion rp = room.getAudMap().get(uid);
                            if (uid != holder.getUserId() && rp != null) {
                                HolderPoolCompetitor holderPoolCompetitor = new HolderPoolCompetitor();
                                holderPoolCompetitor.setUserId(uid);
                                holderPoolCompetitor.setNickName(rp.getUserInfo().getNikeName());
                                holderPoolCompetitor.setFirstCard(rp.getPocers()[0].getSize1());
                                holderPoolCompetitor.setSecondCard(rp.getPocers()[1].getSize1());
                                holderPoolCompetitor.setPoolChip(poolChip.getUnitChips());
                                holderPoolCompetitor.setOuts(userOutsMap.get(uid));
                                logger.debug("holder:{} competitor:{} hand cards:{} outs({}):{}",
                                        holder.getUserId(), uid,
                                        Arrays.toString(rp.getPocers()),
                                        holderPoolCompetitor.getOuts().size(),
                                        holderPoolCompetitor.getOuts().stream().map(Pocer::new).collect(Collectors.toList()));
                                holderPool.addHolderPoolCompetitor(holderPoolCompetitor);
                            }
                        }

                        //  设置赔率 (如果outs超过20 赔率设置为0.8)
                        if (outs.size() < 20) {
                            holderPool.setOdds(oddsMap.get(outs.size()));
                        } else {
                            holderPool.setOdds(0.8);
                        }

                        //  设置最小投保额
                        int minInsure = 0;
                        if (getInsuranceActiveCount() > 0) {
                            minInsure = getMinChip(holder, holderPool);
                        }
                        holderPool.setMinInsure(minInsure);

                        //  设置最高投保额(全底池)
                        int betChip = holderPool.getUserChip();
                        int totalChips = poolChip.getTotalChips();
                        logger.debug("U-{} betChip: {} totalChips: {} poolUserCnt: {}", holder.getUserId(),
                                betChip, totalChips, poolUserCnt);
                        int maxInsure = getMaxChip(totalChips, holderPool.getOdds());
                        holderPool.setMaxInsure(maxInsure);

                        //  设置保本额
                        int safeInsure = getSafeChip(userId, poolChip.getUnitChips(), holderPool);
                        holderPool.setSafeInsure(safeInsure);

                        if (getInsuranceActiveCount() >= 1) {
                            int turnBuyChips = turnChipMap.getOrDefault(holder.getUserId(), 0); //转牌投保额

                            /*
                             * 保险规则4 造成玩家平分的Outs不会计入购买保险的Outs数量，如果河牌存在平分Outs，那么玩家购买保险的总额不会超过他向底池内投入的总额
                             * 此处要比较玩家投入底池总额和底池/赔率,取最小的 否则会造成赔付额大于底池
                             */

                            if (holder.isEqualOuts()) {
                                int maxBuyLimit = holderPool.getUserChip() - turnBuyChips;
                                logger.debug("河牌平分Outs - userChip={} turnBuyChips={} minInsure={} maxInsure={} safeInsure={} maxBuyLimit={}",
                                        holderPool.getUserChip(), turnBuyChips, minInsure, maxInsure, safeInsure, maxBuyLimit);
                                if (maxBuyLimit > 0) {
                                    holderPool.setMinInsure(Math.min(minInsure, maxBuyLimit));
                                    holderPool.setMaxInsure(Math.min(maxInsure, maxBuyLimit));
                                    holderPool.setSafeInsure(Math.min(safeInsure, maxBuyLimit));
                                }
                            }
                        }

                        logger.debug("U-{} odds={} minInsure: {} maxInsure: {} safeInsure: {}", holder.getUserId(),
                                holderPool.getOdds(), holderPool.getMinInsure(), holderPool.getMaxInsure(), holderPool.getSafeInsure());

                        //  设置该投保池的参与人
                        holderPool.setUserIds(poolChip.getUserIds());

                        //  把这个投保池加入到投保人的池子列表
                        holder.addHolderPool(holderPool);
                    }
                }
            }

            /***** start 2017-1-20 筛选出投保人最大那个分池(2选1) *****/
            for (Holder holder : holderMap.values()) {
                List<HolderPool> pools = holder.getHolderPools();
                if (pools.size() == 2) {
                    // 保留投入額度較大那個池
                    // 除非這個池沒有 outs
                    HolderPool first = pools.get(0);
                    HolderPool second = pools.get(1);
                    int firstOuts = first.getOuts().size();
                    int firstBet = first.getUserChip();
                    int secondOuts = second.getOuts().size();
                    int secondBet = second.getUserChip();
                    logger.debug("U-{} 筛选出投保人最大那个分池(2选1) - first:[index: {} outs:{} bet:{}] second:[index: {} outs:{} bet:{}]",
                            holder.getUserId(),
                            first.getPoolIndex(), firstOuts, firstBet,
                            second.getPoolIndex(), secondOuts, secondBet);

                    int removeIndex;
                    if (firstBet < secondBet) {
                        if (secondOuts > 0) {
                            removeIndex = 0;
                        } else {
                            removeIndex = 1;
                        }
                    } else {
                        if (firstOuts > 0) {
                            removeIndex = 1;
                        } else {
                            removeIndex = 0;
                        }
                    }
                    HolderPool removedPool = pools.remove(removeIndex);
                    logger.debug("removed pool with index {}", removedPool.getPoolIndex());
                }
            }

            // 筛选后重新通知
            for (Integer uid : holderMap.keySet()) {
                Holder holder = holderMap.get(uid);
                for (HolderPool holderPool : holder.getHolderPools()) {
                    int outSize = holderPool.getOuts().size();
                    if (outSize > maxBuyOutsLimit) {
                        //  outs超过16 通知投保人 并帮他强制背保
                        noticeHolderOutsExceeded(room, uid);
                        checkHolderMustPay(room, uid, holderPool.getPoolIndex());
                        // 该池检查完强背后 再把操作状态置为已操作
                        holderPool.setStatus(1);
                        room.getDealer().recordMaxBuyOutsLimitInsure(uid, holderPool); //记录超出outs的底池信息

                    } else if (outSize > 0) {
                        addMustInsureUser(uid);
                    } else {
                        // 通知投保人 outs等于0
                        noticeHolderOutsExceeded(room, uid);
                    }
                }
                holder.getHolderPools().removeIf(holderPool -> holderPool.getOuts().isEmpty());
            }
            logger.debug("all insure user:{}", getMustInsureUser());
            /***** end 2017-1-20 筛选出投保人最大那个分池(2选1) *****/

            //  有人可以投保了 通知他们吧
            if (!holderMap.isEmpty()) {

                // 剔除不需要操作的投保人
                List<Holder> holders = holderMap.values().stream()
                    .filter(holder -> holder.getHolderPools().stream()
                        .anyMatch(pool -> pool.getStatus() == 0))
                    .collect(Collectors.toList());

                Integer[] holderArr = new Integer[holders.size()];            // 投保人列表(userid)
                Integer[] holderStatusArr = new Integer[holders.size()];      // 投保人状态
                Integer[] holderChipArr = new Integer[holders.size()];        // 投保人投保额
                Integer[] holderTimeArr = new Integer[holders.size()];        // 投保人剩余操作时间

                for (int i = 0; i < holders.size(); i++) {
                    holderArr[i] = holders.get(i).getUserId();
                    holderStatusArr[i] = holders.get(i).getStatus();
                    holderChipArr[i] = holders.get(i).getInsuranceChip();
                    holderTimeArr[i] = (int) ((holders.get(i).getEndTime() - System.currentTimeMillis()) / 1000);
                }

                // 发给投保人
                Set<Integer> holderUids = new HashSet<>();
                for (Holder holder : holders) {
                    holderUids.add(holder.getUserId());
                    sendToHolder(room, holder, holderArr, holderStatusArr, holderChipArr, holderTimeArr);
                }

                // 如果有需要操作的投保人 通知其他人包括观众
                if (!holders.isEmpty()) {
                    status = 1;
                    for (RoomPersion rp : room.getAudMap().values()) {
                        if (rp != null && !holderUids.contains(rp.getUserId())) {
                            sendToWatcher(room, rp.getUserId(), holderArr,
                                    holderStatusArr, holderChipArr, holderTimeArr);
                        }
                    }

                    // 保险播放动画次数加1
                    showInsuranceCount++;
                    // 当手投保次数+1
                    insuranceActiveCount++;
                }
            }

            // 当手计算保险次数+1
            insuranceCalCount++;
        } catch (Exception e) {
            logger.error(" calculate error, ", e);
            throw e;
        }

        return status;
    }

    /**
     * 通知投保者当前池outs不在范围内 不可投保
     *
     * @param room
     * @param userId
     */
    private void noticeHolderOutsExceeded(Room room, int userId) {
        //  没人有合法的outs 不需要投保 通知投保人
        Object[][] objs = {
                {60, insuranceActiveCount, I366ClientPickUtil.TYPE_INT_1}       //  当前触发保险次数
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OUTS_EXCEEDED);
        logger.debug("send to user_id:{} this pool's outs is exceeded!!!", userId);
        PublisherUtil.sendByUserId(room, bytes, userId);
    }

    /**
     * 计算当前池底的保本额
     *
     * @param userId
     * @param poolUnit
     * @param holderPool
     * @return
     */
    private int getSafeChip(int userId, int poolUnit, HolderPool holderPool) {
        // 转牌投保如果不中 投保成本需要算入河牌的保本额中
        int totalFee = poolUnit;
        if (turnInsuranceFeeMap.containsKey(userId)) {
            Map<Integer, Integer> map = turnInsuranceFeeMap.get(userId);
            if (map.containsKey(holderPool.getPoolIndex())) {
                totalFee += map.get(holderPool.getPoolIndex());
            }
        }

        int safeChip = (int) Math.ceil(totalFee / holderPool.getOdds());
        if (safeChip > holderPool.getMaxInsure()) {
            safeChip = holderPool.getMaxInsure();
        }

        return safeChip;
    }

    /**
     * 计算半池投保额 (小數點後兩位四捨五入)
     *
     * @param holderPool
     * @return
     */
    private int getHalfChip(HolderPool holderPool) {
        int halfChip = (int) Math.round(holderPool.getPoolChip() / 2.0 / holderPool.getOdds());
        if (halfChip > holderPool.getMaxInsure()) {
            halfChip = holderPool.getMaxInsure();
        }
        return halfChip;
    }

    /**
     * 计算最小投保额(包括翻牌不中 河牌的背保最低额度) (小數點後兩位向上取整)
     *
     * @param holder
     * @param holderPool
     * @return
     */
    private int getMinChip(Holder holder, HolderPool holderPool) {
        if (holderPayMap.containsKey(holder.getUserId())) {
            Map<Integer, Integer> map = holderPayMap.get(holder.getUserId());
            int holderPay = map.getOrDefault(holderPool.getPoolIndex(), 0);
            return (int) Math.ceil(holderPay / holderPool.getOdds());
        } else {
            return 0;
        }
    }

    /**
     * 计算最大投保额(全底池) (小數點後兩位向下取整)
     * <p>
     * 转牌阶段（Turn）
     * <p>
     * 规则：保险投注金额不能超过底池的25%
     * <ul>
     * <li> 最小投保额度：0
     * <li> 最大投保额度：1. 底池的25% 2. 底池/赔率 (取两者的较小值)
     * </ul>
     * <p>
     * 河牌阶段（River）
     * <p>
     * 规则：保险投注金额不能超过底池的50%
     * <ul>
     * <li> 最小投保额度：转牌的投保额
     * <li> 最大投保额度：1. 底池的50% 2. 底池/赔率 (取两者的较小值)
     * </ul>
     *
     * @param poolChip
     * @param odds
     * @return
     */
    private int getMaxChip(int poolChip, double odds) {
        double max1 = poolChip / odds;
        double max2;
        if (getInsuranceActiveCount() == 0) {
            max2 = poolChip * 0.25;
        } else {
            max2 = poolChip * 0.5;
        }
        return (int) Math.floor(Math.min(max1, max2));
    }

    /**
     * 接受投保
     *
     * @param userId
     * @param poolIndexArr
     * @param chipArr
     * @return
     */
    public int acceptInsurance(Room room, int userId, Integer[] poolIndexArr,
                               Integer[] chipArr, List<Set<Integer>> outsList) {
        if (poolIndexArr.length != chipArr.length || poolIndexArr.length != outsList.size()) {
            logger.debug("insure pool index and chip do not match!!!");
            return 1;
        }

        // 分池和投保额对应map
        Map<Integer, Integer> chipMap = new HashMap<>();
        // 分池和所选outs对应的map
        Map<Integer, Set<Integer>> outsMap = new HashMap<>();
        for (int i = 0; i < poolIndexArr.length; i++) {
            chipMap.put(poolIndexArr[i], chipArr[i]);
            outsMap.put(poolIndexArr[i], outsList.get(i));
        }

        // 若果是合法的投保人
        if (holderMap.containsKey(userId)) {
            Holder holder = holderMap.get(userId);
            if (holder.getStatus() == 1) {  //如果该轮保险已经操作过了，再次操作无效
                logger.debug("holder {} has already operate !!! status={}", userId, holder.getStatus());
                return 1;
            }
            List<HolderPool> holderPools = holder.getHolderPools();

            // （数据校验）投保额造成的赔付如果超过整个池 本次投保无效
            for (HolderPool holderPool : holderPools) {
                int index = holderPool.getPoolIndex();
                if (chipMap.containsKey(index)) {
                    double selectedOdds = oddsMap.get(outsMap.get(index).size());
                    int maxAccept = (int) Math.ceil(holderPool.getPoolChip() / selectedOdds);
                    if (maxAccept < chipMap.get(index)) {
                        logger.debug("max accept in pool {}:{}", index, maxAccept);
                        logger.debug("real selected odds:{}", selectedOdds);
                        logger.debug("real insure:{}", chipMap.get(index));
                        logger.debug("total chip in this pool:{}", holderPool.getPoolChip());
                        holder.setStatus(1);
                        removeMustInsureUser(userId);
                        if (getMustInsureUser().isEmpty()) {
                            room.setRoomStatus(7);
                        }
                        return 1;
                    }
                }
            }

            // 开始投保
            for (HolderPool holderPool : holderPools) {
                int index = holderPool.getPoolIndex();
                if (chipMap.containsKey(index)) {
                    holderPool.setInsureChip(chipMap.get(index));
                    holderPool.setStatus(1);

                    // 所选outs
                    holderPool.setSelectedOuts(holderPool.getOuts(), out -> outsMap.get(index).contains(out));
                    // 未选中outs
                    holderPool.setSelectedOuts(holderPool.getOuts(), out -> !outsMap.get(index).contains(out));

                    // 所选outs对应的赔率
                    holderPool.setSelectedOdds(oddsMap.get(holderPool.getSelectedOuts().size()));
                    // 未选outs对应的赔率
                    holderPool.setNonSelectedOdds(oddsMap.get(holderPool.getNonSelectedOuts().size()));

                    // 如果是部分选择outs的情况 未选部分outs强制背保
                    if (!holderPool.getNonSelectedOuts().isEmpty()) {
                        int nsChip = (int) Math.ceil(holderPool.getInsureChip() / holderPool.getNonSelectedOdds());
                        holderPool.setNonSelectedInsureChip(nsChip);
                    }

                    // 荷官记下投保记录
                    room.getDealer().recordInsure(userId, holderPool);

                    // 本手每个人投保额累加
                    if (holderChipMap.containsKey(userId)) {
                        holderChipMap.put(userId, holderChipMap.get(userId) + chipMap.get(index));
                        room.getTwoInsurer().put(userId, chipMap.get(index));
                    } else {
                        holderChipMap.put(userId, chipMap.get(index));
                        room.getOneInsurer().put(userId, chipMap.get(index));
                    }
                }
            }

            // 设置投保人已投过保
            holder.setStatus(1);

            removeMustInsureUser(userId);
            if (getMustInsureUser().isEmpty()) {
                room.setRoomStatus(7);
            }
            return 0;
        } else {
            return 1;
        }
    }

    /**
     * 弃保处理 0 成功 1 失败 2 强制背保
     *
     * @param room
     * @param userId
     * @return
     */
    public Integer[] acceptInsurance(Room room, int userId) {
        int status = 0;
        int totalChip = 0;
        Holder holder = getHolderByUserId(userId);
        if (holder == null) {
            status = 1;
        } else if (holder.getStatus() == 1) {  //如果该轮保险已经操作过了，再次操作无效
            logger.debug("holder {} has already operate !!! status={}", userId, holder.getStatus());
            status = 1;
        } else {
            // 如果必须背保
            if (holderPayMap.containsKey(userId)) {
                Map<Integer, Integer> map = holderPayMap.get(userId);
                // 分别对每个池背保
                for (Integer index : map.keySet()) {
                    int chip = map.get(index);
                    int insuranceChip = 0;
                    for (HolderPool holderPool : holder.getHolderPools()) {
                        if (holderPool.getPoolIndex() == index) {
                            logger.debug("R-{}:{} U-{} 强制背保 pool={}", room.getRoomId(), room.getStage(), userId, index);
                            holderPool.setStatus(1);
                            double odds = holderPool.getOdds();
                            insuranceChip = (int) Math.ceil(chip / odds);
                            logger.debug("pool={} chip={} odds={} mustPay={} min={} max={} eqOuts={}",
                                    index, chip, odds, insuranceChip,
                                    holderPool.getMinInsure(), holderPool.getMaxInsure(),
                                    holder.isEqualOuts());
                            // 河牌平分 outs, 強制背保不能超过最大投保额
                            if (insuranceChip > holderPool.getMaxInsure() && holder.isEqualOuts() && insuranceActiveCount >= 1) {
                                insuranceChip = holderPool.getMaxInsure();
                            }
                            holderPool.setInsureChip(insuranceChip);

                            // 弃保时所选outs为全部 赔率对应之
                            holderPool.setSelectedOuts(holderPool.getOuts());
                            holderPool.setSelectedOdds(odds);

                            // 荷官记录
                            room.getDealer().recordInsure(userId, holderPool);
                            break;
                        }
                    }

                    // 背保总额累加
                    totalChip += insuranceChip;
                }
                // 本手每个人投保额累加
                if (holderChipMap.containsKey(userId)) {
                    holderChipMap.put(userId, holderChipMap.get(userId) + totalChip);
                    room.getTwoInsurer().put(userId, totalChip);
                } else {
                    holderChipMap.put(userId, totalChip);
                    room.getOneInsurer().put(userId, totalChip);
                }
                status = 2;
            } else {
                if (!holder.getHolderPools().isEmpty()) { //玩家不需要背保的时候也记录下来该池的outs
                    room.getDealer().recordNoBuyOutsInsure(userId, holder.getHolderPools().get(0));
                }
            }

            logger.debug("no buy :{}", getMustInsureUser());
            holder.setStatus(1);
            removeMustInsureUser(userId);
            if (getMustInsureUser().isEmpty()) {
                room.setRoomStatus(7);
            }
        }

        return new Integer[]{status, totalChip};
    }

    /**
     * 检查下本轮是否有人需要强制背保 有的话下发
     *
     * @param room
     */
    public void checkAllMustPay(Room room) {
        if (!holderPayMap.isEmpty()) {
            for (Integer userId : holderMap.keySet()) {
                Holder holder = holderMap.get(userId);
//                int totalPay = 0;
                if (holderPayMap.containsKey(userId)) {
                    Map<Integer, Integer> poolInsurance = holderPayMap.get(userId);
                    List<HolderPool> holderPools = holder.getHolderPools();
                    for (HolderPool holderPool : holderPools) {
                        if (holderPool.getInsureChip() == 0
                                && poolInsurance.containsKey(holderPool.getPoolIndex())) {

                            logger.debug("R-{}:{} U-{} 强制背保 pool={}", room.getRoomId(), room.getStage(), userId, holderPool.getPoolIndex());

                            // 帮投保人强背
                            int fanpaiChip = poolInsurance.get(holderPool.getPoolIndex());
                            int mustPayChip = (int) Math.ceil(fanpaiChip / holderPool.getOdds());
                            holderPool.setInsureChip(mustPayChip);
                            holderPool.setStatus(1);

                            // 所选outs为全部 赔率对应之
                            holderPool.setSelectedOuts(holderPool.getOuts());
                            holderPool.setSelectedOdds(holderPool.getOdds());

                            // 荷官记下投保记录
                            room.getDealer().recordInsure(userId, holderPool);

                            // 强背费用叠加
//                            totalPay += mustPayChip;
                        }
                    }

//                    logger.debug("holder seat:" + holder.getSeat());
//                    logger.debug("holder userId:" + userId);
//                    logger.debug("holder pay:" + totalPay);
//
//                    // 通知所有人这个投保人强制背保
//                    if (totalPay > 0) {
//                        Object[][] objs = {
//                                {60, 2, I366ClientPickUtil.TYPE_INT_1},
//                                {61, holder.getSeat(), I366ClientPickUtil.TYPE_INT_1},
//                                {130, userId, I366ClientPickUtil.TYPE_INT_4},
//                                {131, totalPay, I366ClientPickUtil.TYPE_INT_4}
//                        };
//                        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPERATE);
//                        room.getRoomService().send(bytes);
//                    }
                }
            }
        }
    }

    /**
     * 计算该玩家背保额
     * 如果是部分选择outs的情况 未选部分outs强制背保
     *
     * @param userId
     * @return
     */
    public int calculateMustPay(int userId) {
        int mustInsuranceChip = 0;

        Holder holder = getHolderByUserId(userId);
        if (holder == null) {
            return mustInsuranceChip;
        }

        List<HolderPool> holderPools = holder.getHolderPools();
        for (HolderPool holderPool : holderPools) {
            logger.debug("getInsuranceActiveCount: {} getNonSelectedOuts: {}", getInsuranceActiveCount(), holderPool.getNonSelectedOuts().size());
            if (getInsuranceActiveCount() >= 1) {
                // 如果是部分选择outs的情况 未选部分outs强制背保
                if (!holderPool.getNonSelectedOuts().isEmpty()) {
                    mustInsuranceChip = (int) Math.ceil(holderPool.getInsureChip() / holderPool.getNonSelectedOdds());
                    return mustInsuranceChip;
                }
            }
        }

        return mustInsuranceChip;

    }

    /**
     * 检查当前投保人的当前池子是否需要强背
     *
     * @param room
     * @param userId
     * @param poolIndex
     */
    private void checkHolderMustPay(Room room, int userId, int poolIndex) {
        if (!holderPayMap.isEmpty() && holderPayMap.containsKey(userId)) {
            Map<Integer, Integer> map = holderPayMap.get(userId);
            //if (map.containsKey(poolIndex)) {
            Holder holder = getHolderByUserId(userId);
            for (int i = 0; i < holder.getHolderPools().size(); i++) {
                HolderPool holderPool = holder.getHolderPools().get(i);
                if (holderPool.getPoolIndex() == poolIndex && holderPool.getInsureChip() == 0) {
                    /**
                     *  fix bug 玩家购买保险导致积分输超
                     问题描述：
                     该牌局结束后，显示玩家带入800，但是输的积分是881，超出了带入的积分(经模拟德州也会有该问题)。
                     牌谱：
                     奥马哈牌谱:https://fhp.zanmen.biz:5443/?lan=zh&info_id=5c539f7005a2aa17e5dd7e9b_287814_5c53b1e805a2aa17e5df258c
                     德州牌型:
                     45 34 39 36 12
                     24 49
                     35 37
                     3 0
                     产生过程：
                     奥马哈牌局 3人allin 触发购买保险
                     玩家转牌购买的是边池的保险 没中  发到河牌时，提示outs大于16或小于0，无法购买保险
                     最后发完河牌，比牌输了，需要扣除373 + 81（买保险的投入）
                     问题现象：
                     玩家带入800，但是输的积分是881，超出了带入的积分
                     问题分析：
                     正常玩家转牌买了保险，河牌是需要背保的。这个bug里为什么不能背保，因为发到河牌时发现他在2个池里都是领先的，
                     参照保险规则第10条，玩家只能购买领先的池中投入积分最大的那个池，这个池与转牌购买的池不是一个池(转牌时他购买的是边池的保险)，这个池中他未投入保险，不需要背保。
                     解决思路：
                     玩家转牌购买保险后,为了防止积分输超,河牌都需要把转牌购买的保险背回来,不需要考虑是否为同一个池
                     受到影响的场景：
                     1 转牌与河牌购买的属于同一个池且河牌outs大于16
                     2 转牌与河牌购买的属于不同池
                     玩家爆牌 河牌购买的背保 抵消转牌购买保险  保险池盈亏为0  玩家输完积分  未发生输超
                     玩家未爆牌  玩家赢牌 盈利中会扣减转牌购买保险及背保值
                     */
                    int fanpaiChip;
                    int realPoolIndex = (int) map.keySet().toArray()[0];
                    if (map.get(poolIndex) != null) {
                        fanpaiChip = map.get(poolIndex);
                        logger.debug("R-{}:{} U-{} 强制背保 pool={}", room.getRoomId(), room.getStage(), userId, poolIndex);
                    } else {
                        fanpaiChip = map.get(realPoolIndex);
                        logger.debug("R-{}:{} U-{} 强制背保 pool={} realPool={}", room.getRoomId(), room.getStage(), userId, poolIndex, realPoolIndex);
                    }
                    int mustPayChip = (int) Math.ceil(fanpaiChip / holderPool.getOdds());
                    holderPool.setInsureChip(Math.min(mustPayChip, holderPool.getMaxInsure()));
                    holderPool.setStatus(1);

                    // 所选outs为全部 赔率对应之
                    holderPool.setSelectedOuts(holderPool.getOuts());
                    holderPool.setSelectedOdds(holderPool.getOdds());

                    // 荷官记下投保记录
                    room.getDealer().recordInsure(userId, holderPool);

                    logger.debug("holder seat:{} userId:{} mustPay:{} maxInsure:{}", holder.getSeat(), userId, mustPayChip, holderPool.getMaxInsure());

                    // 通知所有人这个投保人已强背
                    Object[][] objs = {
                            {60, 2, I366ClientPickUtil.TYPE_INT_1},
                            {61, holder.getSeat(), I366ClientPickUtil.TYPE_INT_1},
                            {130, userId, I366ClientPickUtil.TYPE_INT_4},
                            {131, 0, I366ClientPickUtil.TYPE_INT_4},
                            {132, holderPool.getInsureChip(), I366ClientPickUtil.TYPE_INT_4}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPERATE);
                    PublisherUtil.send(room, bytes);
                    break;
                }
            }

            // 遍历所有的池子操作状态
            boolean allOperate = true;
            for (int j = 0; j < holder.getHolderPools().size(); j++) {
                HolderPool holderPool = holder.getHolderPools().get(j);
                if (holderPool.getStatus() == 0) {
                    allOperate = false;
                    break;
                }
            }

            // 如果投保人所有的池都操作过了 标记为已投保操作
            if (allOperate) {
                removeMustInsureUser(userId);
            }
            //}
        }
    }

    /**
     * 庄家结账 赔付保险
     *
     * @param room
     * @param bipaiIndex
     * @param cardIndex 1=River 2=Turn
     */
    public void checkout(Room room, int bipaiIndex, int cardIndex) {
        logger.info("R-{}:{} 庄家结账 赔付保险", room.getRoomId(), room.getStage());
        logger.debug("insurer checkout roomId: {} bipaiIndex: {} cardIndex: {}", room.getRoomId(), bipaiIndex, cardIndex);
        // 有人站起或离开导致桌面只有一人 不结帐了
        if (room.getT3() == -989) {
            return;
        }

        RoomPersion[] roomPersions = room.getRoomPersions();
        Pocer[] pocers = room.getPocer();

        // 公共牌翻到哪了
        int ppSize = 0;
        for (Pocer p : pocers) {
            if (p != null) {
                ppSize++;
            }
        }

        // 最近翻出的公牌 1 在用户所选的outs里面 2 在用户未选中的outs里 3 在所有outs之外
        Pocer lastPocer = pocers[ppSize - 1];
        logger.debug("last pocer:{}-{}", lastPocer.getSize1(), lastPocer);
        logger.debug("holder size:{}", holderMap.size());
        if (!holderMap.isEmpty()) {
            Map<Integer, RoomPersion> rpMap = new HashMap<>();
            for (RoomPersion rp : roomPersions) {
                if (rp != null) {
                    rpMap.put(rp.getUserId(), rp);
                }
            }

            // 遍历一下上一轮最大牌的用户 看有没有需要赔钱的
            List<Integer> needPaidSeats = new ArrayList<>();
            List<Integer> needPaidUserIds = new ArrayList<>();
            List<Integer> needPaidChips = new ArrayList<>();
            Map<Integer, Set<Integer>> outPoolMap = new HashMap<>();

            for (Integer userId : holderMap.keySet()) {
                Holder holder = holderMap.get(userId);

                // 最大牌人的牌型
                RoomPersion rps = rpMap.get(userId);
                Pocer[] pocer = new Pocer[ppSize + 2];
                System.arraycopy(pocers, 0, pocer, 0, ppSize);
                pocer[ppSize] = rps.getPocers()[0];
                pocer[ppSize + 1] = rps.getPocers()[1];
                Object[] obj = BiPai.zuidapai1(pocer);
                rps.setZuidaPocers((Pocer[]) obj[0]);
                rps.setPocerType((Integer) obj[1]);

                // 遍历投保人的每个池 如果该池的参与人有比他大牌 那他在该池out了
                int compensation = 0;
                for (int i = 0; i < holder.getHolderPools().size(); i++) {
                    boolean rpsOut = false;
                    HolderPool holderPool = holder.getHolderPools().get(i);
                    for (RoomPersion rp : roomPersions) {
                        if (rp != null && holderPool.getUserIds().contains(rp.getUserId()) && rp.getStatus() > 0) {
                            Pocer[] tmpPocer = new Pocer[ppSize + 2];
                            System.arraycopy(pocers, 0, tmpPocer, 0, ppSize);
                            tmpPocer[ppSize] = rp.getPocers()[0];
                            tmpPocer[ppSize + 1] = rp.getPocers()[1];
                            Object[] objRp = BiPai.zuidapai1(tmpPocer);
                            rp.setZuidaPocers((Pocer[]) objRp[0]);
                            rp.setPocerType((Integer) objRp[1]);
                            if (BiPai.bipai2(rps, rp) == 1) {
                                // 记录用户out的分池
                                if (outPoolMap.containsKey(userId)) {
                                    Set<Integer> userOutPools = outPoolMap.get(userId);
                                    userOutPools.add(holderPool.getPoolIndex());
                                    outPoolMap.put(userId, userOutPools);
                                } else {
                                    Set<Integer> userOutPools = new HashSet<>();
                                    userOutPools.add(holderPool.getPoolIndex());
                                    outPoolMap.put(userId, userOutPools);
                                }

                                rpsOut = true;
                                break;
                            }
                        }
                    }

                    // 如果out了 陪钱给上一轮最大牌的人
                    if (rpsOut) {
                        // 如果这个池里投保了
                        if (holderPool.getInsureChip() > 0) {
                            // 这个池需要陪多少钱 累加总赔付额
                            if (getInsuranceActiveCount() >= 1 && holder.isEqualOuts()) {
                                int duePay = (int) (holderPool.getInsureChip() * holderPool.getSelectedOdds());
                                int turnBuyChips = turnChipMap.getOrDefault(userId, 0);
                                logger.debug("insureChip={} odds={} duePay={} turnBuyChips={}",
                                        holderPool.getInsureChip(), holderPool.getSelectedOdds(), duePay, turnBuyChips);
                                if (duePay < turnBuyChips) {
                                    logger.info("河牌平分Outs - 赔付金额小于轉牌投保，強制赔付");
                                    compensation += turnBuyChips;
                                } else {
                                    compensation += duePay;
                                }
                            } else if (holderPool.getSelectedOuts().contains(lastPocer.getSize1())) {
                                // 如果翻出来的牌在用户所选的outs里
                                compensation += (int) (holderPool.getInsureChip() * holderPool.getSelectedOdds());
                                // 如果用户是属于部分选择outs
                                if (holderPool.getNonSelectedInsureChip() > 0) {
                                    compensation -= holderPool.getNonSelectedInsureChip();
                                }
                            } else if (holderPool.getNonSelectedOuts().contains(lastPocer.getSize1())) {
                                // 如果翻出来的牌在用户未选的outs里
                                compensation += (int) (holderPool.getNonSelectedInsureChip()
                                        * holderPool.getNonSelectedOdds()) - holderPool.getInsureChip();
                            }
                        }
                    }
                }
                // 赔钱
                if (compensation > 0) {
                    logger.info("R-{}:{} U-{} 保险赔付, 赔付金额:{}", room.getRoomId(), room.getStage(), userId, compensation);
                    RoomPersion rp = rpMap.get(userId);
                    rp.setNowcounma(rp.getNowcounma() + compensation);
                    rp.setInsuranceLast(rp.getInsuranceLast() + compensation);
                    rp.setWinInsuranceLast(rp.getWinInsuranceLast() + compensation);
                    // 战绩页保险明细经常出错，加上打印方便查看
                    logger.debug("R-{}:{} U-{} compensation={} getInsuranceLast={} win={}", room.getRoomId(), room.getStage(), userId, compensation, rp.getInsuranceLast(), rp.getWinInsuranceLast());
                    room.getDealer().addTotalInsurance(userId, compensation);
                    room.getDealer().addInsurance(userId, compensation);
                    room.setInsuranceChip(room.getInsuranceChip() - compensation);
                    room.setInsuranceLast(room.getInsuranceLast() - compensation);

                    needPaidSeats.add(rpMap.get(userId).getSize());
                    needPaidUserIds.add(userId);
                    needPaidChips.add(compensation);

                    // 荷官爆牌次数统计
                    room.getDealer().recordBoom();
                } else {
                    logger.info("R-{}:{} U-{} 沒中保险", room.getRoomId(), room.getStage(), userId);
                }
            }

            // 有人需要赔钱给他 通知所有人吧
            if (!needPaidSeats.isEmpty()) {
                Integer[] seatArr = new Integer[needPaidSeats.size()];
                Integer[] userIdArr = new Integer[needPaidUserIds.size()];
                Integer[] paidArr = new Integer[needPaidChips.size()];

                StringBuilder seatStr = new StringBuilder();
                StringBuilder uidStr = new StringBuilder();
                StringBuilder chipStr = new StringBuilder();

                for (int i = 0; i < needPaidSeats.size(); i++) {
                    seatArr[i] = needPaidSeats.get(i);
                    seatStr.append(needPaidSeats.get(i)).append(",");
                }
                for (int i = 0; i < needPaidUserIds.size(); i++) {
                    userIdArr[i] = needPaidUserIds.get(i);
                    uidStr.append(needPaidUserIds.get(i)).append(",");
                }
                for (int i = 0; i < needPaidChips.size(); i++) {
                    paidArr[i] = needPaidChips.get(i);
                    chipStr.append(needPaidChips.get(i)).append(",");
                }

                logger.debug("pay seat:{} uid:{} chip:{}", seatStr, uidStr, chipStr);

                byte[] bytes;
                Object[][] objs = {
                        {130, seatArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},                    // 座位
                        {131, userIdArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},                  // userId
                        {132, paidArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},                    // 赔多少
                };
                bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_RESULT);
                PublisherUtil.send(room, bytes);
            }

            // 保险不中 支付保险费累加
            for (Integer userId : holderMap.keySet()) {

                int fee = 0;
                Holder holder = holderMap.get(userId);
                List<HolderPool> holderPools = holder.getHolderPools();

                for (HolderPool holderPool : holderPools) {
                    // 如果投保人的当前分池已经out了 跳过
                    if (outPoolMap.containsKey(userId)) {
                        if (outPoolMap.get(userId).contains(holderPool.getPoolIndex())) {
                            continue;
                        }
                    }

                    if (holderPool.getInsureChip() > 0) {
                        fee += (holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());

                        // 记录投保人当前分池的投保额 用于计算保本
                        if (turnInsuranceFeeMap.containsKey(userId)) {
                            Map<Integer, Integer> turnFeeMap = turnInsuranceFeeMap.get(userId);
                            turnFeeMap.put(holderPool.getPoolIndex(),
                                    holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());
                        } else {
                            Map<Integer, Integer> turnFeeMap = new HashMap<>();
                            turnFeeMap.put(holderPool.getPoolIndex(),
                                    holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());
                            turnInsuranceFeeMap.put(userId, turnFeeMap);
                        }

                        // 记录需要背保的用户及对应池的翻牌投保额
                        if (getInsuranceActiveCount() == 1) {
                            if (holderPayMap.containsKey(userId)) {
                                Map<Integer, Integer> tmpMap = holderPayMap.get(userId);
                                tmpMap.put(holderPool.getPoolIndex(),
                                        holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());
                                holderPayMap.put(userId, tmpMap);
                            } else {
                                Map<Integer, Integer> newMap = new HashMap<>();
                                newMap.put(holderPool.getPoolIndex(),
                                        holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());
                                holderPayMap.put(userId, newMap);
                            }
                        }

                        // 房间保险总营收和本手营收
                        room.setInsuranceChip(room.getInsuranceChip()
                                + holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());
                        room.setInsuranceLast(room.getInsuranceLast()
                                + holderPool.getInsureChip() + holderPool.getNonSelectedInsureChip());

                        // 个人保险营收
                        RoomPersion rp = rpMap.get(userId);

                        logger.debug("rp.getInsuranceLast()= {} getInsureChip: {} getNonSelectedInsureChip: {}", rp.getInsuranceLast(), holderPool.getInsureChip(), holderPool.getNonSelectedInsureChip());
                        rp.setInsuranceLast(rp.getInsuranceLast()
                                - holderPool.getInsureChip() - holderPool.getNonSelectedInsureChip());

                        room.getDealer().addTotalInsurance(userId, -holderPool.getInsureChip() - holderPool.getNonSelectedInsureChip());

                        logger.debug("checkout userId={} roomId={} getTotalInsurance={}", userId, room.getRoomId(), room.getDealer().getTotalInsurance(userId));
                    }
                }

                //  总投保额(结算的时候扣除)
                if (fee > 0) {
                    if (holderFeeMap.containsKey(userId)) {
                        holderFeeMap.put(userId, holderFeeMap.get(userId) + fee);
                    } else {
                        holderFeeMap.put(userId, fee);
                    }
                }

            }

            // 牌局回放
            try {
                for (int i = 0; i < needPaidUserIds.size(); i++) {
                    AnteAction anteAction = room.getRoomReplay().getAnteAction();
                    if (anteAction == null) {
                        anteAction = new AnteAction();
                    }
                    Map<String, Object> actionMap = new HashMap<>();
                    actionMap.put("ACTION", "insure");
                    actionMap.put("NUMBER", needPaidSeats.get(i));
                    actionMap.put("INSRESULT", needPaidChips.get(i));
                    actionMap.put("SYSRESULT", room.getInsuranceLast());
                    logger.debug("action map: {}", actionMap);
                    anteAction.getActions().add(actionMap);
                    if (room.getRoomReplay().getAnteAction() == null) {
                        room.getRoomReplay().setAnteAction(anteAction);
                    }
                    holderMap.remove(needPaidUserIds.get(i));
                }
                for (Integer userId : holderMap.keySet()) {
                    AnteAction anteAction = room.getRoomReplay().getAnteAction();
                    if (anteAction == null) {
                        anteAction = new AnteAction();
                    }
                    Map<String, Object> actionMap = new HashMap<>();
                    actionMap.put("ACTION", "insure");
                    actionMap.put("NUMBER", room.getAudMap().get(userId).getSize());
                    actionMap.put("INSRESULT", 0);
                    actionMap.put("SYSRESULT", room.getInsuranceLast());
                    logger.debug("action map: {}", actionMap);
                    anteAction.getActions().add(actionMap);
                    if (room.getRoomReplay().getAnteAction() == null) {
                        room.getRoomReplay().setAnteAction(anteAction);
                    }
                }
            } catch (Exception e) {
                logger.error("", e);
            }

            // 结账完 该轮投保记录清零
            holderMap.clear();

            //每轮投保的保险池清空
            room.getDealer().resetHolderPoolList();
            // 相对outs数清零
            userOutsMap.clear();
        }


        //判断是否进入保险模式 turn card时才做判断
        logger.debug("R-{}:{} checkout finished....", room.getRoomId(), room.getStage());
        if (cardIndex == 2) {
            room.roomProcedure.checkInsure(bipaiIndex);
        }
    }

    /**
     * 进房发送保险消息
     *
     * @param room
     * @param userId
     */
    public void sendEnterRoomMsg(Room room, int userId) {

        if (!holderMap.isEmpty()) { // 如果正在买保险
            Integer[] holderArr = new Integer[holderMap.size()];            // 投保人列表(userid)
            Integer[] holderStatusArr = new Integer[holderMap.size()];      // 投保人状态
            Integer[] holderChipArr = new Integer[holderMap.size()];        // 投保人投保额
            Integer[] holderTimeArr = new Integer[holderMap.size()];        // 投保人剩余操作时间

            int i = 0;
            for (Integer uid : holderMap.keySet()) {
                holderArr[i] = uid;
                holderStatusArr[i] = holderMap.get(uid).getStatus();
                holderChipArr[i] = holderMap.get(uid).getInsuranceChip();
                holderTimeArr[i] = (int) ((holderMap.get(uid).getEndTime() - System.currentTimeMillis()) / 1000);
                i++;
            }

            // 如果当前用户可以买保险
            if (holderMap.containsKey(userId)) {
                Holder holder = holderMap.get(userId);
                // 如果用户当轮没有完成保险购买操作
                if (holder.getStatus() == 0) {
                    sendToHolder(room, holder, holderArr, holderStatusArr, holderChipArr, holderTimeArr);
                    return;
                }
            }

            // 其他情况处于旁观状态
            sendToWatcher(room, userId, holderArr, holderStatusArr, holderChipArr, holderTimeArr);
        }
    }

    /**
     * 投保人延时 0 成功 1 失败
     *
     * @param userId
     * @param delayTime
     * @param room
     * @return
     */
    public int addTimeByHolder(int userId, int delayTime, Room room) {
        if (holderMap.containsKey(userId)) {
            Holder holder = getHolderByUserId(userId);
            if (holder != null) {
                holder.setEndTime(holder.getEndTime() + delayTime * 1000L);
                holder.setInsuranceDelayTimes(holder.getInsuranceDelayTimes() + 1);

                long insuranceBeginTime = (System.currentTimeMillis() - room.getInsuranceBeginTime()) / 1000;  //当前已经进行的保险时间
                //考虑延时情况增加至35秒 否则会出现10011延时任务和客户端传来的401同时处理，延时2次后连发两张牌
                insuranceBeginTime = 35 - insuranceBeginTime + delayTime;  //新的保险任务触发时间
                room.setInsuranceBeginTime(room.getInsuranceBeginTime() + delayTime * 1000L);
                // 使之前的所有10011延时任务无效
                for (String key : room.roomProcedure.delayTaskMap.keySet()) {
                    Task value = room.roomProcedure.delayTaskMap.get(key);
                    int taskId = value.getTaskId();
                    if (TaskConstant.TASK_INSURANCE_TIMEOUT == taskId) {
                        int taskUserId = (int) value.getMap().get(3); //只能将对应的保险超时任务重置
                        if (taskUserId == userId) {
                            value.setValid(false);
                            value.getTaskFuture().cancel(true);
                        }
                    }
                }

                logger.debug("clear 10011 task");
                //重新生成一个保险超时的任务
                Map<Integer, Object> map = new HashMap<>();
                map.put(1, room.getInsureIndex());
                map.put(2, room.getBetIndex());
                map.put(3, userId);
                Task delaytask = new Task(TaskConstant.TASK_INSURANCE_TIMEOUT, map, room.getRoomId(), room.getRoomPath(), userId); // 20241118 add userId
                logger.debug("403 task InsureIndex: {} BetIndex: {}", room.getInsureIndex(), room.getBetIndex());
                logger.debug("new task 10011 new delaytime: {}", insuranceBeginTime);

                room.roomProcedure.delayTaskMap.put(delaytask.getId(), delaytask);

                WorkThreadService.submitDelayTask(room.getRoomId(), delaytask, insuranceBeginTime * 1000);

                return 0;
            }
        } else {
            logger.error("addTimeByHolder error,can't find holder in holderMap,roomid={},userid={}", room.getRoomId(), userId);
        }

        return 1;
    }

    /**
     * 发送给保险观众
     *
     * @param room
     * @param userId
     * @param holderArr
     * @param holderStatusArr
     * @param holderChipArr
     * @param holderTimeArr
     */
    private void sendToWatcher(Room room, int userId, Integer[] holderArr, Integer[] holderStatusArr,
                               Integer[] holderChipArr, Integer[] holderTimeArr) {

        logger.debug("insurance show count:{}", getShowInsuranceCount());
        logger.debug("holder arr:{}", Arrays.toString(holderArr));
        logger.debug("holder status:{}", Arrays.toString(holderStatusArr));
        logger.debug("holder chip:{}", Arrays.toString(holderChipArr));
        logger.debug("holder lefttime:{}", Arrays.toString(holderTimeArr));

        Object[][] objs = {
                {60, getShowInsuranceCount(), I366ClientPickUtil.TYPE_INT_1},         //  当前触发保险次数
                {130, holderArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},                //  所有参保人列表
                {131, holderStatusArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},          //  所有参保人投保状态
                {132, holderChipArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},            //  所有参保人投保额
                {133, holderTimeArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},            //  所有参保人剩余操作时间
                {147, 0, I366ClientPickUtil.TYPE_INT_4},            //  参保人保险延时次数
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPEN);
        PublisherUtil.sendByUserId(room, bytes, userId);
    }

    /**
     * 发给投保人
     *
     * @param room
     * @param holder
     * @param holderArr
     * @param holderStatusArr
     * @param holderChipArr
     * @param holderTimeArr
     */
    private void sendToHolder(Room room, Holder holder, Integer[] holderArr,
                              Integer[] holderStatusArr, Integer[] holderChipArr, Integer[] holderTimeArr) {
        List<Integer> outsList = new ArrayList<>();

        // 剔除outs大于16的池子
        List<HolderPool> pools = new ArrayList<>();
        for (HolderPool holderPool : holder.getHolderPools()) {
            if (holderPool.getOuts().size() > maxBuyOutsLimit) {
                continue;
            }
            pools.add(holderPool);
        }

        // 可以投保的池子拼数据
        if (!pools.isEmpty()) {
            Integer[] minInsuranceArr = new Integer[pools.size()];
            Integer[] maxInsuranceArr = new Integer[pools.size()];
            Integer[] safeInsuranceArr = new Integer[pools.size()];
            Integer[] halfInsuranceArr = new Integer[pools.size()];
            Integer[] poolIndexArr = new Integer[pools.size()];
            Integer[] poolChipsArr = new Integer[pools.size()];
            Integer[] isEntireBuyArr = new Integer[pools.size()];
            Integer[] turnBuyArr = new Integer[pools.size()];
            Double[] oddsArr = new Double[pools.size()];


            List<String> competitorNick = new ArrayList<>();
            List<String> competitorIds = new ArrayList<>();
            List<Integer> compOuts = new ArrayList<>();         // 竞争者的outs数量
            List<Integer> compChips = new ArrayList<>();        // 竞争者在各个池里有多少筹码
            List<Integer> compCards = new ArrayList<>();                // 竞争者手牌

            int equalOuts = 0;  //  是否存在平分outs (0转牌 1 河牌存在平分 2 河牌不存在平分)
            int turnBuyChips = 0;  //转牌购买保险额

            int compCount = 0;
            for (int poolIndex = 0; poolIndex < pools.size(); poolIndex++) {
                HolderPool holderPool = pools.get(poolIndex);
                poolIndexArr[poolIndex] = holderPool.getPoolIndex();
                poolChipsArr[poolIndex] = holderPool.getPoolChip();
                List<String> compNicks = new ArrayList<>();
                List<String> compIds = new ArrayList<>();

                // 竞争者的数据
                for (HolderPoolCompetitor competitor : holderPool.getHolderPoolCompetitors()) {
                    compNicks.add(competitor.getNickName());
                    compIds.add(String.valueOf(competitor.getUserId()));
//                    logger.debug("i:" + i);
//                    logger.debug("nick:" + competitor.getNickName());
//                    logger.debug("outs:" + competitor.getOuts().size());
//                    logger.debug("competitor.getFirstCard():" + competitor.getFirstCard());
//                    logger.debug("competitor.getSecondCard():" + competitor.getSecondCard());
                    compOuts.add(competitor.getOuts().size());
                    compChips.add(competitor.getPoolChip());
                    compCards.add(competitor.getFirstCard());
                    compCards.add(competitor.getSecondCard());
                    compCount++;
                }

                competitorNick.add(String.join("@%", compNicks));
                competitorIds.add(String.join("@%", compIds));

                outsList.addAll(holderPool.getOuts());

                oddsArr[poolIndex] = oddsMap.get(holderPool.getOuts().size());
                outsList.add(-1);

                minInsuranceArr[poolIndex] = holderPool.getMinInsure();
                maxInsuranceArr[poolIndex] = holderPool.getMaxInsure();
                safeInsuranceArr[poolIndex] = holderPool.getSafeInsure();
                halfInsuranceArr[poolIndex] = getHalfChip(holderPool);
                if (getInsuranceActiveCount() >= 1) {
                    turnBuyChips = turnChipMap.getOrDefault(holder.getUserId(), 0); //转牌投保额
                    equalOuts = holder.isEqualOuts() ? 1 : 2;
                }

                //  投保人的池子是否允许部分购买outs && 上轮未中保险的投保额
                if (turnInsuranceFeeMap.containsKey(holder.getUserId())) {
                    if (turnInsuranceFeeMap.get(holder.getUserId()).containsKey(holderPool.getPoolIndex())) {
                        isEntireBuyArr[poolIndex] = 0;
                        turnBuyArr[poolIndex] = holderPool.getUserChip()
                                + turnInsuranceFeeMap.get(holder.getUserId()).get(holderPool.getPoolIndex());
                    }
                } else {
                    isEntireBuyArr[poolIndex] = 1;
                    turnBuyArr[poolIndex] = holderPool.getUserChip();
                }
            }

            //  outs列表
            Integer[] outsArr = outsList.toArray(new Integer[0]);

            Integer[] compOuts_ = compOuts.toArray(new Integer[0]);
            Integer[] compChips_ = compChips.toArray(new Integer[0]);
            Integer[] compCards_ = compCards.toArray(new Integer[0]);

            int insuranceDelayTimes = holder.getInsuranceDelayTimes();
            //  打下日志
            logger.debug("insurer send to holder user id:" + holder.getUserId());
            logger.debug("insure show count:" + getShowInsuranceCount());
            logger.debug("holder arr:" + Arrays.toString(holderArr));
            logger.debug("holder status:" + Arrays.toString(holderStatusArr));
            logger.debug("holder chip:" + Arrays.toString(holderChipArr));
            logger.debug("holder lefttime:" + Arrays.toString(holderTimeArr));
            logger.debug("holder pool index:" + Arrays.toString(poolIndexArr));
            logger.debug("holder pool chips:" + Arrays.toString(poolChipsArr));
            logger.debug("outs:" + Arrays.toString(outsArr));
            logger.debug("odds:" + Arrays.toString(oddsArr));
            logger.debug("mins:" + Arrays.toString(minInsuranceArr));
            logger.debug("maxs:" + Arrays.toString(maxInsuranceArr));
            logger.debug("safes:" + Arrays.toString(safeInsuranceArr));
            logger.debug("halfs:" + Arrays.toString(halfInsuranceArr));
            logger.debug("is entire buy:" + Arrays.toString(isEntireBuyArr));
            logger.debug("turn buy chip:" + Arrays.toString(turnBuyArr));
            logger.debug("competitor nickname:" + competitorNick);
            logger.debug("competitor outs:" + Arrays.toString(compOuts_));
            logger.debug("competitor chips:" + Arrays.toString(compChips_));
            logger.debug("competitor cards:" + Arrays.toString(compCards_));
            logger.debug("turnBuyChips :" + turnBuyChips);
            logger.debug("equal outs:" + equalOuts);
            logger.debug("insuranceDelayTimes:" + insuranceDelayTimes);
            Map<String, Integer[]> outMapping = new HashMap<>();

            try {

                for (HolderPool holderPool : pools) {
                    for (HolderPoolCompetitor competitor : holderPool.getHolderPoolCompetitors()) {
                        int competitorId = competitor.getUserId();
                        Set<Integer> competitorOuts = competitor.getOuts();
                        outMapping.put(String.valueOf(competitorId), competitorOuts.toArray(new Integer[0]));
                    }
                }
                outMapping.put(String.valueOf(holder.getUserId()), outsArr);

            } catch (Exception e) {
                logger.error("outMapError error", e);
            }

            // to json
            String outMappingStr = JSON.toJSONString(outMapping);
            logger.debug("outMappingStr:{}", outMappingStr);

            String oddsStr = Arrays.stream(oddsArr)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String competitorNickStr = String.join("@%@%", competitorNick);      // 竞争者昵称
            String competitorIdsStr = String.join("@%@%", competitorIds);        // 竞争者昵称

            Object[][] objs = {
                    {60, getShowInsuranceCount(), I366ClientPickUtil.TYPE_INT_1},       //  当前触发保险次数
                    {130, holderArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},              //  所有参保人列表
                    {131, holderStatusArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},        //  所有参保人投保状态
                    {132, holderChipArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},          //  所有参保人投保额
                    {133, holderTimeArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},          //  所有参保人剩余操作时间
                    {134, poolIndexArr, I366ClientPickUtil.TYPE_INT_1_ARRAY},           //  当前参保人可购分池下标
                    {135, outsArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},                //  当前参保人outs列表
                    {136, oddsStr, I366ClientPickUtil.TYPE_STRING_UTF16},               //  当前参保人赔率列表
                    {137, minInsuranceArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},        //  当前参保人最小投保额
                    {138, maxInsuranceArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},        //  当前参保人最大投保额
                    {139, safeInsuranceArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},       //  当前参保人保本投保额
                    {140, isEntireBuyArr, I366ClientPickUtil.TYPE_INT_1_ARRAY},         //  是否允许购买所有outs
                    {141, turnBuyArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},             //  转牌购买的保险额 + 玩家筹码
                    {142, competitorNickStr, I366ClientPickUtil.TYPE_STRING_UTF16},        //  竞争者昵称
                    {143, compOuts_, I366ClientPickUtil.TYPE_INT_4_ARRAY},              //  竞争者outs
                    {144, compChips_, I366ClientPickUtil.TYPE_INT_4_ARRAY},             //  竞争者池里的筹码
                    {145, compCards_, I366ClientPickUtil.TYPE_INT_4_ARRAY},             //  竞争者手牌
                    {146, equalOuts, I366ClientPickUtil.TYPE_INT_4},                   //  是否存在平分outs (0转牌 1 河牌存在平分 2 河牌不存在平分)
                    {147, insuranceDelayTimes, I366ClientPickUtil.TYPE_INT_4},          //  投保人购买保险延时次数
                    {148, outMappingStr, I366ClientPickUtil.TYPE_STRING_UTF16},         //
                    {149, competitorIdsStr, I366ClientPickUtil.TYPE_STRING_UTF16},         //  竞争者id
                    {150, halfInsuranceArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},       //  当前参保人半池投保额
                    {151, poolChipsArr, I366ClientPickUtil.TYPE_INT_4_ARRAY},           //  当前参保人可购分池籌码
            };
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPEN);

            // 发给大牌的人
            PublisherUtil.sendByUserId(room, bytes, holder.getUserId());

            if (AiRuleTemplate.isAiUser(holder.getUserId())) {//如果买保险的玩家是AI的话，则进入AI处理逻辑
                AiInsurer.insurerStrategy(outsArr, room, holder, minInsuranceArr, maxInsuranceArr, safeInsuranceArr, poolIndexArr);
            }
        }
    }
}
