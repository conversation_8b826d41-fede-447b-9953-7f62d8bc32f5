package com.dzpk.insurance;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import java.util.function.Predicate;

/**
 * 投保人的分池信息
 * Created by baidu on 16/9/2.
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class HolderPool {

    private int status;                                                                     // 投保状态 0 未操作 1已操作
    private Set<Integer> outs = new HashSet<>();                                     // outs列表
    private Set<Integer> selectedOuts = new HashSet<>();                             // 所选outs列表
    private Set<Integer> nonSelectedOuts = new HashSet<>();                          // 未选outs列表
    private double odds;                                                                    // 赔率
    private double selectedOdds;                                                            // 所选outs的对应赔率
    private double nonSelectedOdds;                                                         // 未选outs的对应赔率
    private final int poolIndex;                                                                  // 分池下标
    private int minInsure;                                                                  // 最低投保额
    private int maxInsure;                                                                  // 最高投保额
    private int safeInsure;                                                                 // 保本额
    private int insureChip;                                                                 // 该池实际投保额
    private int nonSelectedInsureChip = 0;                                                  // 未选择outs的被动背保额
    private Set<Integer> userIds = new HashSet<>();                                  // 该分池的所有参与用户ID
    private int userChip;                                                                   // 用户在该池的下注筹码数
    private int poolChip;                                                                   // 该池总筹码数
    private List<HolderPoolCompetitor> holderPoolCompetitors = new ArrayList<>();           // 该池子其他竞争用户

    public HolderPool(int poolIndex) {
        this.poolIndex = poolIndex;
    }

    public void setOuts(Set<Integer> outs) {
        this.outs.addAll(outs);
    }

    public void setSelectedOuts(Set<Integer> outs) {
        this.selectedOuts.addAll(outs);
    }

    public void setSelectedOuts(Set<Integer> outs, Predicate<Integer> filter) {
        outs.stream().filter(filter).forEach(out -> this.selectedOuts.add(out));
    }

    public void setNonSelectedOuts(Set<Integer> outs) {
        this.nonSelectedOuts.addAll(outs);
    }

    public void setUserIds(Set<Integer> userIds) {
        this.userIds.addAll(userIds);
    }

    public void addHolderPoolCompetitor(HolderPoolCompetitor holderPoolCompetitor) {
        this.holderPoolCompetitors.add(holderPoolCompetitor);
    }
}
