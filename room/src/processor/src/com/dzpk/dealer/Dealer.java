package com.dzpk.dealer;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

import com.ai.dz.config.AiRuleTemplate;
import com.dzpk.commission.repositories.mysql.model.ClubSuperior;
import com.dzpk.common.utils.LogUtil;
import lombok.Getter;
import org.apache.logging.log4j.Logger;

import com.dzpk.insurance.HolderPool;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;

/**
 * 荷官(用来纪录玩家的牌桌行为 也包括爆牌统计 数据统计 战绩统计)
 */
public class Dealer {
    private final Logger logger = LogUtil.getLogger(Dealer.class);
    private final Room room;
    private int maxPay = 0;
    private int maxPayLimit = 50000;                                        // 投保人最大能承受的单次赔付筹码数
    private int maxOutsLimit = 9;                                           // outs大于等于这个数 不控牌
    private final Set<Integer> exceeOuts = new HashSet<>();                       // outs列表
    private int insureCount = 0;                                            // 房间累计投保次数
    private int insureBoomCount = 0;                                        // 房间累计爆牌次数
    public boolean recordStatus7 = false;
    //private Set<Integer> watcherSet = new LinkedHashSet<>(100);                      // 观察者集合
    private final List<Integer> watcherList = new LinkedList<>();
    private int maxPot = 0;                                                 // 最大的锅
    @Getter
    private HolderPool holderPool = null;                                   // 保险池
    @Getter
    private final List<HolderPool> holderPoolList = new ArrayList<>();            // 所有投过保险的池

    // 玩家上次房间状态 {0:{0:主动离开 1:被动离开}，1:{0:过庄 1:补盲 2:正常}}
    private final Map<Integer, Object[]> userStatusMap = new HashMap<>();

    // 房间内打过牌的用户
    private final Map<Integer, Player> playerMap = new ConcurrentHashMap<>();

    public Dealer(Room room) {
        this.room = room;
    }

    // 获取房间内打过牌的所有人
    public Map<Integer, Player> getPlayers() {
        return Collections.unmodifiableMap(playerMap);
    }

    /**
     * 记录房间打牌数据
     *
     * @param userId
     * @param roomStatus
     */
    public void addWinCountData(int userId, int roomStatus) {
        logger.debug("win cnt room status:" + roomStatus);
        Player player = getPlayerByUserId(userId);
        if (roomStatus < 4) {
            player.setManpaiWinCnt(player.getManpaiWinCnt() + 1);
        }
        if (roomStatus >= 4) {
            player.setFanpaiWinCnt(player.getFanpaiWinCnt() + 1);
            logger.debug("fanpai win cnt +1, " + player.getUserId());
        }
        if (roomStatus >= 5) {
            player.setZhuanpaiWinCnt(player.getZhuanpaiWinCnt() + 1);
        }
        if (roomStatus >= 6) {
            player.setHepaiWinCnt(player.getHepaiWinCnt() + 1);
        }
        if (roomStatus == 7) {
            player.setTanpaiWinCnt(player.getTanpaiWinCnt() + 1);
            logger.debug("tanpai win cnt +1, " + player.getUserId());
        }
    }

    /**
     * 纪录房间打牌赢牌数据
     *
     * @param rp
     * @param roomStatus
     */
    private void addCountData(RoomPersion rp, int roomStatus) {
        Player player = getPlayerByUserId(rp.getUserId());
        if (roomStatus == 3) {
            player.setManpaiCnt(player.getManpaiCnt() + 1);
        } else if (roomStatus == 4) {
            player.setFanpaiCnt(player.getFanpaiCnt() + 1);
        } else if (roomStatus == 5) {
            player.setZhuanpaiCnt(player.getZhuanpaiCnt() + 1);
        } else if (roomStatus == 6) {
            player.setHepaiCnt(player.getHepaiCnt() + 1);
        } else if (roomStatus == 7) {
            player.setTanpaiCnt(player.getTanpaiCnt() + 1);
            logger.debug("tanpai cnt +1, " + player.getUserId());
        }
    }

    /**
     * 房间用户总手数
     *
     * @param userId
     */
    public void addHandCnt(int userId) {
        Player player = getPlayerByUserId(userId);
        player.setHandCnt(player.getHandCnt() + 1);
    }

    /**
     * 获取用户在房间打的手数
     */
    public int getHandCnt(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getHandCnt();
    }

    /**
     * 计算数据
     *
     * @param roomPersion
     * @param anteNumber
     */
    public void collect(RoomPersion roomPersion, int anteNumber) {
        Player player = getPlayerByUserId(roomPersion.getUserId());

        // 翻牌前
        if (room.getRoomStatus() == 3) {
            // 判断入池并统计进缓存
            if (anteNumber > 0 && roomPersion.getInPool() == 0) {
                roomPersion.setInPool(1);
                player.setInPoolCnt(player.getInPoolCnt() + 1);
                logger.debug("add in pool cnt user:" + player.getUserId());
            }

            // 被加注次数累加
            if (roomPersion.getClientStatus() == 3) {
                room.setAddedTimeBeforeFanpai(room.getAddedTimeBeforeFanpai() + 1);

                // 玩家翻牌前就加注
                if (roomPersion.getAddBeforeFanpai() == 0) {
                    roomPersion.setAddBeforeFanpai(1);
                    // addPersonData(roomPersion.getUserId(), Statistics.BCBET);
                }
            }

            // vpip:翻牌前主动往底池里投钱的频率
            if (anteNumber > 0 && roomPersion.getVpip() == 0) {
                roomPersion.setVpip(1);
                // addPersonData(roomPersion.getUserId(), Statistics.VPIP);
            }

            // per:翻牌前主动加注的频率
            if (roomPersion.getPfr() == 0 && roomPersion.getClientStatus() == 3) {
                roomPersion.setPfr(1);
                player.setPerCnt(player.getPerCnt() + 1);
            }

            // 3bet:对一个已经被加过注的池底再加注的概率(大盲注 -> 加注 -> 再加注)
            if (room.getAddedTimeBeforeFanpai() == 1) {
                if (roomPersion.getClientStatus() == 3) {
                    if (roomPersion.getBet3() == 0) {
                        roomPersion.setBet3(1);
                         player.setBet3(player.getBet3() + 1);
                    }
                }

                // 面对2BET的次数累加
                // addPersonData(roomPersion.getUserId(), Statistics.MBET2);
            }

            // f3bet:面对3bet弃牌的概率 弃牌后不会再有操作 不需要roomPersion状态
            if (room.getAddedTimeBeforeFanpai() == 2) {
                if (roomPersion.getClientStatus() == 6) {
                    // addPersonData(roomPersion.getUserId(), Statistics.FBET3);
                }

                //  面对3BET的次数累加
                // addPersonData(roomPersion.getUserId(), Statistics.MBET3);
            }

            // steal:翻牌前在小盲位 庄家位 关煞位就加注的算为偷盲
            if (roomPersion.getSteal() == 0 && (roomPersion.getSize() == room.getManzhuNumber()
                    || roomPersion.getSize() == room.getZhuangjiaNumber()
                    || roomPersion.getSize() == room.getGuanshaNumber())
                    ) {
                if (roomPersion.getClientStatus() == 3) {
                    roomPersion.setSteal(1);
                    room.setStolenBind(1);
                    player.setStealCnt(player.getStealCnt() + 1);
                }

                // 累加位置小盲位 庄家位 关煞位的次数 可以偷盲前
                player.setBStealCnt(player.getBStealCnt() + 1);
            }

            // resteal:面对偷盲的情况再加注(反偷盲)
            if (roomPersion.getReSteal() == 0 && room.getStolenBind() == 1) {
                if (roomPersion.getClientStatus() == 3) {
                    roomPersion.setReSteal(1);
                    // addPersonData(roomPersion.getUserId(), Statistics.RESTEAL);
                }

            }
        }else {
            // 判断入池并统计进缓存
            if (anteNumber > 0 && roomPersion.getInPool() == 0) {
                roomPersion.setInPool(1);
                player.setInPoolCnt(player.getInPoolCnt() + 1);
                logger.debug("add in pool cnt user:" + player.getUserId());
            }
        }

    }

    /**
     * 新的一手开始的时候要重置数据统计的状态
     *
     * @param roomPersion
     */
    public void resetPersonDataStatus(RoomPersion roomPersion) {
        roomPersion.setInPool(0);
        roomPersion.setVpip(0);
        roomPersion.setPfr(0);
        roomPersion.setBet3(0);
        roomPersion.setSteal(0);
        roomPersion.setReSteal(0);
        roomPersion.setAddBeforeFanpai(0);
        roomPersion.setAddAfterFanpaiFirst(0);
    }

    /**
     * 重置房间的统计数据状态
     */
    public void resetGameStatus() {
        recordStatus7 = false;
        room.setAddedTimeBeforeFanpai(0);
        room.setAddedTimeAfterFanpai(0);
        room.setStolenBind(0);
        //开启straddle且人数大于等于4人满足激进行为 不用考虑之前行动人操作
        logger.debug("resetGameStatus: room.getStraddle()=" + room.getStraddle() + " room.getPlayerCount()=" + room.getPlayerCount());
        if(room.getStraddle() && room.getPlayerCount() >= 4){
            room.setAfBeforeFanpai(true);
        }else{
            room.setAfBeforeFanpai(false);
        }

        logger.debug("resetGameStatus: AfBeforeFanpai=" + room.isAfBeforeFanpai());
    }

    /**
     * 记录一些数据
     */
    public void recordGameData() {
        RoomPersion[] roomPersions = room.getRoomPersions();
        int roomStatus = room.getRoomStatus();
        if (roomStatus >= 3 && roomStatus <= 6) {
            for (RoomPersion p : roomPersions) {
                if (p != null && p.getOnlinerType() != -1 && p.getStatus() >= 0) {
                    addCountData(p, roomStatus);
                }
            }
        } else if (roomStatus == 7) {
            // 比牌阶段如果牌桌上只剩一人的话 那不算摊牌
            if (recordStatus7 == false) {
                // 保险会重复跳status 7
                recordStatus7 = true;
                int countPersons = 0;
                for (RoomPersion p : roomPersions) {
                    if (p != null && p.getOnlinerType() != -1 && p.getStatus() >= 0) {
                        countPersons++;
                    }
                }
                logger.debug("countPersons:" + countPersons);

                if (countPersons > 1) {
                    for (RoomPersion p : roomPersions) {
                        if (p != null && p.getOnlinerType() != -1 && p.getStatus() >= 0) {
                            addCountData(p, roomStatus);
                            logger.debug("tanpai uid:" + p.getUserId());
                            p.setTanpai(true);
                        }
                    }
                }
            }
        }
    }

    /**
     * 记录用户在某个房间里的保险营收记录
     *
     * @param userId
     * @param insurance
     */
    public void addInsurance(int userId, int insurance) {
        Player player = getPlayerByUserId(userId);
        player.setInsurance(player.getInsurance() + insurance);
    }

    /**
     * 获取用户在某个房间里的保险营收记录
     *
     * @param userId
     * @return
     */
    public int getInsurance(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getInsurance();
    }

    /**
     * 记录用户在某个房间里的所有保险营收记录 包含：保险盈利和保险赔付
     *
     * @param userId
     * @param totalInsurance
     */
    public void addTotalInsurance(int userId, int totalInsurance) {
        Player player = getPlayerByUserId(userId);
        player.setTotalInsurance(player.getTotalInsurance() + totalInsurance);
        logger.debug("roomId={} userId={} totalInsurance={} getTotalInsurance={}", room.getRoomId(), userId, totalInsurance, player.getTotalInsurance());
    }

    /**
     * 获取用户在某个房间里的所有保险营收记录 包含：保险盈利和保险赔付
     *
     * @param userId
     * @return
     */
    public int getTotalInsurance(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getTotalInsurance();
    }

    /**
     * 添加房间观看者
     *
     * @param userId
     */
    public void addWatcher(int userId) {
        //watcherSet.add(userId);
        synchronized (watcherList) {
            if (watcherList.contains(userId))
                watcherList.remove((Object) userId);

            watcherList.add(0, userId);
        }
    }

    /**
     * 移除观察者
     *
     * @param userId
     */
    public void removeWatcher(int userId) {
        //watcherSet.remove(userId);
        synchronized (watcherList) {
            if (watcherList.contains(userId))
                watcherList.remove((Object) userId);
        }
    }

    /**
     * 读取房间观看者
     *
     * @return
     */
    public List<Integer> getWatchers() {
        return watcherList;
    }

    /**
     * 获取最大锅
     */
    public int getMaxPot() {
        return maxPot;
    }

    public void setMaxPot(int maxPot) {
        this.maxPot = maxPot;
    }

    /**
     * 设置玩家离开时玩家房间状态
     *
     * @param userId
     * @param status
     */
    public void setUserStatus(int userId, Object[] status) {
        userStatusMap.put(userId, status);
    }

    /**
     * 获取玩家上次离开时的状态
     *
     * @param userId
     * @return
     */
    public Object[] getUserStatus(int userId) {
        if (userStatusMap.get(userId) != null) {
            return userStatusMap.get(userId);
        }
        return null;
    }

    /**
     * 增加用户剩余筹码
     *
     * @param userId
     * @param left
     */
    public void addLeftChip(int userId, int left) {
        Player player = getPlayerByUserId(userId);
        player.setLeftChip(player.getLeftChip() + left);
    }

    /**
     * 设置用户房间剩余筹码
     *
     * @param userId
     * @param left
     */
    public void setLeftChip(int userId, int left) {
        Player player = getPlayerByUserId(userId);
        player.setLeftChip(left);
    }

    /**
     * 获取某个玩家的房间剩余筹码
     *
     * @param userId
     * @return
     */
    public int getUserLeftChip(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getLeftChip();
    }

    /**
     * 增加房间内个人带入总额
     *
     * @param userId
     * @param bringIn
     */
    public void addBringIn(int userId, int bringIn) {
        Player player = getPlayerByUserId(userId);
        player.setBringIn(player.getBringIn() + bringIn);
    }

    public int getBringIn(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getBringIn();
    }

    /**
     * 增加房间内个人营收
     *
     * @param userId
     * @param earn
     */
    public void addEarn(int userId, int earn) {
        Player player = getPlayerByUserId(userId);
        player.setEarn(player.getEarn() + earn);
    }

    public int getEarn(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getEarn();
    }

    /**
     * 个人jackpot 统计
     * @param userId
     * @param jackPot
     */
    public void addJackPot(int userId, int jackPot) {
        Player player = getPlayerByUserId(userId);
        player.setJackPotScore(player.getJackPotScore() + jackPot);
    }

    /**
     * 个人jackpot 真实投入统计
     * @param userId
     * @param jackPot
     */
    public void addJackPotReal(int userId, int jackPot) {
        Player player = getPlayerByUserId(userId);
        player.setJackPotRealScore(player.getJackPotRealScore() + jackPot);
    }

    /**
     * 个人jackpotbet 统计
     * @param userId
     * @param jackPot
     */
    public void addJackPotBet(int userId, int jackPot) {
        Player player = getPlayerByUserId(userId);
        player.setJackPotBetScore(player.getJackPotBetScore() + jackPot);
    }


    public int getJackPot(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getJackPotScore();
    }

    public int getJackPotBet(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getJackPotBetScore();
    }

    public int getJackPotReal(int userId) {
        Player player = getPlayerByUserId(userId);
        return player.getJackPotRealScore();
    }

    public int getMaxPayLimit() {
        return maxPayLimit;
    }

    public Set<Integer> getExceeOuts() {
        return exceeOuts;
    }

    public void resetExceeOuts() {
        exceeOuts.clear();
    }

    public void resetHolderPoolList(){
        holderPoolList.clear();
    }

    public int getMaxPay() {
        return maxPay;
    }

    public void setMaxPay(int maxPay) {
        this.maxPay = maxPay;
    }

    public int getInsureCount() {
        return insureCount;
    }

    public int getInsureBoomCount() {
        return insureBoomCount;
    }

    /**
     * 记下投保记录
     *
     * @param userId
     * @param holderPool
     */
    public void recordInsure(int userId, HolderPool holderPool) {
        logger.debug("recordInsure holderPool:" + holderPool);
        this.holderPool = holderPool;
        this.holderPoolList.add(holderPool);
        int insureChip = holderPool.getInsureChip();
        double odds = holderPool.getSelectedOdds();

        //  如果爆牌 需要赔多少钱
        int duePay = (int) Math.ceil(insureChip * odds);

        //  保存当轮超过最大赔付额的所有outs
        logger.debug("due pay:" + duePay + ", chip:" + insureChip + ",odds:" + odds);

        //  赔付额小于设置值 或者 outs数大于设置值 不控牌
        if (!(duePay < maxPayLimit || holderPool.getOuts().size() >= maxOutsLimit)) {
            Iterator<Integer> it = holderPool.getOuts().iterator();
            while (it.hasNext()) {
                exceeOuts.add(it.next());
            }
        }

        //  本房间投保次数递增
        insureCount++;
    }

    /**
     * 记录超出outs的底池信息
     * @param userId
     * @param holderPool
     */
    public void recordMaxBuyOutsLimitInsure(int userId,HolderPool holderPool){
        logger.debug("recordMaxBuyOutsLimitInsure,uid={},poolIndex={}",userId,holderPool.getPoolIndex());
        this.holderPool = holderPool;
        this.holderPoolList.add(holderPool);
    }

    /**
     * 记录不购买outs的底池信息
     * @param userId
     * @param holderPool
     */
    public void recordNoBuyOutsInsure(int userId,HolderPool holderPool){
        logger.debug("recordNoBuyOutsInsure,uid={},poolIndex={}",userId,holderPool.getPoolIndex());
        this.holderPool = holderPool;
        this.holderPoolList.add(holderPool);
    }

    /**
     * 记下爆牌记录
     */
    public void recordBoom() {
        insureBoomCount++;
    }

    /**
     * 通过uid找到allin用户
     *
     * @param userId
     * @return
     */
    public Player getPlayerByUserId(int userId) {
        if (playerMap.containsKey(userId)) {
            return playerMap.get(userId);
        } else {
            Player player = new Player();
            player.setUserId(userId);
            playerMap.put(userId, player);
            return player;
        }
    }

    /**
     * 累加玩家本局allin次数
     *
     * @param rp
     */
    public void addAllinCnt(RoomPersion rp) {
        Player player = getPlayerByUserId(rp.getUserId());
        player.setAllCnt(player.getAllCnt() + 1);
        logger.debug("add allin cnt:" + player.getUserId() + ", total:" + player.getAllCnt());
    }

    /**
     * 累加玩家本局af 加注次数
     * @param rp
     */
    public void addAfjiazhuCnt(RoomPersion rp) {
        Player player = getPlayerByUserId(rp.getUserId());
        player.setAfJiazhuCnt(player.getAfJiazhuCnt() + 1);
        logger.debug("add afjiazhu cnt:" + player.getUserId() + ", total:" + player.getAfJiazhuCnt());
    }

    /**
     * 累加玩家本局af 跟注次数
     * @param rp
     */
    public void addAfgenzhuCnt(RoomPersion rp) {
        Player player = getPlayerByUserId(rp.getUserId());
        player.setAfGenzhuCnt(player.getAfGenzhuCnt() + 1);
        logger.debug("add afgenzhu cnt:" + player.getUserId() + ", total:" + player.getAfGenzhuCnt());
    }

    /**
     * 累加玩家本局af 下注次数
     * @param rp
     */
    public void addAfxiazhuCnt(RoomPersion rp) {
        Player player = getPlayerByUserId(rp.getUserId());
        player.setAfXiazhuCnt(player.getAfXiazhuCnt() + 1);
        logger.debug("add afxiazhu cnt:" + player.getUserId() + ", total:" + player.getAfXiazhuCnt());
    }

    /**
     * 累加玩家allin输牌数 和 allin输钱数
     *
     * @param rp
     */
    public void addAllinLoseCnt(RoomPersion rp) {
        if (rp.getStatus() == 3 && rp.getYq() < 0) {
            Player player = getPlayerByUserId(rp.getUserId());
            player.setAllinLoseCnt(player.getAllinLoseCnt() + 1);
            player.setAllinEarn(player.getAllinEarn() + rp.getYq());
        }
    }

    /**
     * 累加cbet次数
     *
     * @param rp
     */
    public void addCBetCnt(RoomPersion rp) {
        // 上一轮的攻击者 && 当轮第一个行动
        if (rp.getSize() == room.getPreAttackId() && room.getBetFirstId() == rp.getSize()) {
            Player player = getPlayerByUserId(rp.getUserId());
            player.setCbet(player.getCbet() + 1);
            //logger.debug("cbet add:" + player.getUserId() + ", cbet cnt:" + player.getCbet());
        }
    }

    /**
     * 累加可cbet次数
     *
     * @param rp
     * @param anteNumber
     */
    public void addBCBetCnt(RoomPersion rp, int anteNumber) {
        try {
            // 上一轮的攻击者 && 本轮还没有人往池里继续投钱
            //logger.debug("pre attack id:" + room.getPreAttackId());
            logger.debug("rp size:" + rp.getSize());
            logger.debug("cur max chouma:" + room.getCurrentMaxChouma());
            logger.debug("max chouma:" + room.getMaxChouma());

            if (room.getCurrentMaxChouma() == room.getMaxChouma() && anteNumber > 0) {
                //logger.debug("set bet first user size:" + rp.getSize());
                room.setBetFirstId(rp.getSize());
            }

            if (room.getPreAttackId() == rp.getSize() && room.getCurrentMaxChouma() == room.getMaxChouma()) {
                Player player = getPlayerByUserId(rp.getUserId());
                player.setBCbet(player.getBCbet() + 1);
                //logger.debug("bcbet add:" + player.getUserId() + ", bcbet:" + player.getbCbet());
            }
        } catch (Exception e) {
            logger.debug("add bcbet error:", e);
        }
    }

    /**
     * 跳步比牌 数据统计跟上
     *
     * @param tem3
     */
    public void JumpAddCountData(int tem3) {
        int curStatus = room.getRoomStatus();
        if (curStatus != 6 && tem3 > 1) {
            RoomPersion[] roomPersions = room.getRoomPersions();
            for (int status = curStatus + 1; status < 7; status++) {
                for (int i = 0; i < roomPersions.length; i++) {
                    if (roomPersions[i] != null && roomPersions[i].getOnlinerType() != -1
                            && roomPersions[i].getStatus() >= 0 && room.getMaxPlayTime() > 0) {
                        addCountData(roomPersions[i], status);
                    }
                }
            }
        }
    }

    /**
     * 累计赢牌数据(入池赢牌 allin赢牌 摊牌赢牌)
     *
     * @param rp
     */
    public void addWinData(int roomId,RoomPersion rp) {
        Player player = getPlayerByUserId(rp.getUserId());
        // 累加入池赢牌数
        if (rp.getInPool() == 1 && rp.getYq() > 0) {
            player.setInPoolWinCnt(player.getInPoolWinCnt() + 1);
            logger.debug("add in pool win cnt user:" + player.getUserId() + ", yq:" + rp.getYq());
        }
        // 累加allin赢牌数 和 赢钱数
        if (rp.getStatus() == 3 && rp.getYq() > 0) {
            //ai玩家需要判断是否累加
            if(AiRuleTemplate.isAiUser(rp.getUserId()) && AiRuleTemplate.allinWinCntRandom()){
                player.setAllinWinCnt(player.getAllinWinCnt() + 1);
            }else{
                player.setAllinWinCnt(player.getAllinWinCnt() + 1);
            }

            player.setAllinEarn(player.getAllinEarn() + rp.getYq());
        }
    }

    /**
     * 摊牌的输赢总和
     *
     * @param rp
     */
    public void addTanpaiEarn(RoomPersion rp) {
        if (rp.getStatus() > 0) {
            Player player = getPlayerByUserId(rp.getUserId());
            player.setTanpaiEarn(player.getTanpaiEarn() + rp.getYq());
            logger.debug("player:" + rp.getUserId() + ", add taipai earn:"
                    + rp.getYq() + ", tanpai earn:" + player.getTanpaiEarn());
        }
    }

    /**
     * 累加赢牌手数
     *
     * @param userId
     * @param winChip
     */
    public void addWinCnt(int userId, int winChip) {
        if (winChip > 0) {
            Player player = getPlayerByUserId(userId);
            player.setWinCnt(player.getWinCnt() + 1);
        }
    }

    public void setSuperior(int userId, ClubSuperior superior) {
        Player player = getPlayerByUserId(userId);
        player.setSuperiorId(superior.getSuperiorId());
        player.setRefundRate(superior.getRefundRate());
    }

    public void forEachPlayers(BiConsumer<Integer, Player> callback) {
        Set<Integer> userIds = new HashSet<>(playerMap.keySet());
        for (int userId : userIds) {
            Player p = playerMap.get(userId);
            callback.accept(userId, p);
        }
    }
}
