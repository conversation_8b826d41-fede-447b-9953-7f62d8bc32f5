package com.dzpk.processor.impl;

import com.ai.dz.config.AiRuleTemplate;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.insurance.PoolChip;
import com.dzpk.processor.IProcessor;
import com.dzpk.record.AnteAction;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class Request_401_InsuranceRecieve implements IProcessor {

    private Logger logger = LogUtil.getLogger(Request_401_InsuranceRecieve.class);

    @Override
    public void handle(Task task) {
        int status = 1;
        int totalInsureChip = 0;
        int mustInsureChip = 0;    //系统背保
        int roomId = task.getRoomId();
        int roomPath = task.getRoomPath();
        Request request = (Request) task.getRequest();
        int userId = request.getUserId();
        int seat = (Integer) task.getMap().get(132);
        int insuranceStatus = (Integer) task.getMap().get(133);

        logger.debug("roomid: " + roomId + " userId:" + userId + "seat:" + seat + "insuranceStatus:" + insuranceStatus);

        Room room = Cache.getRoom(roomId, roomPath);
        RoomPersion roomPersion = room.getAudMap().get(userId);

        //机器人玩家直接返回失败
        if(AiRuleTemplate.isAiUser(roomPersion.getUserId())){
            logger.debug("AI user now operate!!");

            Object[][] objs = {
                    {60, 1, I366ClientPickUtil.TYPE_INT_1},                 // (0 成功 1 失败 2 强制背保  3 转牌购买保险超过底池的0.25  4 河牌购买保险超过底池的0.5)
                    {61, seat, I366ClientPickUtil.TYPE_INT_1},              // 座位号
                    {130, userId, I366ClientPickUtil.TYPE_INT_4},           // userid
                    {131, totalInsureChip, I366ClientPickUtil.TYPE_INT_4},  // 累计投保总额
                    {132, mustInsureChip, I366ClientPickUtil.TYPE_INT_4}    // 系统背保额
            };

            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPERATE);
            PublisherUtil.publisher(request, bytes);

            return;
        }

        if(!room.getInsuranceActive()) { //如果开了保险模式 并且保险未处于购买中状态
            Object[][] objs = {
                    {60, 5, I366ClientPickUtil.TYPE_INT_1},                 // (0 成功 1 失败 2 强制背保  3 转牌购买保险超过底池的0.25  4 河牌购买保险超过底池的0.5 5 房间未处于保险激活状态 不允许购买)
                    {61, seat, I366ClientPickUtil.TYPE_INT_1},              // 座位号
                    {130, userId, I366ClientPickUtil.TYPE_INT_4},           // userid
                    {131, totalInsureChip, I366ClientPickUtil.TYPE_INT_4},  // 累计投保总额
                    {132, mustInsureChip, I366ClientPickUtil.TYPE_INT_4}    // 系统背保额
            };

            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPERATE);
            PublisherUtil.publisher(request, bytes);

            return;
        }

        if (insuranceStatus == 1) {
            Integer[] poolIndexArr = (Integer[]) task.getMap().get(134);
            Integer[] chipArr = (Integer[]) task.getMap().get(135);
            Integer[] outsArr = (Integer[]) task.getMap().get(136);

            logger.debug("pool index:" + Arrays.toString(poolIndexArr));
            logger.debug("insurance chip:" + Arrays.toString(chipArr));
            logger.debug("insurance outs:" + Arrays.toString(outsArr));

            for (int i = 0; i < chipArr.length; i++) {
                totalInsureChip += chipArr[i];
            }

            //  把outs里面的null干掉（客户端的锅）
            List<Integer> noOutsArr = new ArrayList<Integer>();
            for (Integer out : outsArr) {
                if (out != null) {
                    noOutsArr.add(out);
                }
            }
            Integer[] realOutsArr = new Integer[noOutsArr.size()];
            for (int i = 0; i < noOutsArr.size(); i++) {
                realOutsArr[i] = noOutsArr.get(i);
            }
            logger.debug("real insurance outs:" + Arrays.toString(realOutsArr));

            //  多个池的所选outs由-1隔开
            List<Set<Integer>> outsList = new ArrayList<Set<Integer>>();
            Set<Integer> poolOuts = new HashSet<Integer>();
            for (int j = 0; j < realOutsArr.length; j++) {
                if (realOutsArr[j] == -1) {
                    outsList.add(poolOuts);
                    poolOuts = new HashSet<Integer>();
                } else {
                    poolOuts.add(realOutsArr[j]);
                    if (j == (realOutsArr.length - 1)) {
                        outsList.add(poolOuts);
                    }
                }
            }
            //  打个日志
            logger.debug("outs list size:" + outsList.size());
            for (int z = 0; z < outsList.size(); z++) {
                logger.debug("out list " + z + ":" + outsList.get(z).toString());
            }

            try {
                if (room != null) {
                    if (room.getT3() != -989) {
                        status = room.getInsurer().acceptInsurance(room, userId, poolIndexArr, chipArr, outsList);

                        // 牌谱回放，记录投保额
                        if (status == 0) {
                            try {
                                AnteAction anteAction = room.getRoomReplay().getAnteAction();
                                if (anteAction == null) {
                                    anteAction = new AnteAction();
                                }
                                Map<String, Object> actionMap = new HashMap<String, Object>();
                                actionMap.put("ACTION", "insure");
                                actionMap.put("NUMBER", room.getAudMap().get(userId).getSize());
                                actionMap.put("INSBUY", totalInsureChip);
                                logger.debug("action map " + actionMap.toString());
                                anteAction.getActions().add(actionMap);
                                if (room.getRoomReplay().getAnteAction() == null) {
                                    room.getRoomReplay().setAnteAction(anteAction);
                                }

                                mustInsureChip = room.getInsurer().calculateMustPay(userId);  //背保额
//                                int deanCount = 0;   //已发的公共牌
//                                for (int i = 4; i >= 0; i--) {
//                                    if (room.getPocer()[i] != null) {
//                                        break;
//                                    }
//                                    deanCount += 1;
//                                }

//                                ArrayList<PoolChip> poolList = room.getRoomService().getPoolList(true);
//                                PoolChip poolChip = poolList.get(0);  //拿到第一个分池数据

//                                logger.debug("leftcard: " + deanCount + " totalChips: " + poolChip.getTotalChips() + " totalInsureChip " + totalInsureChip);
//                                if(deanCount == 2 && totalInsureChip > poolChip.getTotalChips() * 0.25){    // 转牌购买保险超过底池的0.25
//                                    status = 3;
//                                }else if(deanCount == 1 && totalInsureChip > poolChip.getTotalChips() * 0.5){   // 河牌购买保险超过底池的0.5
//                                    status = 4;
//                                }

                            } catch (Exception e) {
                                logger.error("", e);
                            }
                        }
                    } else {
                        logger.debug("cannot insure in not insure status!");
                    }
                }
            } catch (Exception e) {
                logger.debug("insurance fail", e);
            }
        } else {
            try {
                room = Cache.getRoom(roomId, roomPath);
                if (room != null) {
                    Integer[] ret = room.getInsurer().acceptInsurance(room, userId);
                    status = ret[0];
                    mustInsureChip = ret[1];

                    logger.debug("Request_401_InsuranceRecieve =======status=" + status + " insuranceStatus=" + insuranceStatus);
                }
            } catch (Exception e) {
                logger.debug("insurance fail", e);
            }
        }


        /**
         * 玩家挑选部分OUTS时需要增加背保的额度
         * 计入转牌购买的总保险额 = 背保额 + 转牌购买的保险
         */
        int realTotalInsureChip = 0;
        realTotalInsureChip = totalInsureChip + mustInsureChip;

        room.getInsurer().getTurnChipMap().put(userId,realTotalInsureChip);

        logger.debug("holder status:" + status);
        logger.debug("holder seat:" + seat);
        logger.debug("holder userId:" + userId);
        logger.debug("holder totalInsureChip= " + totalInsureChip);
        logger.debug("holder mustInsureChip= " + mustInsureChip);


        Object[][] objs = {
                {60, status, I366ClientPickUtil.TYPE_INT_1},            // (0 成功 1 失败 2 强制背保  3 转牌购买保险超过底池的0.25  4 河牌购买保险超过底池的0.5)
                {61, seat, I366ClientPickUtil.TYPE_INT_1},              // 座位号
                {130, userId, I366ClientPickUtil.TYPE_INT_4},           // userid
                {131, totalInsureChip, I366ClientPickUtil.TYPE_INT_4},  // 累计投保总额
                {132, mustInsureChip, I366ClientPickUtil.TYPE_INT_4}    // 系统背保额
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPERATE);
        PublisherUtil.send(room,bytes, userId);
        PublisherUtil.publisher(request, bytes);
    }

}
