package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import com.dzpk.insurance.PoolChip;
import com.dzpk.processor.IProcessor;
import com.dzpk.record.AnteAction;
import com.dzpk.work.Task;
import com.i366.cache.Cache;

import com.i366.constant.Constant;
import com.i366.model.player.RoomPersion;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 *  AI机器人购买保险操作
 */
public class Task_10021 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10021.class);

    private Map<Integer, Double> oddsMap = new HashMap<Integer, Double>() {             //  保险人的赔率表
        {
            put(0, 0.0);
            put(1, 30.0);
            put(2, 16.0);
            put(3, 10.0);
            put(4, 8.0);
            put(5, 6.0);
            put(6, 5.0);
            put(7, 4.0);
            put(8, 3.5);
            put(9, 3.0);
            put(10, 2.5);
            put(11, 2.2);
            put(12, 2.0);
            put(13, 1.8);
            put(14, 1.6);
            put(15, 1.4);
            put(16, 1.3);

            //  以下为背保赔率
            put(17, 1.2);
            put(18, 1.1);
            put(19, 1.0);
            put(20, 0.8);
        }
    };

    @Override
    public void handle(Task task) {

        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        if (null != room) {

            int userId = (int) task.getMap().get(1);
            int insuranceStatus = (int) task.getMap().get(2);

            logger.debug("Task_10021 ai auto buy insucer,userId: " + userId + " ,insuranceStatus: " + insuranceStatus);

            RoomPersion roomPersion = room.getAudMap().get(userId);

            int status = 1;
            int totalInsureChip = 0;
            int mustInsureChip = 0;    //系统背保

            if (insuranceStatus == 1) {   // 0不购买  1 购买

                roomPersion.setAiInsurerBuyTimes( roomPersion.getTimeoutCheckOpTimes() + 1);  //增加ai玩家每手购买保险的次数
                Integer[] outsArr = (Integer[]) task.getMap().get(3);
                logger.debug("insurance outs:" + Arrays.toString(outsArr));

                totalInsureChip = (int) task.getMap().get(4);
                Integer[] chipArr = {totalInsureChip};
                logger.debug("chipArr: " +  Arrays.toString(chipArr));

                Integer[] poolIndexArr = (Integer[]) task.getMap().get(5);
                logger.debug("poolIndexArr: " +  Arrays.toString(poolIndexArr));

                //  把outs里面的null干掉（客户端的锅）
                List<Integer> noOutsArr = new ArrayList<Integer>();
                for (Integer out : outsArr) {
                    if (out != null) {
                        noOutsArr.add(out);
                    }
                }
                Integer[] realOutsArr = new Integer[noOutsArr.size()];
                for (int i = 0; i < noOutsArr.size(); i++) {
                    realOutsArr[i] = noOutsArr.get(i);
                }
                logger.debug("real insurance outs:" + Arrays.toString(realOutsArr));

                //  多个池的所选outs由-1隔开
                List<Set<Integer>> outsList = new ArrayList<Set<Integer>>();
                Set<Integer> poolOuts = new HashSet<Integer>();
                for (int j = 0; j < realOutsArr.length; j++) {
                    if (realOutsArr[j] == -1) {
                        outsList.add(poolOuts);
                        poolOuts = new HashSet<Integer>();
                    } else {
                        poolOuts.add(realOutsArr[j]);
                        if (j == (realOutsArr.length - 1)) {
                            outsList.add(poolOuts);
                        }
                    }
                }
                //  打个日志
                logger.debug("outs list size:" + outsList.size());
                for (int z = 0; z < outsList.size(); z++) {
                    logger.debug("out list " + z + ":" + outsList.get(z).toString());
                }

                try {
                    if (room != null) {
                        if (room.getT3() != -989) {
                            status = room.getInsurer().acceptInsurance(room, userId, poolIndexArr, chipArr, outsList);

                            // 牌谱回放，记录投保额
                            if (status == 0) {
                                try {
                                    AnteAction anteAction = room.getRoomReplay().getAnteAction();
                                    if (anteAction == null) {
                                        anteAction = new AnteAction();
                                    }
                                    Map<String, Object> actionMap = new HashMap<String, Object>();
                                    actionMap.put("ACTION", "insure");
                                    actionMap.put("NUMBER", room.getAudMap().get(userId).getSize());
                                    actionMap.put("INSBUY", totalInsureChip);
                                    logger.debug("action map " + actionMap.toString());
                                    anteAction.getActions().add(actionMap);
                                    if (room.getRoomReplay().getAnteAction() == null) {
                                        room.getRoomReplay().setAnteAction(anteAction);
                                    }

                                    mustInsureChip = room.getInsurer().calculateMustPay(userId);  //背保额
//                                    int deanCount = 0;   //已发的公共牌
//                                    for (int i = 4; i >= 0; i--) {
//                                        if (room.getPocer()[i] != null) {
//                                            break;
//                                        }
//                                        deanCount += 1;
//                                    }
//
//                                    ArrayList<PoolChip> poolList = room.getRoomService().getPoolList(true);
//                                    PoolChip poolChip = poolList.get(0);  //拿到第一个分池数据
//
//                                    logger.debug("leftcard: " + deanCount + " totalChips: " + poolChip.getTotalChips() + " totalInsureChip " + totalInsureChip);
//                                    if(deanCount == 2 && totalInsureChip > poolChip.getTotalChips() * 0.25){    // 转牌购买保险超过底池的0.25
//                                        status = 3;
//                                    }else if(deanCount == 1 && totalInsureChip > poolChip.getTotalChips() * 0.5){   // 河牌购买保险超过底池的0.5
//                                        status = 4;
//                                    }

                                } catch (Exception e) {
                                    logger.error("", e);
                                }
                            }
                        } else {
                            logger.debug("cannot insure in not insure status!");
                        }
                    }
                } catch (Exception e) {
                    logger.debug("insurance fail", e);
                }
            } else {
                try {
                    room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
                    if (room != null) {
                        Integer[] ret = room.getInsurer().acceptInsurance(room, userId);
                        status = ret[0];
                        mustInsureChip = ret[1];

                        logger.debug("Task_10021 Recieve =======status=" + status + " insuranceStatus=" + insuranceStatus);
                    }
                } catch (Exception e) {
                    logger.debug("insurance fail", e);
                }
            }


            /**
             * 玩家挑选部分OUTS时需要增加背保的额度
             * 计入转牌购买的总保险额 = 背保额 + 转牌购买的保险
             */
            int realTotalInsureChip = 0;
            realTotalInsureChip = totalInsureChip + mustInsureChip;

            room.getInsurer().getTurnChipMap().put(userId,realTotalInsureChip);
            int seat = roomPersion.getSize();
            logger.debug("holder status:" + status);
            logger.debug("holder seat:" + seat);
            logger.debug("holder userId:" + userId);
            logger.debug("holder totalInsureChip= " + totalInsureChip);
            logger.debug("holder mustInsureChip= " + mustInsureChip);

            Object[][] objs = {
                    {60, status, I366ClientPickUtil.TYPE_INT_1},            // (0 成功 1 失败 2 强制背保  3 转牌购买保险超过底池的0.25  4 河牌购买保险超过底池的0.5)
                    {61, seat, I366ClientPickUtil.TYPE_INT_1},              // 座位号
                    {130, userId, I366ClientPickUtil.TYPE_INT_4},           // userid
                    {131, totalInsureChip, I366ClientPickUtil.TYPE_INT_4},  // 累计投保总额
                    {132, mustInsureChip, I366ClientPickUtil.TYPE_INT_4}    // 系统背保额
            };

            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_INSURANCE_OPERATE);
            PublisherUtil.send(room,bytes);

            room.roomProcedure.delayTaskMap.remove(task.getId());
        }else{
            logger.debug("error not ai user !!!");
        }

    }
}
