package com.dzpk.processor.impl;

import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 结束保险任务
 * <AUTHOR>
 *
 */
public class Task_10010 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10010.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        logger.debug("roomId: " + task.getRoomId() + ", roomPath: " + task.getRoomPath());
        // 结束保险
        if (room != null) {
            logger.debug("setInsuranceActive: false Task_10010 roomId: " + task.getRoomId());
            room.getInsurer().getTurnChipMap().clear();
            room.setInsuranceActive(false);
            room.setRoomStatus(7);
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
