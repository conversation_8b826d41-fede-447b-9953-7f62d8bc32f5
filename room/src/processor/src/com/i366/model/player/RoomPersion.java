
package com.i366.model.player;

import com.ai.dz.config.AiRoomManager;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.model.UserInfo;
import com.i366.constant.LeaveRoomCode;
import com.i366.model.pocer.Pocer;
import com.i366.model.room.Room;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 玩家信息
 */
public class RoomPersion {
    private boolean firstSmallGamel = false; // true表示需要进行第一小局进行数据恢复，false正常扣除
    private Integer preIntegral = 0; // 初始化第一局牌的时候，预扣的积分数据
    private int userId;
    private int chouma = 0;                            //	进入游戏时携带的筹码数量 即是总共的 每次添加筹码是减少 (未和数据库同步)每次添加筹码时同步
    private int nowcounma = 0;                        //	当前剩余筹码量 每次下注后减少 每局结束增加 //比赛房间固定为2000
    private int betChouma = 0;                        //	已经下注的筹码
    private Pocer[] pocers = new Pocer[2];            //	玩家的两张牌
    private int status = 0;                        //	玩家当前状态 0准备下注/或开始状态  1让牌 2下注 3全下 -1掉线托管  -2第二次拖/或者最后一次发牌掉线() -3弃牌() -4站起
    private int clientStatus = 0;                    //	1:下注  2:跟注  3:加注  4:全下 5:让牌  6:弃牌 7托管
    private int type = 1;                            //	玩家状态 1观看 2准备下一次进入  3游戏中 4坐下状态 5超时站起
    private long time;                                //	倒计时开始时间
    private int size = -1;                            // 	玩家座位号 0 ~ 8
    private int lastSize = -1;                        //	上一位玩家编号
    private int betStatus = 0;                        //	0 等待下注     1下注并且比上次betChouma有增加     2筹码没有增加
    private int anteNumber = 0;                    //	下注筹码数 指的是最后一次下注的筹码数量
    private Pocer[] zuidaPocers = new Pocer[5];    //	最后一轮最大牌的那5张牌
    private int pocerType;                            //	最大5张牌的类型 ////最后一轮最大牌 类型
    private int yq;                                //	最后一轮赢了多少筹码
    private int insuranceLast;                        //	最近一手保险的正负总营收
    private int winInsuranceLast;                    //	最近一手保险的正营收
    private int onlinerType = 1;                    //	在线状态 1在线 -1 掉线做T掉处理
    private int lastChouma;                            //	最后一轮下来多少筹码
    private int anteCount;                            //	本轮已经下了多少注
    private int ys = -1;                            //	最后一盘是赢还是输 -1 输 1赢
    private int zdlk = -1;                            //	-1 表示非主动离开 ..其它表示主动离开
    private long addUpTime;                            //	记录一盘游戏进行时间
    private int ac;                                    //	当天累计局数
    private int wc;                                    //	连赢次数
    private int isGame = -999;
    private int fundLast = 0;                        //	本手需要被抽多少水
    private boolean muck = false;                    // 	本手是否盖牌
    private boolean tanpai = false;                    //	本手摊牌

    /*************** 数据统计相关变量 ****************/
    private int inPool;                                //	入池
    private int vpip;                                //	翻牌前是否主动往底池里投钱
    private int pfr;                                //	翻牌前是否主动往底池里加注
    private int bet3;                                //	翻牌前是否二次加注过
    private int steal;                                //	翻牌前偷盲
    private int reSteal;                            //	翻牌前反偷盲
    private int addBeforeFanpai;                    //	翻牌前就加注
    private int addAfterFanpaiFirst;                //	翻牌后又加注
    /*************** 数据统计相关变量 ****************/


    private int requestChip = 0;                    //	玩家请求下盘加入筹码数，同一局游戏中后面请求覆盖前面的，直到下一局开局才生效
    private int timeoutCheckOpTimes = 0;            //	超时自动过牌操作次数
    private int timeoutFoldOpTimes = 0;                //	超时自动弃牌操作次数
    private int showCardsId = 0;                    //	亮牌id 0不亮 1第一张 2第二张 3两张

    private boolean haveJackpotRewad = false;        //如果为true，则要发放奖励，如果为false，则没有对应奖励

    private boolean canPlay = true;                 // 能否参与牌局
    private int passHand = 0;                       // 玩家是否显示气泡 0不显示  大于0显示
    private int preStage = -1;                      // 上一手
    //下局自动补盲若为大盲小盲庄家这局过庄下局继续自动补盲
    private int playType = 0;                        // 过庄0 补盲1 正常2
    private int standupType = 0;                    // 离开牌桌类型 主动0 被动1 冻结2
    private int straddleChip = 0;                   // straddle需要chip
    private boolean isAutoOp = false;               // 托管
    private boolean active = false;                 // 是否主动托管

    private boolean isStandUp = false;              //是否被站起
    private boolean isKickOut = false;              //是否被踢出
    private boolean isApplyFor = false;             //是否申请过带入
    private long applyForBeginTime = 0;             //申请带入时间
    private int beforeAllinScore = -1;              //不够前注时,玩家all in之前的筹码数

    private boolean equalOuts = false;           //河牌购买保险时是否存在平分Outs

    /*************** AI相关相关变量 ****************/
    private int aiInsurerBuyTimes = 0;              //ai玩家每手投保次数
    private boolean aiWin = false;                  //ai比牌结果 true赢 false输
    private int handBeginChip = 0;                  //每手筹码初始值
    /*************** AI相关相关变量 ****************/

    // 玩家上次还在托管的房间号
    private int lastRoomId;
    private int lastRoomPath;

    private UserInfo userInfo;
    private boolean canSeeHandCard;
    // 是否下局离开 正常0 站起1 2离开
    private int nextGameStandup = 0;
    //预删除对象0正常1为不正常
    private int delUserInfo;
    //游戏状态是否坐下即开游戏；
    private int gameType = -1;

    //补盲玩家，每局补盲玩家
    private HashMap<Integer, Integer> bumang = new HashMap<>();

    public HashMap<Integer, Integer> getBumang() {
        return bumang;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        if (logger.isTraceEnabled()) {
            logger.trace("U-{} canPlay={} gameType={} -> {}", this.userId, this.canPlay, this.gameType, gameType);
        }
        this.gameType = gameType;
    }


    public int getLastSize() {
        return lastSize;
    }

    public void setLastSize(int lastSize) {
        this.lastSize = lastSize;
    }

    public int getDelUserInfo() {
        return delUserInfo;
    }

    public void setDelUserInfo(int delUserInfo) {
        this.delUserInfo = delUserInfo;
    }

    public int getNextGameStandup() {
        return nextGameStandup;
    }

    public void setNextGameStandup(int nextGameStandup) {
        this.nextGameStandup = nextGameStandup;
    }

    public boolean isCanSeeHandCard() {
        return canSeeHandCard;
    }

    public void setCanSeeHandCard(boolean canSeeHandCard) {
        this.canSeeHandCard = canSeeHandCard;
    }

    //用户角色 SB BB UTG UTG+1  UTG+2   MP HJ CO BTN
    private Integer role;

    private Logger logger = LogUtil.getLogger(RoomPersion.class);

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public long getAddUpTime() {
        return addUpTime;
    }

    public void setAddUpTime(long addUpTime) {
        this.addUpTime = addUpTime;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getIsGame() {
        return isGame;
    }

    /**
     * 玩过游戏....>0 1
     *
     * @param isGame
     */
    public void setIsGame(int isGame) {
        this.isGame = isGame;
    }

    public int getAc() {
        return ac;
    }

    public void setAc(int ac) {
        this.ac = ac;
    }

    public int getWc() {
        return wc;
    }

    public void setWc(int wc) {
        this.wc = wc;
    }

    /**
     * 进入游戏时携带的筹码数量
     *
     * @return
     */
    public int getChouma() {
        return chouma;
    }

    /**
     * 进入游戏时携带的筹码数量 即是总共的 添加到nowcounma做减去操作
     *
     * @return
     */
    public synchronized void setChouma(int chouma) {
        this.chouma = chouma;
    }

    /**
     * //当前剩余筹码量
     *
     * @return
     */
    public int getNowcounma() {
        return nowcounma;
    }

    /**
     * //当前剩余筹码量
     *
     * @return
     */
    public void setNowcounma(int nowcounma) {
        this.nowcounma = nowcounma;
    }

    public Pocer[] getPocers() {
        return pocers;
    }

    public void setPocers(Pocer[] pocers) {
        this.pocers = pocers;
    }

    /**
     * 玩家当前状态 0准备下注/或开始状态  1让牌 2下注 3全下 -1掉线托管 -2第二次拖/或者最后一次发牌掉线() -3弃牌() -4站起
     *
     * @return
     */
    public int getStatus() {
        return status;
    }

    /**
     * 玩家当前状态 0准备下注/或开始状态  1让牌 2下注 3全下 -1掉线托管 -2第二次拖/或者最后一次发牌掉线() -3弃牌() -4站起
     *
     * @param status
     */
    public void setStatus(int status) {
        this.status = status;
    }

    public long getTime() {
        return time;
    }

    /**
     * 玩家状态 1观看 2准备下一次进入  3游戏中 4坐下状态
     *
     * @param time
     */
    public void setTime(long time) {
        this.time = time;
    }

    /**
     * 玩家状态 1观看 2准备下一次进入  3游戏中 4坐下状态 5超时站起 6占座状态
     *
     * @return
     */
    public int getType() {
        return type;
    }

    /**
     * 玩家状态 1观看 2准备下一次进入  3游戏中 4坐下状态 5超时站起 6进入房间占座
     *
     * @param type
     */
    public void setType(int type) {
        this.type = type;
    }

    /**
     * 获取玩家座位编号
     *
     * @return
     */
    public int getSize() {
        return size;
    }

    /**
     * 设置玩家座位编号
     *
     * @param size
     */
    public void setSize(int size) {
        this.size = size;
    }

    /**
     * 已经下注的筹码
     *
     * @return
     */
    public int getBetChouma() {
        return betChouma;
    }

    /**
     * 已经下注的筹码
     *
     * @param betChouma
     */
    public void setBetChouma(int betChouma) {
        this.betChouma = betChouma;
    }

    /**
     * 0 等待下注     1下注并且比上次betChouma有增加     2筹码没有增加
     *
     * @return
     */
    public int getBetStatus() {
        return betStatus;
    }

    /**
     * 0 等待下注     1下注并且比上次betChouma有增加     2筹码没有增加
     *
     * @param betStatus
     */
    public void setBetStatus(int betStatus) {
        this.betStatus = betStatus;
    }

    /**
     * 1:下注  2:跟注  3:加注  4:全下 5:让牌  6:弃牌 7托管
     *
     * @return
     */
    public int getClientStatus() {
        return clientStatus;
    }

    /**
     * 1:下注  2:跟注  3:加注  4:全下 5:让牌  6:弃牌 7托管
     *
     * @param clientStatus
     */
    public void setClientStatus(int clientStatus) {
        this.clientStatus = clientStatus;
    }

    /**
     * 下注筹码数 指的是最后一次下注的筹码数量
     *
     * @return
     */
    public int getAnteNumber() {
        return anteNumber;
    }

    /**
     * 下注筹码数 指的是最后一次下注的筹码数量
     *
     * @param anteNumber
     */
    public void setAnteNumber(int anteNumber) {
        this.anteNumber = anteNumber;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public Pocer[] getZuidaPocers() {
        return zuidaPocers;
    }

    public void setZuidaPocers(Pocer[] zuidaPocers) {
        this.zuidaPocers = zuidaPocers;
    }

    public int getPocerType() {
        return pocerType;
    }

    public void setPocerType(int pocerType) {
        this.pocerType = pocerType;
    }

    public int getYq() {
        return yq;
    }

    public void setYq(int yq) {
        this.yq = yq;
    }

    /**
     * 在线状态 1在线 -1 掉线做T掉处理
     *
     * @return
     */
    public int getOnlinerType() {
        return onlinerType;
    }

    /**
     * 在线状态 1在线 -1 掉线做T掉处理
     *
     * @param onlinerType
     */
    public void setOnlinerType(int onlinerType) {
        this.onlinerType = onlinerType;
    }

    public int getLastChouma() {
        return lastChouma;
    }

    public void setLastChouma(int lastChouma) {
        this.lastChouma = lastChouma;
    }

    public int getYs() {
        return ys;
    }

    public void setYs(int ys) {
        this.ys = ys;
    }

    /**
     * 本轮已经下了多少注
     *
     * @return
     */
    public int getAnteCount() {
        return anteCount;
    }

    /**
     * 本轮已经下了多少注
     *
     * @param anteCount
     */
    public void setAnteCount(int anteCount) {
        this.anteCount = anteCount;
    }

    /**
     * -1 表示非主动离开 ..其它表示主动离开
     *
     * @return
     */
    public int getZdlk() {
        return zdlk;
    }

    /**
     * -1 表示非主动离开 ..其它表示主动离开
     *
     * @param zdlk
     */
    public void setZdlk(int zdlk) {
        this.zdlk = zdlk;
    }

    public void setInPool(int inPool) {
        this.inPool = inPool;
    }

    public int getInPool() {
        return inPool;
    }

    public void setFirstSmallGamel(boolean firstSmallGamel) {
        this.firstSmallGamel = firstSmallGamel;
    }

    public void setPreIntegral(Integer preIntegral) {
        this.preIntegral = preIntegral;
    }

    public Integer getPreIntegral() {
        return preIntegral;
    }

    public boolean isFirstSmallGamel() {
        return firstSmallGamel;
    }

    public void setRequestChip(int requestChip) {
        this.requestChip = requestChip;
    }

    public int getRequestChip() {
        return requestChip;
    }

    public void setTimeoutCheckOpTimes(int timeoutCheckOpTimes) {
        this.timeoutCheckOpTimes = timeoutCheckOpTimes;
    }

    public int getTimeoutCheckOpTimes() {
        return timeoutCheckOpTimes;
    }

    public void setTimeoutFoldOpTimes(int timeoutFoldOpTimes) {
        this.timeoutFoldOpTimes = timeoutFoldOpTimes;
    }

    public int getTimeoutFoldOpTimes() {
        return timeoutFoldOpTimes;
    }

    public void addTimeoutCheckOpTimes(int times) {
        timeoutCheckOpTimes += times;
    }

    public void addTimeoutFoldOpTimes(int times) {
        timeoutFoldOpTimes += times;
    }

    public int getShowCardsId() {
        return showCardsId;
    }

    public void setShowCardsId(int showCardsId) {
        this.showCardsId = showCardsId;
    }

    public void setVpip(int vpip) {
        this.vpip = vpip;
    }

    public int getVpip() {
        return vpip;
    }

    public void setPfr(int pfr) {
        this.pfr = pfr;
    }

    public int getPfr() {
        return pfr;
    }

    public void setBet3(int bet3) {
        this.bet3 = bet3;
    }

    public int getBet3() {
        return bet3;
    }

    public void setSteal(int steal) {
        this.steal = steal;
    }

    public int getSteal() {
        return steal;
    }

    public void setReSteal(int reSteal) {
        this.reSteal = reSteal;
    }

    public int getReSteal() {
        return reSteal;
    }

    public void setAddBeforeFanpai(int addBeforeFanpai) {
        this.addBeforeFanpai = addBeforeFanpai;
    }

    public int getAddBeforeFanpai() {
        return addBeforeFanpai;
    }

    public void setAddAfterFanpaiFirst(int addAfterFanpaiFirst) {
        this.addAfterFanpaiFirst = addAfterFanpaiFirst;
    }

    public int getAddAfterFanpaiFirst() {
        return addAfterFanpaiFirst;
    }

    public boolean isCanPlay() {
        return canPlay;
    }

    public void setCanPlay(boolean canPlay) {
        this.canPlay = canPlay;
    }

    public int getInsuranceLast() {
        return insuranceLast;
    }

    public void setInsuranceLast(int insuranceLast) {
        //logger.debug("setInsuranceLast: " + insuranceLast);
        this.insuranceLast = insuranceLast;
    }

    public int getWinInsuranceLast() {
        return winInsuranceLast;
    }

    public void setWinInsuranceLast(int winInsuranceLast) {
        this.winInsuranceLast = winInsuranceLast;
    }

    public int getPassHand() {
        return passHand;
    }

    public void setPassHand(int passHand) {
        this.passHand = passHand;
    }

    public int getPreStage() {
        return preStage;
    }

    public void setPreStage(int preStage) {
        this.preStage = preStage;
    }

    public int getPlayType() {
        return playType;
    }

    public void setPlayType(int playType) {
        this.playType = playType;
    }

    public int getStandupType() {
        return standupType;
    }

    public void setStandupType(int standupType) {
        this.standupType = standupType;
    }

    public int getStraddleChip() {
        return straddleChip;
    }

    public void setStraddleChip(int straddleChip) {
        this.straddleChip = straddleChip;
    }

    public int getFundLast() {
        return fundLast;
    }

    public void setFundLast(int fundLast) {
        this.fundLast = fundLast;
    }

    public boolean isAutoOp() {
        return isAutoOp;
    }

    public void setAutoOp(boolean isAutoOp) {
        this.isAutoOp = isAutoOp;
    }

    public int getLastRoomId() {
        return lastRoomId;
    }

    public void setLastRoomId(int lastRoomId) {
        this.lastRoomId = lastRoomId;
    }

    public int getLastRoomPath() {
        return lastRoomPath;
    }

    public void setLastRoomPath(int lastRoomPath) {
        this.lastRoomPath = lastRoomPath;
    }

    public void setMuck(boolean muck) {
        this.muck = muck;
    }

    public boolean getMuck() {
        return muck;
    }

    public void setTanpai(boolean tanpai) {
        this.tanpai = tanpai;
    }

    public boolean getTanpai() {
        return tanpai;
    }

    /**
     * @return the active
     */
    public boolean isActive() {
        return active;
    }

    /**
     * @param active the active to set
     */
    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isStandUp() {
        return isStandUp;
    }

    public void setStandUp(boolean standUp) {
        isStandUp = standUp;
    }

    public boolean isKickOut() {
        return isKickOut;
    }

    public void setKickOut(boolean kickOut) {
        isKickOut = kickOut;
    }

    public boolean isApplyFor() {
        return isApplyFor;
    }

    public void setApplyFor(boolean applyFor) {
        isApplyFor = applyFor;
    }

    public long getApplyForBeginTime() {
        return applyForBeginTime;
    }

    public void setApplyForBeginTime(long applyForBeginTime) {
        this.applyForBeginTime = applyForBeginTime;
    }

    public int getBeforeAllinScore() {
        return beforeAllinScore;
    }

    public void setBeforeAllinScore(int beforeAllinScore) {
        this.beforeAllinScore = beforeAllinScore;
    }

    public boolean isEqualOuts() {
        return equalOuts;
    }

    public void setEqualOuts(boolean equalOuts) {
        this.equalOuts = equalOuts;
    }

    public int getAiInsurerBuyTimes() {
        return aiInsurerBuyTimes;
    }

    public void setAiInsurerBuyTimes(int aiInsurerBuyTimes) {
        this.aiInsurerBuyTimes = aiInsurerBuyTimes;
    }

    public boolean isAiWin() {
        return aiWin;
    }

    public void setAiWin(boolean aiWin) {
        this.aiWin = aiWin;
    }

    public int getHandBeginChip() {
        return handBeginChip;
    }

    public void setHandBeginChip(int handBeginChip) {
        this.handBeginChip = handBeginChip;
    }

    public boolean isHaveJackpotRewad() {
        return haveJackpotRewad;
    }

    public void setHaveJackpotRewad(boolean haveJackpotRewad) {
        this.haveJackpotRewad = haveJackpotRewad;
    }

    /**
     * 获取用户翻牌前强制下注的筹码
     * @param room
     * @return
     */
    public int getFirstAnteCount(Room room){
        int firstAnteCount=0;
        if (room.getRoomStatus()==3){
            if (room.getDamanzhuNumber()==this.getSize()){
                firstAnteCount=room.getDamanzhu();
            }else if (room.getManzhuNumber()==this.getSize()){
                firstAnteCount=room.getManzhu();
            }else if (room.getStraddleNumber()==this.getSize()){
                firstAnteCount=room.getDamanzhu()+room.getDamanzhu();
            }
        }
        return firstAnteCount;
    }

    private ScheduledExecutorService executorService=null;
    public void startWaitingTask (int waitingTimeSec,Runnable callBack) {
        if (executorService != null) {
            return;
        }else {
            executorService = Executors.newScheduledThreadPool(1);
        }
        executorService.schedule(() -> {
            if (callBack !=null) {
                callBack.run();
            }
        }, waitingTimeSec, TimeUnit.SECONDS);

    }
    public void cancelWaitingTask(Runnable callBack) {
        if (executorService != null) {
            if (callBack != null) {
                callBack.run();
            }
            //关闭等待任务
            executorService.shutdownNow();
            executorService = null;
        }
    }
}
