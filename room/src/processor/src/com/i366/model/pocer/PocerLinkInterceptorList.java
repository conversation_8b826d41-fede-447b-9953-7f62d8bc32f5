package com.i366.model.pocer;

import com.dzpk.insurance.HolderPool;
import com.i366.model.pocer.PocerLink.PocerLinkInterceptor;
import com.i366.model.room.Room;

import java.util.Arrays;
import java.util.List;

public class PocerLinkInterceptorList implements PocerLinkInterceptor {

    private final List<PocerLinkInterceptor> interceptors;

    public PocerLinkInterceptorList(List<PocerLinkInterceptor> interceptors) {
        this.interceptors = interceptors;
    }

    public static PocerLinkInterceptorList of(PocerLinkInterceptor... interceptors) {
        return new PocerLinkInterceptorList(Arrays.asList(interceptors));
    }

    @Override
    public void onNext(Pocer[] deck, int[] shuffled, int next) {
        for (PocerLinkInterceptor interceptor : interceptors) {
            interceptor.onNext(deck, shuffled, next);
        }
    }

    @Override
    public boolean onInsureNext(HolderPool holderPool, Room room) {
        for (PocerLinkInterceptor interceptor : interceptors) {
            if (!interceptor.onInsureNext(holderPool, room)) {
                return false;
            }
        }
        return true;
    }
}
