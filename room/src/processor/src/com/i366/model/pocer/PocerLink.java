/**
 * $RCSfile: Pocer.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-13  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.model.pocer;

import java.util.*;
import java.util.stream.Collectors;

import com.dzpk.common.utils.LogUtil;
import com.i366.model.room.Room;
import lombok.Getter;
import org.apache.logging.log4j.Logger;

import com.dzpk.insurance.HolderPool;

/**
 * 扑克对象 开始游戏则new一个该对象
 * <p>Title: Pocer</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class PocerLink {
	private static final Logger logger = LogUtil.getLogger(PocerLink.class);

	private static final Pocer[] p_ = new Pocer[52];
	private Random r = new Random();
	/** 52张牌顺序 */
	protected final int[] pocerArray = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
	        13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
	        26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38,
	        39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51};
    /**
     * 当前在第几张牌
     */
    @Getter
    private int index = 0; //当前第几张牌了 从0开始计算
	/**
	 * 1皇家同花 2同花顺 3四条 4葫芦 5同花 6顺子 7三条 8两对 9一对 10高牌
	 */
	public static final int[] POCER_TYPE = {1,2,3,4,5,6,7,8,9,10};
	private int[] zuidapai;
	private int robotIndex = 0;
	
	// 指定牌用户map和公告牌
	private static final String filename = "poker.txt";
	Map<Integer, Pocer[]> playerPokers = new HashMap<Integer, Pocer[]>();
	Pocer[] publicPoker = new Pocer[5];
    Map<Integer, Integer> players = new HashMap<Integer, Integer>();
	
	private int s1 = -1;
	private int s2 = -1;

	@Getter
    private final Set<Integer> intervenenPocerSet = new HashSet<>(); //本手已经干预的牌

    @Getter
    private final PokerControl pokerControl = new PokerControl();

    public interface PocerLinkInterceptor {
		default void onNext(Pocer[] deck, int[] shuffled, int next) {
            // no-op
        }
        default boolean onInsureNext(HolderPool holderPool, Room room) {
            return true;
        }
	}

	private final PocerLinkInterceptor interceptor;

	public PocerLink(PocerLinkInterceptor interceptor) {
		this.interceptor = interceptor;
	}

	public Pocer[] getPublicPoker() {
		return Arrays.copyOf(publicPoker, publicPoker.length);
	}

	public Pocer getPublicPoker(int i) {
		return publicPoker[i];
	}

	public int getPocerArray(int i) {
		return pocerArray[i];
	}

	public Pocer getPocer(int i) {
		return new Pocer(pocerArray[i]);
	}

	public int[] cloneDeck() {
		return pocerArray.clone();
	}

	public void restoreDeck(int[] deck) {
		System.arraycopy(deck, 0, this.pocerArray, 0, this.pocerArray.length);
	}
	
	/**
	 * 1 获取 1-2张牌 3发两张牌是获取正常牌 2获取公共牌 -1是花钱看牌
	 * @param type
	 * @return
	 */
	public Pocer robotNext(int type,int controlIndex) {
		if (type==4){
			return nextControl(controlIndex);
		}
		if (type == -1) {
            return nextShowcards();
        }
		if (type == 0) {
			return next();
		}
		Pocer pocer = null;
		if (type == 1) {
			robotIndex ++;
			if (robotIndex > 2) {
				logger.error("robotNext err!!!!");
			}
			return new Pocer(zuidapai[robotIndex -1]);
		}else if (type == 2) {
			if ((robotIndex == 2 && s1 == -1) || (robotIndex == 3 && s2 == -1) || robotIndex >= 5) {
				s1 = 1;
				if (robotIndex == 3) {
					s2 = 1;
				}
				int w = 0;
				while(w == 0) {
					pocer = next();
					if (zuidapai[0] != pocer.getSize1() &&zuidapai[1] != pocer.getSize1() &&zuidapai[2] != pocer.getSize1() && zuidapai[3] != pocer.getSize1() && zuidapai[4] != pocer.getSize1()) {
						w = 1;
						return pocer;
					}
				}
			}else {
				pocer = new Pocer(zuidapai[robotIndex]);
				robotIndex ++;
				return pocer;
			}
		}else if (type == 3) {
			int w = 0;
			while(w == 0) {
				pocer = next();
				if (zuidapai[0] != pocer.getSize1() &&zuidapai[1] != pocer.getSize1() &&zuidapai[2] != pocer.getSize1() && zuidapai[3] != pocer.getSize1() && zuidapai[4] != pocer.getSize1()) {
					w = 1;
					return pocer;
				}
			}
		}
		return null;
	}

	/**
	 * 依次获取一张牌
	 *
	 * @return
	 */
	private synchronized Pocer next() {
		if (index>51){
			throw new IllegalStateException("index下标异常===========》"+index);
		}
		interceptor.onNext(p_, pocerArray, index);
		Pocer pocer = p_[pocerArray[index]];
		index++;
		logger.trace("当前index下标:{} 牌面值:{}", index, Arrays.toString(pocerArray));
		return pocer;
	}
	/**
	 * 获取的控制牌谱功能
	 *
	 * @return
	 */
	private Pocer nextControl(int pocerIndex) {
		int oldIndex=printArray(pocerArray,pocerIndex);
		int oldEle=pocerArray[index];
		pocerArray[index]=pocerIndex;
		pocerArray[oldIndex]=oldEle;
		return next();
	}

	//遍历数组
	private int printArray(int[] array,int value) {
		for (int i = 0; i < array.length; i++) {
			if (array[i] == value) {
				return i;
			}
		}
		return -1;//当if条件不成立时，默认返回一个负数值-1
	}

		/**
         * 依次获取一张牌
         * 花钻石看底牌任务时，公共牌的下标再加5
         * @return
         */
    private Pocer nextShowcards() {
        Pocer pocer = p_[pocerArray[index + 5]];
        index++;
        return pocer;
    }

    /**
     * Allin状态下获取干预的下一张牌
     * @param isAllInWin 是否allin处于领先状态
     * @param interveneUserId 干预玩家id
     * @param room
     * @return
     */
    public Pocer allinInterveneNextPocer(HolderPool holderPool,boolean isAllInWin, int interveneUserId, Room room){
        logger.debug("allin状态下获取干预的牌型,isAllInWin={},interveneUserId={},rid={}",isAllInWin,interveneUserId,room.getRoomId());
        Pocer pocer = p_[pocerArray[index]];
        index++;


        if (holderPool != null) {  
            Set<Integer> selectedOuts = holderPool.getOuts();
            if(null != selectedOuts){
                for (Integer selectedOut : selectedOuts) {
                    logger.debug("insureNext =========" + selectedOut);
                }
            }

            if(isAllInWin){

                while (index < pocerArray.length){

                    if(!selectedOuts.contains(pocer.getSize1()) && !intervenenPocerSet.contains(pocer.getSize1())){ //处于领先则不发outs中的牌
                         break;
                    }
                    pocer = p_[pocerArray[index++]];
                }
            }else{ //落后时需要再次判断outs中的牌是否发完会领先 因为有3个人的情况 弱势玩家的outs集合都在里边
                while (index <  pocerArray.length){

                    if(selectedOuts.contains(pocer.getSize1()) && !intervenenPocerSet.contains(pocer.getSize1())){ //处于弱势则发outs中的牌

                        //if(totalPlayer == 2){
                            break;
                        //}

//                        if(totalPlayer == 3){   //比较干预玩家和领先玩家的牌,当干预玩家牌力大时才返回
//                            if(AllinInterveneBiPai.bipai(allinWinUserId,interveneUserId,pocer,room)){
//                                break;
//                            }
//                        }
                    }
                    pocer = p_[pocerArray[index++]];
                }
            }
        }
        
        logger.debug("计算出的牌={}",pocer.getName() + pocer.getSize2());
        return pocer;
    }

    /**
     * 普通模式下获取干预的下一张牌
     * @param notOuts
     * @return
     */
    public Pocer normalInterveneNextPocer(Set<Integer> notOuts){
        logger.debug("普通模式下干预下获取干预的牌型");
        Pocer pocer = p_[pocerArray[index]];
        index++;
        if (notOuts != null && !notOuts.isEmpty()) {
            logger.debug("普通模式下干预,outs={}",notOuts.toString());

            while (index < pocerArray.length){

                if(!notOuts.contains(pocer.getSize1()) && !intervenenPocerSet.contains(pocer.getSize1())){ //不发outs中的牌
                    break;
                }
                pocer = p_[pocerArray[index++]];
            }
        }

        logger.debug("计算出的牌={}",pocer.getName() + pocer.getSize2());
        return pocer;
    }

    /**
     * 计算未发的牌
     * @return
     */
    public List<Pocer> getUnSendCards(){
        logger.debug("获取未发的牌,当前的索引值={},已经干预的牌={}",index,intervenenPocerSet.toString());
        List<Pocer> hideCardsList = new ArrayList<>();
        for(int index = getIndex();index < pocerArray.length; index ++){
            Pocer pocer = p_[pocerArray[index]];

            if(!this.intervenenPocerSet.contains(pocer.getSize1())){ //去掉已经干预过的牌
                hideCardsList.add(pocer);
            }
        }

        return hideCardsList;
    }


	/**
	 * 依次获取一张牌(保险爆牌控制 对保险人的保护)
	 *
	 * @param holderPool
     * @param cardControl 是否控牌
	 * @return
	 */
    public Pocer insureNext(HolderPool holderPool, boolean cardControl, List<Integer> sendedCards, Room room) {

        Pocer nextPocer;
        interceptor.onNext(p_, pocerArray, index);
        do{
            nextPocer = p_[pocerArray[index]];
            logger.debug("next pocer:{}-{}", nextPocer.getSize1(), nextPocer);
            index++;
        } while (sendedCards.contains(nextPocer.getSize1())); //发现获取的牌已经发了就继续找下一张
        // 保险爆牌控制
        if (holderPool != null && !cardControl && interceptor.onInsureNext(holderPool, room)) {
            int insureChip = holderPool.getInsureChip();
            double odds = holderPool.getSelectedOdds();
            // 如果爆牌 需要赔多少钱
            int duePay = (int) Math.ceil(insureChip * odds);
            // 底池
            int poolChip = holderPool.getPoolChip();
            Set<Integer> selectedOuts = holderPool.getSelectedOuts();
            logger.debug("poolChip={} pay={} chip={} odds={} selectedOuts={}", poolChip, duePay, insureChip, odds, selectedOuts);
            // 保险的赔付额大于底池的38%和outs中包含这张牌时，触发概率调整逻辑
            if (((float)duePay / poolChip) > 0.38 && selectedOuts != null && selectedOuts.contains(nextPocer.getSize1())) {
                Random random = new Random();
                double ran = random.nextDouble();
                int numOuts = selectedOuts.size();
                logger.debug("pay/poolChip={} outs={} ran={}", (float) duePay / poolChip, numOuts, ran);
                Pocer pocer = nextPocer;
                switch (numOuts) {
                    case 1:
                    case 2:
                        if (ran > 0.2) { // Outs数量为1和2时，80%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 3:
                    case 4:
                        if (ran > 0.3) { // Outs数量为3和4时，70%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 5:
                    case 6:
                        if (ran > 0.5) { // Outs数量为5和6时，50%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 7:
                    case 8:
                        if (ran > 0.6) { // Outs数量为7和8时，40%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 9:
                    case 10:
                        if (ran > 0.7) { // Outs数量为9和10时，30%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 11:
                    case 12:
                        if (ran > 0.8) { // Outs数量为11和12时，20%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 13:
                    case 14:
                        if (ran > 0.9) { // Outs数量为13和14时，10%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    case 15:
                    case 16:
                        if (ran > 0.9) { // Outs数量为15和16时，10%概率要发下一张牌
                            pocer = p_[pocerArray[index++]];
                        }
                        break;
                    default:
                        break;
                }
                if (pocer != nextPocer) {
                    nextPocer = pocer;
                    logger.debug("swapped next pocer:{}-{}", nextPocer.getSize1(), nextPocer);
                }
            }
        }
        return nextPocer;
    }


    static {
		for (int i=0 ; i < p_.length ; i++) {
			p_[i] = new Pocer(i);
		}
	}

	protected void onAfterShuffle() {
		//
	}

	// 新洗牌算法
	public void newShuffle() {
	    s1 = -1;
        s2 = -1;
        robotIndex = 0;
        index = 0;

        randing(0);
        shuffling();
        cutting();
		onAfterShuffle();
        logger.debug("pocerArray{}",Arrays.toString(pocerArray));

	}

	public void shuffleRemaining() {
		randing(index);
	}
	   
	// 合牌：把第0-第51张牌随机跟这52张牌任意一张互换顺序
    private void randing(int fromIndex) {
        for (int i = fromIndex; i < 52; i++) {
            int a = (new Random()).nextInt(52 - i) + i;
            int c = pocerArray[a];
            pocerArray[a] = pocerArray[i];
            pocerArray[i] = c;
        }
    }
	// 洗牌
	private void shuffling() {
	    int[] shufflePokers = new int[52];
	    System.arraycopy(pocerArray, 0, shufflePokers, 0, pocerArray.length);
	    // 洗牌次数3-7次
	    int runTimes = 3 + (new Random()).nextInt(5);
	    while (runTimes > 0) {
	        int s = 0;
	        // 分牌位置 20-32 将牌队分成a,b两堆
	        int pos = 20 + (new Random()).nextInt(13);
            int i = pos - 1;
            int j = pos;
	        while (s < pocerArray.length) {
    	        int times = (new Random()).nextInt(3);
    	        while (times >= 0 && i >= 0) {
    	            shufflePokers[s++] = pocerArray[i--];
    	            times--;
    	        }
    	        times = new Random().nextInt(3);
    	        while (times >= 0 && j < pocerArray.length) {
                    shufflePokers[s++] = pocerArray[j++];
                    times--;
                }
	        }
	        System.arraycopy(shufflePokers, 0, pocerArray, 0, pocerArray.length);
	        runTimes--;
	    }
	}
	
	// 切牌：随机到位置n（1-50），第0张跟第n张互换牌，第1跟第n+1张互换。。。第51-n跟第51张换；第51-n+1张跟第0张换，第51-n+2张跟第1张换。。。第51张跟第n-1张换
	private void cutting() {
		Random rand=new Random(38);
		List<Integer> pocerList = Arrays.stream(pocerArray).boxed().collect(Collectors.toList());
		Collections.shuffle(pocerList, rand);
		for (int i = 0; i < pocerList.size(); i++) {
			pocerArray[i] = Integer.parseInt(pocerList.get(i).toString());
		}
	    int[] cutPokers = new int[52];
        System.arraycopy(pocerArray, 0, cutPokers, 0, pocerArray.length);
	    int pos = 1 + (new Random()).nextInt(50);
	    int s = 0;
	    for (int i = pos; i < cutPokers.length; i++) {
	        pocerArray[s++] = cutPokers[i];
	    }
	    for (int i = 0; i < pos; i++) {
	        pocerArray[s++] = cutPokers[i];
	    }
	}
	
	public boolean checkExist(int s) {
	    for (int i = 0; i < playerPokers.size(); i++) {
	        if (playerPokers.get(i) != null && (playerPokers.get(i)[0].getSize1() == s
	                || playerPokers.get(i)[1].getSize1() == s)) {
	            return true;
	        }
	    }
	    for (int i = 0; i < publicPoker.length; i++) {
	        if (publicPoker[i].getSize1() == s) {
	            return true;
	        }
	    }
	    return false;
	}
	
	public Pocer getNext(int userId, int index) {
	    if (playerPokers.get(userId) != null && playerPokers.get(userId)[index] != null) {
	        return playerPokers.get(userId)[index];
	    }
	    Random random = new Random();
	    
	    Pocer[] pocers = new Pocer[2];
        int pres = -1;
	    for (int i = 0; i < 2; i++) {
	        int s = 0;
    	    do {
        	    s = random.nextInt(52);    
    	    } while(s != pres && checkExist(s));
    	    pocers[i] = new Pocer(s);
    	    pres = s;
	    }
	    playerPokers.put(userId, pocers);
	    return playerPokers.get(userId)[index];
	}
	
	public Pocer getPublic(int index) {
	    return publicPoker[index];
	}
}


