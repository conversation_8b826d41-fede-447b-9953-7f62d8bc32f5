
package com.i366.room;

import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.AiRuleTemplate;
import com.ai.dz.config.constant.EAiType;
import com.ai.dz.config.constant.EBringinType;
import com.ai.dz.room.model.player.AiPlayer;
import com.ai.dz.room.util.AiAddChips;
import com.ai.dz.room.util.AiDispatch;
import com.ai.dz.room.util.AiHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.agent.Muck;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.IUserBalanceAuditDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.impl.UserBalanceAuditDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubSuperior;
import com.dzpk.commission.repositories.mysql.model.UserBalanceAuditLog;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.mq.rabbitmq.constant.EMessageCode;
import com.dzpk.component.mq.rabbitmq.model.record.PlayerPl;
import com.dzpk.component.repositories.mongo.MongodbService;
import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.component.repositories.redis.SkyRedWalletRedisService;
import com.dzpk.component.repositories.redis.lock.*;
import com.dzpk.db.dao.*;
import com.dzpk.db.imp.*;
import com.dzpk.db.model.*;
import com.dzpk.dealer.Player;
import com.dzpk.gameevent.GameEventService;
import com.dzpk.gameevent.impl.GameEventServiceImpl;
import com.dzpk.insurance.PoolChip;
import com.dzpk.insurance.PoolInfo;
import com.dzpk.jackpot.IJackpotService;
import com.dzpk.record.AnteAction;
import com.dzpk.record.LogManage;
import com.dzpk.record.RoomReplay;
import com.dzpk.work.Task;
import com.dzpk.work.TaskConstant;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.*;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.player.StandUpUserInfo;
import com.i366.model.pocer.JPPool;
import com.i366.model.pocer.Pocer;
import com.i366.model.pocer.WinPoolChip;
import com.i366.model.room.LeveRoom;
import com.i366.model.room.Room;
import com.i366.util.*;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.Request;
import com.work.comm.message.BusinessMessageSender;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;
import org.bson.Document;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 房间操作service
 */
public class RoomService {
    private static final Logger logger = LogUtil.getLogger(RoomService.class);
    public static final String sp = "@%";

    // 将发过的牌保存到列表，做二次校验，避免发一样的牌
    public List<Integer> sendedCards = new ArrayList<Integer>();

    Room room;

    private RoomService(Room room) {
        this.room = room;
    }

    private static final GameHistoryDao historyDao = new GamehistoryImp();

    private static final RoomDao roomDao = new RoomDaoImpl();

    @Getter
    private static final GameEventService gameEventService = new GameEventServiceImpl();

    public static synchronized RoomService initRoomService(Room room) {
        return new RoomService(room);
    }

    public boolean isClubLossLimitExceeded(int clubId) {
        // only applicable to club rooms
        if (room.getClubRoomType() != 1) {
            return false;
        }
        // total supply + unsettled < total_amount - loss_limit
        ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
        return clubTribeDao.isClubLossLimitExceeded(clubId);
    }

    /**
     * 玩家站起
     *
     * @param rp          玩家信息
     * @param standupType 站起类型
     */
    public void zhanqi2(RoomPersion rp, StandUpRoomCode standupType) {
        logger.debug("stand up room,userid={},roomid={},persion type={}," +
        "leaveType={},seat={}", rp.getUserId(), room.getRoomId(), rp.getType(), standupType.toString(), rp.getSize());

        RoomAutoOp.setUserAutoOp(room, rp, false);//只要站起了，就取消托管，无论是主动还是被动。避免托管状态下，站起继续坐下还是托管的情况

        logger.debug("zhanqi2 roomId:" + room.getRoomId() + " userId=" + rp.getUserId() + " getRequestChip=" + rp.getRequestChip());
        if (rp.getRequestChip() > 0) {//避免站起的时候，还保留这下一手牌才刷新筹码，站起就添加
            RoomRequest.bindUserChip(rp, rp.getRequestChip(), room);
            rp.setRequestChip(0);
        }

        if (rp.getType() == 3) {    // 游戏中站起
            rp.setStandupType(0);   // 主动站起
            if (room.getCurrentNumber() != -1 && room.getCurrentNumber() == rp.getSize()) {     // 当前操作玩家站起
                rp.setStatus(-4);        // 玩牌站起
                room.roomProcedure.runByRoomStatus();
                return;
            } else {   // 非当前玩家
                trimUserInfo(rp);
                StandUpUserInfo standUpUserInfo = new StandUpUserInfo(rp.getBetChouma(), 0, rp.getUserInfo().getClubId()); // 纪录游戏中站起玩家已经下注筹码
                room.setStandUpUserInfo(rp.getUserId(), standUpUserInfo);
                if (rp.getStatus() != -3) {
                    rp.setStatus(-3);
                    setClientAction(rp, 6, 0);     // 记录用户弃牌操作
                }
            }
        } else if (rp.getType() == 5) {
            StandUpUserInfo standUpUserInfo = new StandUpUserInfo(rp.getBetChouma(), 0, rp.getUserInfo().getClubId()); // 纪录游戏中站起玩家已经下注筹码
            room.setStandUpUserInfo(rp.getUserId(), standUpUserInfo);
        } else {
            rp.setStandupType(1);   // 默认设置被动站起
        }

        rp.setType(1);
        clearSeat(rp.getUserId(), rp.getSize(), 1); //清除玩家座位信息
        room.getRoomPlayers().get(rp.getUserId()).setRequestToBringInTimes(0);
        room.getDealer().addWatcher(rp.getUserId());//站起后变成观察者
        room.getDealer().setLeftChip(rp.getUserId(), rp.getNowcounma());

        giveUpInsurance(rp); // 投保中站起等于弃保

        Object[][] objs = { //  通知别的玩家有人起来了
                {60, rp.getSize(), I366ClientPickUtil.TYPE_INT_1},
                {130, rp.getUserId(), I366ClientPickUtil.TYPE_INT_4}};
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_LEAVE);
        PublisherUtil.send(room, bytes, rp.getUserId());

        int action = 2;  //1坐下、2站起、3退出、4强制站起、5强制踢出 8冻结
        if (rp.isKickOut()) {
            rp.setKickOut(false);
            action = 5;

            if (rp.getStandupType() == 2) {
                action = 8;
            }
        }

        if (rp.isStandUp()) {
            rp.setStandUp(false);
            action = 4;
        }

        Object[][] objs2 = { // 通知本人站起成功
                {60, 0, I366ClientPickUtil.TYPE_INT_1},
                {61, action, I366ClientPickUtil.TYPE_INT_1},
                {130, rp.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                {131, rp.getChouma(), I366ClientPickUtil.TYPE_INT_4}
        };
        bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
        PublisherUtil.publisher(rp.getUserInfo(), bytes);

        if (room.getMinPlayTime() > 0) {
            long leftTime = RoomAutoOp.needAutoOp(room, rp.getUserId());
            if (leftTime > 0) { // 还未完成最短打牌时间，重算最短打牌时间
                logger.debug("leftTime: " + leftTime);
                RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
                roomPlayer.setPlayTime(roomPlayer.getPlayTime()
                        + System.currentTimeMillis() / 1000 - roomPlayer.getDownTime());
                roomPlayer.setDownTime(0);
            }

            RedisService.getRedisService().delAutoOp(rp.getUserId(), room.getRoomId());
        }

        RoomPlayer zhanqiRoomPlayer = room.getRoomPlayers().get(rp.getUserId()); //将状态设置为站起
        if (zhanqiRoomPlayer != null) {
            zhanqiRoomPlayer.setSeatStatus(0);
            zhanqiRoomPlayer.setSeat(-1);
            zhanqiRoomPlayer.setSeatSize(-1);
        }
        rp.setSize(-1);
        rp.setNextGameStandup(0); //防止下一局開始自動站起

        int handTotal = room.getDealer().getHandCnt(rp.getUserId()); //玩家已经打的手数
        room.getRoomSeatChangeService().checkSeatChangeTpye(room.getRoomId(), rp.getUserId(), room.getStage(), handTotal, RoomSeatChangeCode.STANDUP_ROOM);

        checkRoomPlayingNum();

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);

    }

    /**
     * 站起并且占座,玩家输完积分时
     *
     * @param rp
     */
    public void standAndOccupySeat(RoomPersion rp) {
        logger.debug("userId: " + rp.getUserId() + ", person type: " + rp.getType());

        rp.setStandupType(1); // 设为主动站起
        rp.setCanPlay(false);  //设置为下一局不可以玩
        rp.setType(1); // 非游戏中站起
        rp.setPlayType(0); //站起时要设置为过庄
        rp.setCanPlay(false);
        rp.setGameType(1);
        clearSeat(rp.getUserId(), rp.getSize(), 2); //清除玩家座位信息

        room.getDealer().setLeftChip(rp.getUserId(), rp.getNowcounma());
        giveUpInsurance(rp);  // 投保中站起等于弃保

        RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
        if (null != roomPlayer) { //自动进入离桌留座状态
            roomPlayer.setNickName(rp.getUserInfo().getNikeName()); // 占座玩家的名字
            roomPlayer.setSeatSize(rp.getSize());                   // 被占座位编号
            roomPlayer.setHeader(rp.getUserInfo().getHead());       // 占座玩家的头像
            roomPlayer.setUseCustom(rp.getUserInfo().getUseCustom());       // 占座玩家是否使用自定义头像
            roomPlayer.setCustomUrl(rp.getUserInfo().getCustomUrl());       // 占座玩家的自定义头像
            roomPlayer.setNowChipWhenOccupySeat(rp.getNowcounma()); // 占座时玩家剩余的桌上筹码
            roomPlayer.setSex(rp.getUserInfo().getSex());
            roomPlayer.setChouma(rp.getChouma());

            if (0 == newOccupySeatTask(rp.getUserId(), Constant.MAX_OCCUPY_SEAT_TIME)) {
                // 通知别的玩家有人占座了
                Object[][] objs2 = {
                    {60, 0, I366ClientPickUtil.TYPE_INT_1},// 0成功 1 失败 2未到最短时间 3下一手生效
                    {130, rp.getUserId(), I366ClientPickUtil.TYPE_INT_4},   // 玩家id
                    {131, 3, I366ClientPickUtil.TYPE_INT_1},                // 操作类型
                    {132, rp.getSize(), I366ClientPickUtil.TYPE_INT_4},     // 座位号
                    {133, (int) (Constant.MAX_OCCUPY_SEAT_TIME / 1000), I366ClientPickUtil.TYPE_INT_4}, // 剩余倒计时
                    {134, "", I366ClientPickUtil.TYPE_STRING_UTF16}         // 房间名字
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_OCCUPY_SEAT);
                PublisherUtil.send(room, bytes);
            }
        }

        if (room.getMinPlayTime() > 0) {

            // 记录打牌时间
            long leftTime = RoomAutoOp.needAutoOp(room, rp.getUserId());
            if (leftTime > 0) { // 还未完成最短打牌时间，重算最短打牌时间
                logger.debug("leftTime: " + leftTime);
                roomPlayer.setPlayTime(roomPlayer.getPlayTime()
                        + System.currentTimeMillis() / 1000 - roomPlayer.getDownTime());
                roomPlayer.setDownTime(0);
            }

            // 删除正在留盲代打房间信息
            RedisService.getRedisService().delAutoOp(rp.getUserId(), room.getRoomId());
        }

        checkRoomPlayingNum();

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);
    }

    /**
     * 将用户从座位上清除
     *
     * @param userId    玩家id
     * @param seatId    座位号
     * @param clearType 清除类型 1玩家主动站起 2玩家输完留座离桌
     */
    private void clearSeat(int userId, int seatId, int clearType) {
        logger.debug("clearSeat,roomid={},userid={},seatid={},clearType={}", room.getRoomId(), userId, seatId, clearType);
        if (seatId >= 0 && seatId < room.getRoomPersions().length) {
            if (room.getRoomPersions()[seatId] != null && room.getRoomPersions()[seatId].getOnlinerType() != -1) {
                room.getRoomPersions()[seatId] = null;
            }
            room.getDdRoomPersions()[seatId] = null;

        }
        if (seatId >= 0) {
            room.getDdRoomPersions()[seatId] = null;
        }

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);
    }

    /**
     * 玩家站起
     *
     * @param rp          玩家信息
     * @param standupType 站起类型
     */
    public synchronized void zhanqi(RoomPersion rp, StandUpRoomCode standupType) {
        zhanqi2(rp, standupType);
    }


    /**
     * 玩家进入房间
     * 在线列表中有该用户则 直接T掉、并且已经在玩的做弃牌处理
     * 往在线列表中添加该用户
     *
     * @param userInfo
     * @return
     */
    public synchronized void accessRoom(UserInfo userInfo) {
        //座位号
        int seat = -1;
        int userId = userInfo.getUserId();

        RoomPersion rp = room.getAudMap().get(userId);
        if (rp == null) { // 正常退出房间后再进来
            logger.debug("[R-{}][U-{}]accessRoom,user is not in room now",room.getRoomId(), userId);
            accessClubRoom2(userInfo);
            rp = room.getAudMap().get(userId);
        } else { // 留盲代打时进这里
            logger.debug("[R-{}][U-{}]accessRoom,user is in room now",room.getRoomId(), userId);
            if (rp.getSize() >= 0 && rp.getSize() < room.getPlayerCount()) {
                if (room.getRoomPersions()[rp.getSize()] != null
                        && room.getRoomPersions()[rp.getSize()].getUserId() == userId
                        && room.getRoomPersions()[rp.getSize()].getOnlinerType() != -1) {
                    seat = rp.getSize();
                } else if (room.getDdRoomPersions()[rp.getSize()] != null
                        && room.getDdRoomPersions()[rp.getSize()].getUserId() == userId) {
                    seat = rp.getSize();
                }
                logger.debug("[R-{}][U-{}]seat: {}", room.getRoomId(), userId, seat);
            }
        }
        inspectPrivilege(room, rp);
        Cache.setOnlineUserInfo(userInfo.getUserId(), room.getRoomId(), userInfo); //用户每次进入都更新全局，保存到onlineHashMap中
        rp.setUserInfo(userInfo);    //更新userInfo
        rp.setChouma(userInfo.getChip()); // 更新用户房间外筹码
        rp.setOnlinerType(1);

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        if (null != roomPlayer) {
            roomPlayer.setHasLeft(false);
            if (room.isTribeRoom() && roomPlayer.getSelectClubId() != 0) {
                logger.debug("[R-{}][U-{}]聯盟房二次進入, 玩家帶入的俱樂部: selectClubId={}", room.getRoomId(),userId,roomPlayer.getSelectClubId());
                userInfo.setClubId(roomPlayer.getSelectClubId());
            }
        } else {
            roomPlayer = new RoomPlayer(userId);
            room.getRoomPlayers().put(userId, roomPlayer);
        }

        logger.debug("[R-{}][U-{}]accessRoom,get room data detail,seat={},chip={},now chip={}",room.getRoomId(), userId, seat, userInfo.getChip(), rp.getNowcounma());
        byte[] bytes = getRoomData2(userId, seat, userInfo.getChip(), rp.getNowcounma()); //获取进房详细信息
        PublisherUtil.publisher(userInfo, bytes);

        if (room.getInsuranceActive()) { //如果开了保险模式 并且保险处于购买中状态 需要额外下发信息
            room.getInsurer().sendEnterRoomMsg(room, userId);
        }
        for (Entry<Integer, RoomPlayer> playerEntry : room.getOccupySeatPlayers().entrySet()) {
            if (playerEntry != null) {
                long times = playerEntry.getValue().getRequestToBringInTimes();
                Integer playerUserId = playerEntry.getKey();
                if (times != 0 && times > System.currentTimeMillis()) {
//                    logger.info("156==>RoomPersions");
                    Object[][] objs2 = {
                        {60, 0, I366ClientPickUtil.TYPE_INT_1},// 0成功 1 失败 2未到最短时间 3下一手生效
                        {130, playerUserId, I366ClientPickUtil.TYPE_INT_4},   // 玩家id
                        {132, playerEntry.getValue().getSeatSize(), I366ClientPickUtil.TYPE_INT_4},     // 座位号
                        {133, (int) ((times - System.currentTimeMillis()) / 1000), I366ClientPickUtil.TYPE_INT_4}, // 剩余倒计时
                    };
                    PublisherUtil.send(room, I366ClientPickUtil.packAll(objs2, Constant.REQ_WAITE_SEAT));
                }
            }
        }
        room.removeLeaveUser(userId);  //进来后将该用户从离开房间的玩家集合中删除

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);

        RabbitMqUtil.joinChatGroupRoom(room.getRoomId(), userId);
        gameEventService.emitEnterRoom(room, userId);
    }

    private void inspectPrivilege(Room room, RoomPersion persion) {
        UserInfoDao userInfoDao = new UserInfoDaoImp();
        try {
            Integer userPrivilege = userInfoDao.getUserPrivilege(persion.getUserId());
            if (userPrivilege != null) {
                persion.getUserInfo().setPrivilege(true);
                if (room.getRoomStatus() > 1 && room.getRoomStatus() < 8) {
                    showCardsAndPrivilegeUser(userPrivilege);
                }
            } else {
                persion.getUserInfo().setPrivilege(false);
            }
            logger.debug("特权玩家加入成功======" + userPrivilege);
        } catch (SQLException sqlException) {
            logger.debug("特权玩家加入失败======" + persion.getUserId(), sqlException);
        }
    }

    public void accessClubRoom2(UserInfo userInfo) {
        userInfo.setOnlineTime(System.currentTimeMillis());
        RoomPersion roomPersion = new RoomPersion();
        roomPersion.setUserId(userInfo.getUserId());
        roomPersion.setChouma(userInfo.getChip());
        roomPersion.setUserInfo(userInfo);
        roomPersion.setNowcounma(room.getDealer().getUserLeftChip(userInfo.getUserId()));
        RoomPlayer roomPlayer = room.getRoomPlayers().get(userInfo.getUserId());
        logger.debug("person nowChouma=" + roomPersion.getNowcounma());
        Object[] userStatus = room.getDealer().getUserStatus(userInfo.getUserId());
        if (userStatus != null) {
            roomPersion.setStandupType((Integer) userStatus[0]);    // 离开类型 0主动 1被动
            roomPersion.setPlayType((Integer) userStatus[1]);       // 参与方式 0过庄 1补盲 2正常
        }
        room.getAudMap().put(userInfo.getUserId(), roomPersion);

        if (null == roomPlayer || (null != roomPlayer && 3 != roomPlayer.getSeatStatus())) {
            //超级名单,实时战况不可见
            if (!AiRoomManager.checkIfRoomViewerWhitelist(userInfo.getUserId(), room.getRoomId())) {
                // 进入房间的玩家若非离桌留座状态，则记录为观察者
                room.getDealer().addWatcher(userInfo.getUserId());
            }

        }
        logger.debug("...enter room user id is: " + userInfo.getUserId() + " time is:" + System.currentTimeMillis());

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);
    }

    //通知开始倒计时
    public synchronized void beginCountdown() {
        try {
            Thread.sleep(500); //停顿500ms
        } catch (InterruptedException e) {
            // suppressed
        }

        for (RoomPersion rp : room.getRoomPersions()) {
            if (rp != null) {
                rp.setType(4);
            }
        }
        logger.debug("beginCountdown,room id :" + room.getRoomId());
        int time = (new Long(Constant.STATUS_2_DC / 1000)).intValue();
        Object[][] objs = {
                {60, time, I366ClientPickUtil.TYPE_INT_1}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_READYTIME);
        PublisherUtil.send(room, bytes);
    }

    /**
     * 跟注
     *
     * @param roomPersion
     * @param anteNumber
     */
    public synchronized void genzhu(RoomPersion roomPersion, int anteNumber) {
        if (roomPersion.getBetStatus() == 0) {  // 防止客户端并发发送玩家操作
            room.addActionCount();
            room.addGenZhuCount();
            roomPersion.setNowcounma(roomPersion.getNowcounma() - anteNumber);
            roomPersion.setBetChouma((roomPersion.getBetChouma() < 0 ? 0 : roomPersion.getBetChouma()) + anteNumber);
            roomPersion.setStatus(2);
            roomPersion.setAnteCount(roomPersion.getAnteCount() + anteNumber);

            if (roomPersion.getBetChouma() - room.getMaxChouma() >= room.getMinRaiseNumber()) { // 更新最小加注金额和对应玩家 （这里加这段主要是防止客户端加注当作跟注发过来）
                room.setMinRaiseNumber(roomPersion.getBetChouma() - room.getMaxChouma());
                room.setRaisePerson(roomPersion.getSize());
            }

            int publicCardNum = RoomUtil.getPublicCardNum(room);
            if (publicCardNum >= 3) {
                room.getDealer().addAfgenzhuCnt(roomPersion);
            }

            room.getDealer().addBCBetCnt(roomPersion, anteNumber);
            room.setMaxChouma(Math.max(roomPersion.getBetChouma(), room.getMaxChouma()));
            //翻牌前
            //开启straddle的牌局跟注后牌局内最大筹码仍是4SB,即为跟到了straddle
            //不开启straddle的牌局跟注后牌局内最大筹码仍是2SB，即为跟到了BB
            if (room.getRoomStatus() == 3) {
                if (!room.isAfBeforeFanpai()) {
                    room.setAfBeforeFanpai(true);
                }
                //logger.debug("isJiazhuPreFlop: " + room.isJiazhuPreFlop() + " getStraddle=" + room.getStraddle() + " getMaxChouma=" + room.getMaxChouma() + " qianzhu: " + room.getQianzhu());
                logger.debug("[R-{}][U-{}: {}]isJiazhuPreFlop: {} getStraddle={} getMaxChouma={} qianzhu: {}" ,
                        room.getRoomId(),roomPersion.getUserId(),roomPersion.getUserInfo().getNikeName(),
                        room.isJiazhuPreFlop(),room.getStraddle(),room.getMaxChouma(),room.getQianzhu());

                if (!room.isJiazhuPreFlop()) {
                    if (room.getStraddle()) {
                        if ((room.getMaxChouma() - room.getQianzhu()) == 2 * room.getDamanzhu()) {
                            room.setCallToBBorStraddleCount(room.getCallToBBorStraddleCount() + 1);
                        }
                    } else {
                        if ((room.getMaxChouma() - room.getQianzhu()) == room.getDamanzhu()) {
                            room.setCallToBBorStraddleCount(room.getCallToBBorStraddleCount() + 1);
                        }
                    }
                } else { // 增加bet/raise/call次数(补齐BB/stradle的不算)
                    room.setCallOrRaiseCount(room.getCallOrRaiseCount() + 1);
                }
            } else { // 增加bet/raise/call次数(补齐BB/stradle的不算)
                room.setCallOrRaiseCount(room.getCallOrRaiseCount() + 1);
            }

            roomPersion.setBetStatus(1);
            room.setPoolChip(room.getPoolChip() + anteNumber);    // 增加底池
            setClientAction(roomPersion, 2, anteNumber);
            room.getDealer().collect(roomPersion, anteNumber);
        }
    }

    /**
     * 加注
     *
     * @param roomPersion
     * @param anteNumber
     */
    public synchronized void jiazhu(RoomPersion roomPersion, int anteNumber) {
        if (roomPersion.getBetStatus() == 0) {  // 防止客户端并发发送玩家操作
//            room.addJiazhuCount();
            room.addActionCount();
            roomPersion.setNowcounma(roomPersion.getNowcounma() - anteNumber);
            roomPersion.setBetChouma((roomPersion.getBetChouma() < 0 ? 0 : roomPersion.getBetChouma()) + anteNumber);
            roomPersion.setStatus(2);
            roomPersion.setAnteCount(roomPersion.getAnteCount() + anteNumber);

//            if (roomPersion.getBetChouma() - room.getMaxChouma() >= room.getMinRaiseNumber()) {  // 更新最小加注金额和对应玩家
//                room.setMinRaiseNumber(roomPersion.getBetChouma() - room.getMaxChouma());
//                room.setRaisePerson(roomPersion.getSize());
//            }

//            setAttacker(roomPersion,anteNumber); // 设置攻击者

            int publicCardNum = RoomUtil.getPublicCardNum(room);

            /**
             * 进入翻牌后第一次加注视为下注操作
             */
            int firstFlopBet = room.getFirstFlopBet();
            if (publicCardNum >= 3) {

                if (roomPersion.getBetChouma() - room.getMaxChouma() >= room.getMinRaiseNumber()) {
                    firstFlopBet++;
                    room.setFirstFlopBet(firstFlopBet);
                }

                if (firstFlopBet == 1) {
                    room.getDealer().addAfxiazhuCnt(roomPersion);
                } else {
                    room.getDealer().addAfjiazhuCnt(roomPersion);
                }

                room.setAddedTimeAfterFanpai(room.getAddedTimeAfterFanpai() + 1);

            }

            if (room.getRoomStatus() == 3) {

//                logger.debug("isAfBeforeFanpai: " + room.isAfBeforeFanpai() + " isJiazhuPreFlop: " + room.isJiazhuPreFlop());
                logger.debug("[R-{}][U-{}: {}]isAfBeforeFanpai: {} isJiazhuPreFlop={}" ,
                        room.getRoomId(),roomPersion.getUserId(),roomPersion.getUserInfo().getNikeName(),
                        room.isAfBeforeFanpai(),room.isJiazhuPreFlop());
                if (!room.isAfBeforeFanpai()) {
                    room.setAfBeforeFanpai(true);
                }

                if (!room.isJiazhuPreFlop()) {
                    room.setJiazhuPreFlop(true);
                }
            }

            room.setCallOrRaiseCount(room.getCallOrRaiseCount() + 1); //增加bet/raise/call次数
            room.getDealer().addBCBetCnt(roomPersion, anteNumber); // 累加可cbet和cbet次数
            room.getDealer().addCBetCnt(roomPersion);

            room.setMaxChouma(roomPersion.getBetChouma());
            roomPersion.setBetStatus(1);
            room.setPoolChip(room.getPoolChip() + anteNumber);    // 增加底池
            setClientAction(roomPersion, 3, anteNumber);
            room.getDealer().collect(roomPersion, anteNumber);

        }
    }

    /**
     * 全下
     *
     * @param roomPersion
     * @param anteNumber
     */
    public synchronized void quanxia(RoomPersion roomPersion, int anteNumber) {
        if (roomPersion.getBetStatus() == 0) {  // 防止客户端并发发送玩家操作
            room.addActionCount();
            roomPersion.setNowcounma(0);
            roomPersion.setBetChouma((roomPersion.getBetChouma() < 0 ? 0 : roomPersion.getBetChouma()) + anteNumber);
            roomPersion.setStatus(3);

            roomPersion.setAnteCount(roomPersion.getAnteCount() + anteNumber);

            if (roomPersion.getBetChouma() - room.getMaxChouma() >= room.getMinRaiseNumber()) { // 更新最小加注金额和对应玩家
                room.setMinRaiseNumber(roomPersion.getBetChouma() - room.getMaxChouma());
                room.setRaisePerson(roomPersion.getSize());
            }


            if (room.getMaxAllinChouma() < anteNumber) { //更新allin最大下注筹码
                room.setMaxAllinChouma(anteNumber);
            }
//            setAttacker(roomPersion,anteNumber); // 设置攻击者
            room.getDealer().addAllinCnt(roomPersion);

            int publicCardNum = RoomUtil.getPublicCardNum(room);
            if (publicCardNum >= 3) {  //增加翻牌后加注次数
                room.setAddedTimeAfterFanpai(room.getAddedTimeAfterFanpai() + 1);
            }

            if (room.getRoomStatus() == 3 && !room.isAfBeforeFanpai()) {
                room.setAfBeforeFanpai(true);
            }

            room.setAllinCount(room.getAllinCount() + 1);
            room.getDealer().addBCBetCnt(roomPersion, anteNumber);
            room.getDealer().addCBetCnt(roomPersion);

            room.setMaxChouma(Math.max(roomPersion.getBetChouma(), room.getMaxChouma()));
            roomPersion.setBetStatus(1);
            room.setPoolChip(room.getPoolChip() + anteNumber);  // 增加底池
            setClientAction(roomPersion, 4, anteNumber);
            room.getDealer().collect(roomPersion, anteNumber);
        }
    }

    /**
     * 让牌
     *
     * @param roomPersion
     */
    public synchronized void rangpai(RoomPersion roomPersion) {
        if (roomPersion.getBetStatus() == 0) {  // 防止客户端并发发送玩家操作

            room.addActionCount();
            roomPersion.setStatus(1);
            roomPersion.setBetStatus(2);
            setClientAction(roomPersion, 5, 0);
            room.getDealer().addBCBetCnt(roomPersion, 0);
        }
    }

    /**
     * 弃牌
     *
     * @param roomPersion
     */
    public synchronized void qipai(RoomPersion roomPersion) {
        if (roomPersion.getBetStatus() == 0) {  // 防止客户端并发发送玩家操作
            room.addActionCount();
            room.addQiPaiCount();
            roomPersion.setStatus(-3);
            roomPersion.setBetStatus(2);
            setClientAction(roomPersion, 6, 0);
            room.getDealer().collect(roomPersion, 0);
            room.getDealer().addBCBetCnt(roomPersion, 0);
        }
    }

    /**
     * 客户端操作
     *
     * @param roomPersion  操作用户
     * @param clientStatus 1:下注 2:跟注 3:加注 4:全下 5:让牌 6:弃牌 7托管
     * @param anteChips    操作筹码
     */
    public synchronized void setClientAction(RoomPersion roomPersion, int clientStatus, int anteChips) {
        roomPersion.setAnteNumber(anteChips);
        roomPersion.setClientStatus(clientStatus);
        if (clientStatus >= 2 && clientStatus <= 6) {
            AnteAction anteAction = room.getRoomReplay().getAnteAction();
            if (anteAction == null) {
                anteAction = new AnteAction();
            }
            Map<String, Object> actionMap = new HashMap<String, Object>();
            String action = "";
            if (clientStatus == 2) {
                action = "call " + anteChips;
            } else if (clientStatus == 3) {
                action = "raise " + anteChips;
            } else if (clientStatus == 4) {
                action = "allin " + anteChips;
            } else if (clientStatus == 5) {
                action = "check 0";
            } else if (clientStatus == 6) {
                action = "fold 0";
            }
            actionMap.put("ACTION", action);
            actionMap.put("NUMBER", roomPersion.getSize());
            if (roomPersion.isAutoOp()) {
                actionMap.put("AUTOOP", 1);
            }
            anteAction.getActions().add(actionMap);
            if (room.getRoomReplay().getAnteAction() == null) {
                room.getRoomReplay().setAnteAction(anteAction);
            }
        }
    }

    /**
     * 客户端
     *
     * @param roomPersion
     */
    public synchronized void overtime(RoomPersion roomPersion) {
        if (roomPersion.getBetStatus() == 0) {  // 防止客户端并发发送玩家操作
            roomPersion.setBetStatus(2);
            // roomPersion.setAnteNumber(0);
            // 弃牌
            if (roomPersion.getBetChouma() < room.getMaxChouma()) {    // 筹码不够，弃牌
                // roomPersion.setClientStatus(6);
                setClientAction(roomPersion, 6, 0);
                roomPersion.setStatus(-3);
                roomPersion.addTimeoutFoldOpTimes(1);
            } else if (roomPersion.getTimeoutCheckOpTimes() >= Constant.MAX_TIMEOUT_CHECK_TIME) { // 已经连续过牌四次，弃牌
                // roomPersion.setClientStatus(6);
                setClientAction(roomPersion, 6, 0);
                roomPersion.setStatus(-3);
                roomPersion.addTimeoutCheckOpTimes(1);
            } else {    // 过牌
                // roomPersion.setClientStatus(5);
                setClientAction(roomPersion, 5, 0);
                roomPersion.setStatus(1);
                roomPersion.addTimeoutCheckOpTimes(1);
            }
            if (roomPersion.getBetChouma() < room.getMaxChouma() || roomPersion.getTimeoutCheckOpTimes()
            >= Constant.MAX_TIMEOUT_CHECK_TIME) {
                if (roomPersion.getNextGameStandup() == 2) {
                    roomPersion.setNextGameStandup(0);
                    likai(roomPersion, LeaveRoomCode.USER_SELF_LEAVE);
                    Object[][] objs = {  // 通知本人离开成功
                        {60, 0, I366ClientPickUtil.TYPE_INT_1},
                        {61, 3, I366ClientPickUtil.TYPE_INT_1},
                        {130, roomPersion.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                        {131, roomPersion.getChouma(), I366ClientPickUtil.TYPE_INT_4}
                    };
                    byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
                    PublisherUtil.publisher(roomPersion.getUserInfo(), bytes);
                }
            }
        }
    }

    /**
     * 关闭牌局相关逻辑
     * 更新房间状态
     * 返回金豆
     * 推送mq消息
     * 清除redis key
     * 释放派遣at
     * 销毁房间对象
     * 删除对应的mongoDB的牌谱数据
     */
    public synchronized void closeRoom2() {

        RoomUtil.closeRoom(room);
        bringBackChip();
        clearRedisKey();
        releaseAt(room.getRoomId());
        destroyRoom();
        RabbitMqUtil.closeChatGroupRoom(room.getRoomId(), String.valueOf(room.getRoomPath()), room.getName());// 删除聊天房间群组
        RedisService.getRedisService().removeRoomSet(room.getRoomId());
        // 刪除全部RoomPlayerList
        logger.debug("RemoveRoomPlayerList,RoomId={},RoomPath={}", room.getRoomId(), room.getRoomPath());
        RedisService.getRedisService().delRoomPlayerList(room.getRoomId(), room.getRoomPath());

        //historyDao.deleteGameProcessAndCard(room.getRoomId());
    }

    /**
     * 返回金豆
     */
    private void bringBackChip() {
        Map<Integer, Player> players = room.getDealer().getPlayers();
        if (players.size() > 0) {
            List<PlayerPl> playerPlList = new ArrayList<>();
            Iterator<Integer> iterator = players.keySet().iterator();
            while (iterator.hasNext()) {
                int userId = iterator.next();

                Player player = players.get(userId);
                if (player != null && player.getBringIn() > 0) {

                    RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
                    //TODO 联盟扣费2
                    if (room.getClubRoomType() == 0 && null != roomPlayer &&
                    !roomPlayer.isSettlement()) { //提前离座的玩家不需要再次结算
                        updateUserInfo(userId, ChipsCode.ROOM_TIMES_UP);  //更新玩家信息
                    }
                    playerPlList.add(getPlayerPl(player, userId));
                }
            }

            pushMqMessage(playerPlList);  //推送mq消息
        }
    }


    /**
     * 获取分享赚金豆相关信息
     *
     * @param player
     * @param userId
     */
    private PlayerPl getPlayerPl(Player player, int userId) {
        PlayerPl playerPl = new PlayerPl();
        playerPl.setPl(player.getEarn() + player.getInsurance());
        playerPl.setUserId(userId);
        playerPl.setPlFee(player.getPlFee());
        if (room.isJackPot()) {
            playerPl.setJpReward(room.getJackpotService().getTotalJpReward(userId));
            playerPl.setJpRewardFee(room.getJackpotService().getTotalJpRewardFee(userId));
            int totalJpBet = room.getJackpotService().getTotalJpBet(userId); //应投jp总额
            int totalRealJpBet = room.getJackpotService().getTotalRealJpBet(userId); //实际jp总额
            playerPl.setJpCount(totalRealJpBet);
            playerPl.setJpbCount(totalJpBet - totalRealJpBet * 2); //差值即为投入jpb仓
        } else {
            playerPl.setJpReward(0);
        }

        playerPl.setTribeId(player.getTribeId());
        playerPl.setClubId(player.getClubId());

        playerPl.setHandCount(player.getHandCnt());
        playerPl.setTakeInCount(player.getBringInTimes());
        playerPl.setTotalInsurance(player.getTotalInsurance());
        playerPl.setBuyInsuranceCount(player.getInsurance() - player.getTotalInsurance());

        return playerPl;
    }

    /**
     * 清除redis相关key
     * 玩家维度
     * 带入过的房间
     * 最后进入过的房间
     * 开启最短上桌时间,留盲代打信息
     * 牌局维度
     * 房间信息
     * 俱乐部维度
     * 俱乐部玩家带入过的牌局
     * 活动相关
     * 天降红包
     */
    private void clearRedisKey() {
        RedisService redisService = RedisService.getRedisService();

        Map<Integer, Player> players = room.getDealer().getPlayers();
        if (!players.isEmpty()) {
            for (int userId : players.keySet()) {
                redisService.removeUserRoomApplySet(userId, room.getRoomPath(), room.getRoomId()); //删除玩家带入过的房间
            }
        }

        redisService.delLastRoomIdByUser(room.getRoomId(), players.keySet(), false); //删除玩家最后进入房间

        if (room.getMinPlayTime() > 0) {
            for (int userId : room.getRoomPlayers().keySet()) { //删除留盲代打缓存
                redisService.delAutoOp(userId, room.getRoomId());
            }
        }

        redisService.removeRoomsInfo(room.getRoomPath(), room.getRoomId()); //删除牌局信息

        if (null != room.getFeeService()) {
            Set<Integer> clubIds = room.getFeeService().getBringClubs();
            if (!clubIds.isEmpty()) {
                for (int clubId : clubIds) {
                    redisService.removeClubBringRoomSet(clubId, room.getRoomId()); //删除俱乐部带入的房间信息
                }
            }
        }

        SkyRedWalletRedisService skyRedWalletRedisService = SkyRedWalletRedisService.getRedisService();
        skyRedWalletRedisService.clearSkyActivitySet(room.getRoomId()); //删除红包相关key

    }

    /**
     * 释放派遣的at  手动/自动
     * 手动无需考虑当前模式开关是否开启
     *
     * @param roomId
     */
    private void releaseAt(int roomId) {

        if (AiRoomManager.getStatusOfAutoMode(roomId)) {
            AiRoomManager.releaseDispatchedAi(roomId, null, false);//释放已经派遣的at,userId要传null值
        }

        AiRoomManager.clearAllDispatchMt(); //释放派遣mt
    }

    /**
     * 销毁房间对象
     */
    private void destroyRoom() {
        logger.info("[R-{}] ######销毁房间对象#######",room.getRoomId());
        room.setRoomStatus(-999);
        LeveRoom leveRoom = Cache.getLeveRoom(room.getRoomId(), room.getRoomPath());
        leveRoom.close(room.getRoomId());
        Cache.getSecondRoom(room.getRoomPath()).getLeveRoomMap().remove(room.getRoomId());

        if (room.isJackPot() && null != room.getJackpotService()) {
            JackPotUtil.delJackPotRoom(room);
        }
    }

    /**
     * 推送mq消息
     * 关闭临时聊天组消息
     * 玩家盈利信息计算返佣
     */
    private void pushMqMessage(List<PlayerPl> playerPlList) {
        ReBateUtil.sendGameRecordInfo(room.getRoomId(), room.getRoomPath(), room.getName(), room.getClubRoomType() != 0, playerPlList); // 推送玩家盈利信息计算返佣
    }

    /**
     * 根据用户离开的相关状态做处理
     * 修改积分相关
     */
    public void trimUserInfo(RoomPersion roomPersion) {
        if (roomPersion == null || roomPersion.getUserInfo() == null) {
            return;
        }

        UserInfo userInfo = roomPersion.getUserInfo();
        if (room.getRoomStatus() > 2 && room.getRoomStatus() < 7 && roomPersion.getType() == 3) {
            userInfo.setLose(userInfo.getLose() + 1);
            roomPersion.setAc(roomPersion.getAc() + 1);
        }
    }

    /**
     * 保险购买中站起 相当于弃保
     *
     * @param rp
     */
    public void giveUpInsurance(RoomPersion rp) {
        if (room.getInsuranceActive()) {
            for (Integer userId : room.getInsurer().getHolderMap().keySet()) {
                if (rp.getUserId() == userId) {
                    if (room.getInsurer().getHolderMap().get(userId).getStatus() == 0) {
                        room.getInsurer().acceptInsurance(room, rp.getUserId());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 玩家离开房间
     *
     * @param persion       玩家信息
     * @param leaveRoomType 离开类型
     */
    public void likai2(RoomPersion persion, LeaveRoomCode leaveRoomType) {
        logger.debug("leave room,userid={},roomid={},persion type={},leaveType={},seat={}",
                persion.getUserId(), room.getRoomId(), persion.getType(), leaveRoomType.toString(), persion.getSize());

        int leaveType = leaveRoomType.getCode();
        if (persion.getRequestChip() > 0) { //避免离开的时候，还保留这下一手牌才刷新筹码，站起就添加
            RoomRequest.bindUserChip(persion, persion.getRequestChip(), room);
            persion.setRequestChip(0);    // 上局请求清0
        }

        room.delRoomAud(persion.getUserId(), leaveRoomType == LeaveRoomCode.KICK_OUT_LEAVE ? RemoveUserCode.KICKOUT_PLAYING : RemoveUserCode.LEAVE_ROOM);

        giveUpInsurance(persion);

        if (persion.getType() == 3) { //游戏中
            persion.setOnlinerType(-1);
            int zdlk = leaveType == 1 ? 1 : -1;  // -1 表示非主动离开 ..其它表示主动离开
            persion.setZdlk(zdlk);

            // 当前操作玩家离开
            if (room.getCurrentNumber() != -1 && room.getCurrentNumber() == persion.getSize()) {
                room.roomProcedure.runByRoomStatus();
            } else if (persion.getStatus() != 3) {  // 非当前玩家操作，并且没有allin
                setClientAction(persion, 6, 0);     // 玩家离开，当作弃牌，记录玩家动作
            }

            trimUserInfo(persion);
            updateUserBasicInfo(persion);

        } else {
            trimUserInfo(persion); //保存用户信息
            updateUserBasicInfo(persion);

            if (persion.getSize() > -1 && persion.getSize() < room.getPlayerCount()) {
                if (room.getRoomPersions()[persion.getSize()] != null
                        && room.getRoomPersions()[persion.getSize()].getUserId() == persion.getUserId()
                        && room.getRoomPersions()[persion.getSize()].getOnlinerType() != -1) {
                    room.getRoomPersions()[persion.getSize()] = null;
                }
                if (room.getDdRoomPersions()[persion.getSize()] != null
                        && room.getDdRoomPersions()[persion.getSize()].getUserId() == persion.getUserId()) {
                    room.getDdRoomPersions()[persion.getSize()] = null;
                }
            }
        }

        //离开房间从观察者中干掉
        room.getDealer().removeWatcher(persion.getUserId());
        if (persion.getSize() > -1 && persion.getSize() < room.getPlayerCount()) {    // 坐下过，首次离开
            Object[] status = {1, persion.getPlayType()};
            if (leaveType == 1) { // 主动离开
                status[0] = 0;
            }
            room.getDealer().setUserStatus(persion.getUserId(), status);
        }

        if (persion.getSize() > -1) { //如果该玩家已经坐下,则要通知别的玩家有人起来了

            Object[][] objs2 = {
                {60, persion.getSize(), I366ClientPickUtil.TYPE_INT_1},
                {130, persion.getUserId(), I366ClientPickUtil.TYPE_INT_4}};
            byte[] bytes2 = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_RECV_LEAVE);
            PublisherUtil.send(room, bytes2, persion.getUserId());
        }
        //2024/11/25 :line 986 room.delRoomAud 已经处理了用户离开的行为，这里应该是重复了,因此注释了，
        /*if (AiRuleTemplate.getUserType(persion.getUserId()) != null) { //如果是auto ai需要调用移除ai接口,同时牌局中ai数量-1

            if (EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(), persion.getUserId())) {
                AiRoomManager.releaseDispatchedAi(room.getRoomId(), persion.getUserId(), false); //释放at
                room.getAtRoomConfig().setAutoAiCount(room.getAtRoomConfig().getAutoAiCount() - 1);
            }


            if (EAiMode.manual == AiRoomManager.getModeOfPlayer(room.getRoomId(), persion.getUserId())) {//释放mt
                AiRoomManager.removeDispatchedMt(persion.getUserId());
            }

        }*/


        checkRoomPlayingNum();

        RoomPlayer roomPlayer = room.getRoomPlayers().get(persion.getUserId());
        roomPlayer.setSeatStatus(0);  //设置为站起状态
        roomPlayer.setSeat(-1);
        roomPlayer.setSeatSize(-1);

        if (persion.getSize() > -1) { //在座位上时，离开房间才需要更新空余座位
            int handTotal = room.getDealer().getHandCnt(persion.getUserId()); //玩家已经打的手数
            room.getRoomSeatChangeService().checkSeatChangeTpye(room.getRoomId(), persion.getUserId(), room.getStage(), handTotal, RoomSeatChangeCode.STANDUP_ROOM);
        }

        RabbitMqUtil.leaveChatGroupRoom(room.getRoomId(), persion.getUserId());

        gameEventService.emitLeaveRoom(room, persion.getUserId());

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);
    }

    /**
     * 检测房间人数，如果只有一个在玩，直接结束
     */
    public void checkRoomPlayingNum() {
        Object[] objs = RoomUtil.getRoomPlayingNum(room);
        int count = (int) objs[0];
        int userId = (int) objs[1];

        // 桌面只有1个玩家
        if (room.getRoomStatus() == 1 || room.getRoomStatus() == 2) { //非游戏
        } else { //游戏中
            if (count == 1) {
                room.setT3(-989);
                Map<Integer, Object> map = new HashMap<Integer, Object>();
                map.put(1, 1);       // 最后没有弃牌玩家数
                map.put(2, room.getAudMap().get(userId).getSize());      // 如果只剩一个玩家，玩家座位号
                map.put(3, room.roomProcedure.getBipaiIndex());     // 比牌index
                Task bipaiTask = new Task(TaskConstant.TASK_BIPAI, map, room.getRoomId(), room.getRoomPath());
                WorkThreadService.submit(room.getRoomId(), bipaiTask);
            }
        }
    }

    /**
     * 玩家二次进入房间时
     * 通知别的玩家有人退出房间
     * -1 不做任何操作 1失败 2逻辑往下走 4还在其他房间
     */
    public synchronized int ercijinru(int userID) {
        RoomPersion persion = room.getAudMap().get(userID);
        if (persion == null) {
            return 2;
        }

        if ((persion.getStatus() == 3 || persion.getStatus() == 2 || persion.getStatus() == 1)) { //还在其他房间内 且处于加注、跟注、allin、让牌状态
            return 4;
        } else {
            userTimeOut2(persion.getUserInfo(), 2);
            return 2;
        }

    }

    /**
     * 玩家离开房间
     *
     * @param person       玩家信息
     * @param leaveRoomType 离开类型
     */
    public synchronized void likai(RoomPersion person, LeaveRoomCode leaveRoomType) {
        likai2(person, leaveRoomType);

        int status = 0;
        int action = 3;

        if (LeaveRoomCode.AI_PLAY_ENOUGH_HANDS_LEAVE.getCode() == leaveRoomType.getCode()
                || LeaveRoomCode.AI_PLAY_ENOUGH_BRING_IN_LEAVE.getCode() == leaveRoomType.getCode()
                || LeaveRoomCode.AI_PLAY_ENOUGH_BRING_IN_TIMES_LEAVE.getCode() == leaveRoomType.getCode()
                || LeaveRoomCode.KICK_OUT_LEAVE.getCode() == leaveRoomType.getCode()
                || LeaveRoomCode.AI_PLAY_NOT_ENOUGH_BRING_LEAVE.getCode() == leaveRoomType.getCode()) {
            //把AI踢出
            action = 5;
        }else if (LeaveRoomCode.FOBBIDEN_LEAVE.getCode() == leaveRoomType.getCode()) { //玩家冻结离开
            status = 13;
            action = 7;
        }

        logger.debug("【玩家离开】【JYM】[UID-{}][name-{}] leave reason ={}, ACTION={}",person.getUserId(),person.getUserInfo().getNikeName(),leaveRoomType.getDesc(),action);
        Object[][] objs = {
            {60, status, I366ClientPickUtil.TYPE_INT_1},
            {61, action, I366ClientPickUtil.TYPE_INT_1},
            {130, person.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
            {131, person.getChouma(), I366ClientPickUtil.TYPE_INT_4}
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_SEND_SEAT_ACTION);
        PublisherUtil.publisher(person.getUserInfo(), bytes);
    }


    /**
     * 请求坐下
     *
     * @param userId
     * @param size   座位号
     * @return -1 坐下成功 1 坐下失败  2 有同ip用户 3 房间强制关闭不让坐 4 当前信用额度小于离座筹码 6 社区房间有结算请求未被处理
     * 7 gps限制无法入座 15 餘额不足无法入座
     */
    public synchronized int downRoom(int userId, int size, UserInfo userInfo) {
        if (room.roomProcedure.forceCloseRoom) {
            logger.debug("room force stop, cannot sit down");
            return 3;
        }
        logger.debug("userId=" + userId + " apply seat=" + size);
        logger.debug("occupy seats are: " + room.getOccupySeatPlayers().keySet().toString());
        for (Integer id : room.getOccupySeatPlayers().keySet()) {
            // 当前房间离桌留座玩家
            RoomPlayer roomPlayer = room.getOccupySeatPlayers().get(id);
            if (null != roomPlayer) {
                int occupySeat = roomPlayer.getSeatSize();
                if (3 == roomPlayer.getSeatStatus()) {
                    if (size == occupySeat) {
                        if (userId == id) {
                            // 离桌留座的玩家申请再次坐下
                            logger.debug("userId=" + userId + " resit to his occupy seat=" + size);
                            //
                        } else {
                            // 该位置已被占座
                            logger.debug("seat=" + size + " is occupied by " + id);
                            return Constant.SEAT_OCCUPIED;
                        }
                    }
                }
            }
        }

        /**
         * 如果该座位已经被占用，则返回 1
         */
        for (Integer id : room.getRoomPlayers().keySet()) {
            RoomPlayer roomPlayer = room.getRoomPlayers().get(id);
            if (null != roomPlayer && 4 == roomPlayer.getSeatStatus()) {
                int seat = roomPlayer.getSeatSize();
                if (size == seat) {
                    logger.debug("seat=" + size + " is zhanzuo by " + id);
                    return Constant.SEAT_OCCUPIED;
                }
            }
        }

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        if (roomPlayer.getSettledSum() > 0) {
            // 提前结算再回來, 需要做異步帶入
            logger.debug("需要做異步帶入, roomId={} settledSum={}", room.getRoomId(), roomPlayer.getSettledSum());
            if (!hasChip(room, userInfo, roomPlayer.getSettledSum())) {
                logger.error("用戶餘額不足, 坐下失敗", roomPlayer.getSettledSum());
                return Constant.INSUFFICIENT_BALANCE;
            }
        }

        int realPos = downRoom2(userId, size, userInfo); // 实际坐下位置

        if (realPos == -1) {
            logger.debug("downRoom fail,roomid={},userid={}", room.getRoomId(), userId);
            return Constant.SEAT_OCCUPIED;
        } else if (realPos == -2) {
            logger.debug("desk has the same ip user,roomid={},userid={}", room.getRoomId(), userId);
            return Constant.SAME_IP;
        } else if (realPos == -3) {
            logger.debug("can't sit down for gps district,roomid={},userid={}", room.getRoomId(), userId);
            return Constant.SAME_GPS;
        } else {
            //通知别的玩家有人坐下了
            RoomPersion p = room.getRoomPersions()[realPos];
            if (p == null || p.getOnlinerType() == -1) {
                p = room.getDdRoomPersions()[realPos];
            }

            if (p == null || p.getOnlinerType() == -1) {
                p = room.getAudMap().get(userId);
            }

            byte[] bytes;
            if (realPos == size) {
                for (Integer uid : room.getAudMap().keySet()) {
                    if (uid != userId) {
                        String head = p.getUserInfo().getHead();
                        if (p.getUserInfo().getUseCustom().equals(1)) {
                            if (!StringUtils.isBlank(p.getUserInfo().getCustomUrl())) {
                                head = p.getUserInfo().getCustomUrl();
                            }
                        }
                        Object[][] objs = {
                            {61, realPos, I366ClientPickUtil.TYPE_INT_1},
                            {62, p.getUserInfo().getSex(), I366ClientPickUtil.TYPE_INT_1},
                            {130, head, I366ClientPickUtil.TYPE_STRING_UTF16},
                            {131, p.getUserInfo().getNikeName(uid, userId), I366ClientPickUtil.TYPE_STRING_UTF16},
                            {132, userId, I366ClientPickUtil.TYPE_INT_4},
                            {133, p.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                        };
                        bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_SEAT_DOWN);
                        PublisherUtil.sendByUserId(room, bytes, uid);
                    }
                }
            }
            //是否需要选择过庄或者补盲
            int playTypeChoose = 1;
            if (room.getStage() == 0 || p.isCanPlay() || RoomUtil.getCanPlayNum(room) < 2 || p.getPlayType() != 0) {
                playTypeChoose = 0;
            }

            if (p.getNowcounma() < room.getQianzhu() + room.getDamanzhu()) { //筹码小于前注加大盲时不需要弹补盲按钮
                playTypeChoose = 0;
            }

            logger.debug("p.canPlay={} p.playType()={} playTypeChoose={} stage={}", p.isCanPlay(), p.getPlayType(), playTypeChoose, room.getStage());

            int settledSum = 0;
            int feeLocked = 0;
            if (roomPlayer.getSettledSum() > 0) {
                settledSum = roomPlayer.getSettledSum();
                feeLocked = roomPlayer.getFeeLocked();
                logger.debug("即將進行異步帶入, roomId={} settledSum={} feeLocked={}", room.getRoomId(), settledSum, feeLocked);
                mockAddChip(room, userInfo, roomPlayer.getSeat(), settledSum);
            }

            Object[][] objs2 = {
                {60, 0, I366ClientPickUtil.TYPE_INT_1},
                {61, 1, I366ClientPickUtil.TYPE_INT_1},
                {130, p.getNowcounma() + settledSum + feeLocked, I366ClientPickUtil.TYPE_INT_4},
                {131, p.getChouma(), I366ClientPickUtil.TYPE_INT_4},
                {132, realPos, I366ClientPickUtil.TYPE_INT_1},
                //{133, playTypeChoose, I366ClientPickUtil.TYPE_INT_4},		// 是否需要弹补盲选择框 0不需要 1需要
                {133, p.getPlayType(), I366ClientPickUtil.TYPE_INT_4},        // 是否需要弹补盲选择框 0不需要 1需要
                {134, roomPlayer.getSelectClubId(), I366ClientPickUtil.TYPE_INT_4},
            };
            bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.publisher(p.getUserInfo(), bytes);

            int handTotal = room.getDealer().getHandCnt(userId); //玩家已经打的手数
            room.getRoomSeatChangeService().checkSeatChangeTpye(room.getRoomId(), userId, room.getStage(), handTotal, RoomSeatChangeCode.DOWN_ROOM);
            return -1;
        }
    }

    private boolean hasChip(Room room, UserInfo userInfo, int minChip) {
        UserInfoDao userInfoDao = new UserInfoDaoImp();
        try (Connection conn = DBUtil.getConnection()) {
            int balance = 0;
            if (room.getTribeRoomType() == 1) {
                balance = userInfoDao.queryUserTribeChip(conn, userInfo.getClubId(), userInfo.getUserId());
            } else if (room.getClubRoomType() != 0) {
                balance = userInfoDao.queryUserGold(conn, userInfo.getUserId());
            }
            logger.debug("clubId={} balance={} required={}", userInfo.getClubId(), balance, minChip);
            return balance >= minChip;
        } catch (SQLException ex) {
            logger.error("Unable to obtain user balance", ex);
        }
        return false;
    }

    private static final String MONGO_requestInfoRecord = "add_request_info";//带入成功的信息表(与其他应用共用此集合名)
    private static final String MONGO_db = "dzpk";

    /**
     * 查出玩家在房间内第一次成功带入的信息
     * @param roomId
     * @param userId
     * @return
     */
    private static Document getRequestInfo(int roomId,int userId){
        MongoClient mongo = null;
        MongoCursor<Document> mc;
        Document doc = null;
        try {
            mongo = MongodbService.getMongoInstance();
            MongoDatabase database = mongo.getDatabase(MONGO_db);
            MongoCollection<Document> requestCollection = database.getCollection(MONGO_requestInfoRecord);

            BasicDBObject fil = new BasicDBObject("user_id", userId);
            fil.append("room_id", roomId);

            mc = MongodbService.find(requestCollection, fil, 1, "time", 1); //查出这个房间第一次成功带入的信息

            if (mc.hasNext()) {
                doc = mc.next();
                return doc;
            }
        }catch(Exception e){
            logger.error(e.getMessage());
        }finally {
            MongodbService.close(mongo);
        }

        return doc;
    }

    private static void mockAddChip(Room room, UserInfo userInfo, int seat, int chips) {
        try {
            Document doc = getRequestInfo(room.getRoomId(), userInfo.getUserId());//获取用户带入信息
            String ip = userInfo.getIp();
            String longitudeStr = String.valueOf(userInfo.getLongitude());
            String latitudeStr = String.valueOf(userInfo.getLatitude());
            String imei = userInfo.getImei();
            String mac = doc.getString("mac");
            int virtual = 0;

            Map<Integer, Object> map = new HashMap<>();
            map.put(60, seat);
            map.put(61, 1);
            map.put(62, longitudeStr);
            map.put(63, latitudeStr);
            map.put(64, ip);
            map.put(130, chips);
            map.put(131, room.getRoomPath());
            map.put(132, room.getRoomId());
            map.put(134, userInfo.getClubId());
            map.put(136, imei);
            map.put(137, mac);
            map.put(138, virtual);
            map.put(139, "");

            Request request = new Request();
            request.setChannel(userInfo.getChannel());
            request.setUserId(userInfo.getUserId());
            Task task = new Task(Constant.REQ_GAME_RECV_ADD_CHIPS, map, request, room.getRoomId(), room.getRoomPath());

            WorkThreadService.submitDelayTask(room.getRoomId(), task, 100);

        } catch (Exception e) {
            logger.error("mockAddChip error", e);
        }
    }

    /**
     * 请求坐下
     *
     * @param userId
     * @param size   座位号
     * @return -1 坐下失败 >-1 坐下成功的座位 -2 ip限制功能 -3gps限制
     */
    public int downRoom2(int userId, int size, UserInfo userInfo) {
        int numOccupied = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) { // 判断用户是否在其他位置已经坐下,防止一个用户坐多个位置
            RoomPersion rpl = room.getRoomPersions()[i];
            if (rpl != null) {
                numOccupied++;
                logger.debug(rpl.getUserId() + " is on seat " + i);
            }
            if (rpl != null && rpl.getOnlinerType() != -1 && rpl.getUserId() == userId) {   // 在游戏位
                logger.debug("user already in postion: " + i);
                return i;
            }
            RoomPersion rpdl = room.getDdRoomPersions()[i];
            if (null != rpdl) {
                logger.debug(rpdl.getUserId() + " is waiting on seat " + i);
            }
            if (rpdl != null && rpdl.getUserId() == userId) {       // 在等待位
                logger.debug("user already in wait postion: " + i);
                return i;
            }
        }

        if (room.isLimitGPS() && null != userInfo) {
            if (RoomIpGps.checkGPS(userId, userInfo, room)) { // 检查新坐下玩家是否符合gps限制规则
                return -3;
            }
        }

        if (room.isLimitIp() && null != userInfo) {
            if (RoomIpGps.checkIp(userId, userInfo.getIp(), room)) {
                return -2;
            }
        }

        RoomPersion rp = room.getRoomPersions()[size];
        RoomPersion rpd = room.getDdRoomPersions()[size];

        RoomPersion roomPersion = room.getAudMap().get(userId);
        if (rpd != null || (rp != null && rp.getOnlinerType() != -1)) {
            logger.debug("postion has another seated user!");
            return -1;
        }

        if (roomPersion == null) {
            logger.debug("room person null");
            return -1;
        }
        //对于坐下的玩家初始化这些信息
        roomPersion.setBetChouma(0);
        roomPersion.setAnteCount(0);
        roomPersion.setUserId(userId);
        roomPersion.setStatus(0);
        roomPersion.setClientStatus(1);
        roomPersion.setSize(size);
        roomPersion.setBetStatus(0);
        roomPersion.setAnteNumber(0);
        roomPersion.setTimeoutCheckOpTimes(0);
        roomPersion.setTimeoutFoldOpTimes(0);
        roomPersion.setPocers(new Pocer[2]);
        roomPersion.setPreStage(room.getStage() - 1);
        roomPersion.setFundLast(0);
        roomPersion.setMuck(false);
        roomPersion.setTanpai(false);
        roomPersion.setType(6); //占座状态
        roomPersion.setPassHand(0);

        /***
         * 已经补充带入过(不考虑是否开启控制带入，筹码足够前注+大盲时，该玩家坐下，要把用户放到下一手要玩的集合中
         * 同时设置是否显示气泡为1
         */
        if (roomPersion.getNowcounma() > room.getQianzhu() + room.getDamanzhu()) {
            roomPersion.setPassHand(roomPersion.getPassHand() + 1);
            room.getDdRoomPersions()[size] = roomPersion;
        }

        logger.debug("now chips={},total chips={},standType={},playType={}", roomPersion.getNowcounma(), roomPersion.getChouma(), roomPersion.getStandupType(), roomPersion.getPlayType());

        if (roomPersion.getStandupType() == 0) {// 主动站起设置为过庄
            roomPersion.setPlayType(0);
        }
        if ((roomPersion.getStandupType() != 0 && roomPersion.getPlayType() == 2) || room.getStage() == 0) {// 被动离开，或者第一手 直接可以打
            roomPersion.setCanPlay(true);
            logger.debug("R-{} U-{} 被动离开，或者第一手 直接可以打, canPlay -> true", room.getRoomId(), userId);
        } else {
            roomPersion.setCanPlay(false);
            roomPersion.setGameType(1);
            logger.debug("R-{} U-{} 不是被动离开或者第一手, canPlay -> false", room.getRoomId(), userId);
        }
        if (room.getZhuangjiaNumber() > -1 && numOccupied > 2) {
            roomPersion.setCanPlay(false);
            roomPersion.setGameType(1);
            logger.debug("R-{} U-{} zhuangjia={} numOccupied={}, canPlay -> false", room.getRoomId(), userId, room.getZhuangjiaNumber(), numOccupied);
        }

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);

        if (roomPlayer != null) {

            room.getDealer().removeWatcher(userId);// 坐下后得从观察者移除
            room.removeLeaveUser(userId);

            if (room.getClubId() > 0 && room.isClubRoom()) {
                ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
                ClubSuperior superior = clubTribeDao.getClubSuperior(room.getClubId(), userId);
                if (superior != null) {
                    room.getDealer().setSuperior(userId, superior);
                }
            }

            /**
             * 房间开始前,如果开启自动开局,需要校验是否自动开局
             * 房间开始后，按照之前逻辑
             **/
            if (room.isAutoStartRoom() && room.getStatus() == 1) {
                Task task1 = new Task(TaskConstant.TASK_ROOMSTATUS1, null, room.getRoomId(), room.getRoomPath());
                WorkThreadService.submit(room.getRoomId(), task1);
            } else {
                if (room.getRoomStatus() == 1) {
                    Task task1 = new Task(TaskConstant.TASK_ROOMSTATUS1, null, room.getRoomId(), room.getRoomPath());
                    WorkThreadService.submit(room.getRoomId(), task1);
                }
            }

            roomPlayer.incrSeatTimes(); // 增加坐下次数和时间
            if (roomPlayer.getDownTime() <= 0) {  //第一次坐下时才设置坐下时间
                roomPlayer.setDownTime(System.currentTimeMillis() / 1000);
            }
            roomPlayer.setSeat(size);
            roomPlayer.setSeatSize(size); // 被占座位编号
            try {
                if (userInfo == null) {    //如果此时玩家信息为空时，再次从数据库中获取该玩家信息
                    UserInfoDao userInfoDao = new UserInfoDaoImp();
                    userInfo = userInfoDao.getUserInfo(userId);
                }
            } catch (SQLException e) {
                logger.error("fail to get user info", e);
                return -1;
            }

            roomPlayer.setChouma(userInfo.getChip());  //设置为用户的真实筹码
            roomPlayer.setNickName(userInfo.getNikeName());
            roomPlayer.setHeader(userInfo.getHead());
            roomPlayer.setUseCustom(userInfo.getUseCustom());       // 玩家是否使用自定义头像
            roomPlayer.setCustomUrl(userInfo.getCustomUrl());       // 玩家的自定义头像
            roomPlayer.setSex(userInfo.getSex());

            room.getRoomPlayers().put(userId, roomPlayer);

            if (AiRuleTemplate.isAiUser(userId) && Constant.ROOM_PATH_NORMAL == room.getRoomPath()) {//自动、手动at初始化
                AiHelper.initData(roomPlayer, userId, room.getMinRate(), room.getMaxRate(), room.getRoomId());
            }

            long leftTime = RoomAutoOp.needAutoOp(room, userId);

            int handTotal = room.getDealer().getHandCnt(userId);
            logger.debug("downRoom2 leftTime: " + leftTime + " handTotal: " + handTotal);
            if (leftTime > 0 && handTotal > 0) {
                roomPersion.setAutoOp(false);
                // 坐下，设置最短打牌时间任务（开启最短打牌任务）
                Map<Integer, Object> map = new HashMap<Integer, Object>();
                map.put(1, userId);
                Task task = new Task(TaskConstant.TASK_MIN_PLAYTIME, map, room.getRoomId(), room.getRoomPath(), userId); // 20241118 add userId
                WorkThreadService.submitDelayTask(room.getRoomId(), task, leftTime * 1000);
                room.roomProcedure.delayTaskMap.put(task.getId(), task);
                // 记录正在留盲代打房间信息
                Map<String, String> hash = new HashMap<String, String>();
                hash.put("roomId", String.valueOf(room.getRoomId()));
                hash.put("name", room.getName());
                RedisService.getRedisService().setAutoOp(userId, room.getRoomId(), hash);
            }
        }

        logger.debug("user down pos: " + size);

        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);

        return size;
    }


    /**
     * 设置大小盲注 庄家编号..已经存在则往后推一位 //玩家如果筹码不够则全下
     */
    public void nextmanzhu() {
        // 能够打牌的人数
        Set<Integer> canPlayUids = new HashSet<Integer>();
        //  设置用户相关状态
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            if (room.getRoomPersions()[i] != null) {
                room.getRoomPersions()[i].setStatus(0);
                room.getRoomPersions()[i].setClientStatus(1);
                room.getRoomPersions()[i].setAnteCount(0);
                room.getRoomPersions()[i].setBetChouma(0);
                if (room.getPlayType() != 1 || room.getRoomPersions()[i].isCanPlay()) {
                    canPlayUids.add(room.getRoomPersions()[i].getUserId());
                }
            }
        }

        // 少于3个人或者第一把 直接可以打
        if (room.getZhuangjiaNumber() < 0 || canPlayUids.size() < 3) {
            for (RoomPersion rp : room.getRoomPersions()) {
                if (rp != null) {
                    rp.setCanPlay(true);
                    rp.setGameType(-1);
                    canPlayUids.add(rp.getUserId());
                }
            }
        }
        //  随机指定个庄家
        if (room.getZhuangjiaNumber() < 0) {
            int a = Room.getR().nextInt(room.getPlayerCount());
            for (int i = 0; i < room.getRoomPersions().length; i++) {
                if (room.getRoomPersions()[a] != null &&
                        (room.getPlayType() != 1 || room.getRoomPersions()[a].isCanPlay())) {
                    room.setZhuangjiaNumber(a);
                    break;
                }
                a = (a + 1) % room.getPlayerCount();
            }
        }


        // 设置最小加注限制
        room.setMinRaiseNumber(room.getManzhu());
        room.setRaisePerson(-1);

        // 先把在大盲位和莊家之間的玩家設成可打牌
        // 推一位後，原來的大盲位後第一個玩家成為大盲
        checkCanPlay(false);

        logger.debug("sb: " + room.manzhuNumber + ", bb: " + room.damanzhuNumber + ", z: " + room.getZhuangjiaNumber());

        //  推一位
        boolean shiftSuccessful = false;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            room.zhuangjiaNumber = (room.zhuangjiaNumber + 1) % room.getPlayerCount();
            if (room.getRoomPersions()[room.zhuangjiaNumber] != null &&
                    room.getRoomPersions()[room.zhuangjiaNumber].getGameType() != 1) {
                if (room.getPlayType() == 1 && !room.getRoomPersions()[room.zhuangjiaNumber].isCanPlay()) {
                    continue;
                }
                int a = room.zhuangjiaNumber;
                for (int i2 = 0; i2 < room.getRoomPersions().length; i2++) {
                    a = (a + 1) % room.getPlayerCount();
                    if (room.getRoomPersions()[a] != null &&
                            room.getRoomPersions()[a].getGameType() != 1) {
                        if (room.getPlayType() == 1 && !room.getRoomPersions()[a].isCanPlay()) {
                            continue;
                        }
                        room.manzhuNumber = a;

                        for (int i3 = 0; i3 < room.getRoomPersions().length; i3++) {
                            a = (a + 1) % room.getPlayerCount();
                            if (room.getRoomPersions()[a] != null &&
                                    room.getRoomPersions()[a].getGameType() != 1) {
                                canPlayUids.add(room.getRoomPersions()[a].getUserId());
                                room.getRoomPersions()[a].setCanPlay(true); // 标记为可参与牌局
                                room.damanzhuNumber = a;
                                if (canPlayUids.size() == 2) {                             //  两人局特殊处理
                                    room.damanzhuNumber = room.manzhuNumber;
                                    room.manzhuNumber = room.zhuangjiaNumber;
                                } else if (canPlayUids.size() > 3) {
                                    int guanshaNumber = room.getZhuangjiaNumber() - 1;
                                    guanshaNumber = room.getZhuangjiaNumber() - 1 < 0
                                    ? room.getPlayerCount() - 1 : guanshaNumber;
                                    room.setGuanshaNumber(guanshaNumber);    //  三人以上的牌局有关煞位(CO)
                                }

                                // 大盲位是补盲玩家，直接设置成正常玩家
//                                if (room.getRoomPersions()[room.damanzhuNumber].getPlayType() == 1) {
//                                    room.getRoomPersions()[room.damanzhuNumber].setPlayType(2);
//                                }
                                shiftSuccessful = true;
                                break;
                            }
                        }
                        break;
                    }
                }
                break;
            }
        }

        // 纠正两人局大小盲位置
        if (canPlayUids.size() == 2) {
            correctBlindPosition(room);
        }

        // 推一位後，再次把在大盲位和莊家之間的玩家設成可打牌
        // 於是原來的莊家和小盲位之間的所有玩家都可以打牌
        checkCanPlay(true);

        logger.debug("sb: " + room.manzhuNumber + ", bb: " + room.damanzhuNumber + ", z: " + room.getZhuangjiaNumber());

        if (shiftSuccessful) {
            Stream<RoomPersion> canPlayPersions = Stream.of(room.getRoomPersions())
                    .filter(Objects::nonNull)
                    .filter(rp -> rp.isCanPlay() || room.getPlayType() != 1);
            canPlayPersions.forEach(person -> {
                int minChouma = 0;
                // 如果前注大于0 所有玩家都下前注
                if (room.getQianzhu() > 0) {
                    minChouma += room.getQianzhu();
                }

                if (minChouma > 0) {
                    if (person.getNowcounma() <= minChouma) {
                        person.setBeforeAllinScore(person.getNowcounma());
                        person.setBetChouma(person.getNowcounma());
                        person.setNowcounma(0);
                        room.getDealer().addAllinCnt(person);
                        room.setPoolChip(room.getPoolChip() + person.getNowcounma());
                    } else {
                        person.setBetChouma(minChouma);
                        person.setNowcounma(person.getNowcounma() - minChouma);
                        room.setPoolChip(room.getPoolChip() + minChouma);
                    }
                    person.setAnteCount(person.getBetChouma());
                }
            });
        }

        if (logger.isTraceEnabled()) {
            for (int i = 0; i < room.getPlayerCount(); i++) {
                RoomPersion rp1 = room.getRoomPersions()[i];
                RoomPersion rp2 = room.getDdRoomPersions()[i];
                logger.trace("R-{} i={} rp1={} rp2(dd)={} canPlay={} playType={} gameType={}",
                        room.getRoomId(), i,
                        rp1 != null ? rp1.getUserId() : -1,
                        rp2 != null ? rp2.getUserId() : -1,
                        (rp1 != null ? rp1.isCanPlay() : rp2 != null && rp2.isCanPlay()),
                        (rp1 != null ? rp1.getPlayType() : rp2 != null ? rp2.getPlayType() : 0),
                        (rp1 != null ? rp1.getGameType() : rp2 != null ? rp2.getGameType() : 0));
            }
        }

        if (room.getManzhuNumber() < 0 || room.getDamanzhuNumber() < 0
                || room.getManzhuNumber() > room.getPlayerCount() || room.getDamanzhuNumber() > room.getPlayerCount()
                || room.getZhuangjiaNumber() < 0 || room.getZhuangjiaNumber() > room.getPlayerCount()
                || room.getRoomPersions()[room.getZhuangjiaNumber()] == null
                || room.getRoomPersions()[room.getManzhuNumber()] == null
                || room.getRoomPersions()[room.getDamanzhuNumber()] == null) {
            logs();
        }

    }

    private void checkCanPlay(boolean moveToWaiting) {
        if (room.getPlayType() != 1) return;
        // 将所有在座位上不能打牌的玩家放到等待位上
        for (int k = 0; k < room.getRoomPersions().length; k++) {
            RoomPersion rp = room.getRoomPersions()[k];
            if (rp == null || rp.isCanPlay()) continue;
            boolean b = false;
            if (rp.getGameType() == 1) {
                int dealerPos = room.getZhuangjiaNumber();
                int sbPos = room.getManzhuNumber();
                int bbPos = room.getDamanzhuNumber();
                int myPos = rp.getSize();
                if (dealerPos > 0) {
                    int offset = room.getPlayerCount() - dealerPos;
                    sbPos = (sbPos + offset) % room.getPlayerCount();
                    bbPos = (bbPos + offset) % room.getPlayerCount();
                    myPos = (myPos + offset) % room.getPlayerCount();
                    dealerPos = 0;
                }
                logger.debug("R-{} U-{} 相對順位: 莊家={}, 小盲={}, 大盲={}, 玩家={}", room.getRoomId(), rp.getUserId(), dealerPos, sbPos, bbPos, myPos);
                // 玩家在大盲位和莊家之間
                if (myPos > bbPos) {
                    logger.debug("R-{} U-{} 可以打牌, {} > {}", room.getRoomId(), rp.getUserId(), myPos, bbPos);
                    rp.setPlayType(0); // 25/01/15 取消自動補盲
                    rp.setCanPlay(true);
                    rp.setGameType(-1);
                    b = true;
                }
            }
            if (!b && moveToWaiting) {
                logger.debug("R-{} U-{} 等待中, gameType={}", room.getRoomId(), rp.getUserId(), rp.getGameType());
                room.getDdRoomPersions()[k] = rp;
                room.getRoomPersions()[k] = null;
                if (rp.getGameType() != 1) {
                    logger.debug("R-{} U-{} why gameType != 1?", room.getRoomId(), rp.getUserId());
                    rp.setGameType(1);
                }
            }
        }
    }

    public void initBetChip(boolean needStraddle) {
        logger.debug("初始化注碼, 底池={}", room.getPoolChip());
        room.setCurrentNumber(room.damanzhuNumber);
        boolean validStraddle = needStraddle;
        SingleProcess singleProcess;
        int count = 0;
        for (RoomPersion persion : room.getRoomPersions()) {
            if (persion != null) {
                count++;
            }
        }
        Integer[] roleUsers = room.getRoleUsers(count);
        int start = -1;
        int roule = 0;
        while (room.manzhuNumber != start) {
            if (start == -1) {
                start = room.manzhuNumber;
            }
            start = (start + 1) % room.getPlayerCount();
            RoomPersion rp2 = room.getRoomPersions()[start];
            if (rp2 != null && room.manzhuNumber != start) {
                roule++;
                rp2.setRole(roleUsers[roule]);
            }
        }

        SingleProcess singleProcessStraddle = null;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            if (i == room.damanzhuNumber || i == room.zhuangjiaNumber || i == room.manzhuNumber) {
                continue;
            }
            RoomPersion rp1 = room.getRoomPersions()[i];
            if (rp1 != null) {
                int minChouma = 0;
                if (rp1.getSize() != room.zhuangjiaNumber && rp1.getSize() != room.manzhuNumber
                        && rp1.getSize() != room.damanzhuNumber) {
                    if (validStraddle && rp1.getStraddleChip() > 0) {
                        minChouma = rp1.getStraddleChip();
//                        rp1.setPlayType(2);  // 去掉补盲
                        room.setCurrentNumber(i);
                        //抓头
                        singleProcessStraddle = SingleProcess.builder().role(rp1.getRole()).userName(rp1.getUserInfo().getNikeName())
                                .action(6).wager(minChouma).chip(rp1.getNowcounma() - minChouma).build();
                        room.setZhuaTou(true);
                        room.setZhuaTouRoomPersion(true);
                        room.setLastBet(rp1.getStraddleChip());
                        room.setStraddleNumber(rp1.getSize());
                        validStraddle = false;
                    } else {
                        if (rp1.getStraddleChip() > 0) {
                            logger.warn("选择抓头位失败======" + rp1.getSize());
                            rp1.setStraddleChip(-1);
                        }
                        if (rp1.getPlayType() == 1) {
                            minChouma = room.getDamanzhu();
                            if (needStraddle) {
                                minChouma = minChouma + room.getDamanzhu();
                            }
                            room.getBuMangPlay().put(rp1.getUserId(), minChouma);
                            room.getDisposableBuMangPlay().put(rp1.getUserId(), minChouma);
                        }
                    }
                }
                //补盲用户抓头不下前注造成一系列错误
                if (rp1.getPlayType() == 1) {
                    minChouma = minChouma + room.getQianzhu();
                }
                if (minChouma > 0) {
                    if (rp1.getNowcounma() <= minChouma) {
                        minChouma = rp1.getNowcounma();
                        rp1.setBetChouma(rp1.getBetChouma() + rp1.getNowcounma());
                        rp1.setAnteCount(rp1.getBetChouma());
                        rp1.setNowcounma(0);
                        rp1.setStatus(3);
                        rp1.setClientStatus(4);
                        room.getDealer().addAllinCnt(rp1);
                    } else {
                        rp1.setNowcounma(rp1.getNowcounma() - minChouma);
                        rp1.setBetChouma(rp1.getBetChouma() + minChouma);
                        rp1.setAnteCount(rp1.getBetChouma());

                    }
                    if (room.getMaxChouma() < rp1.getBetChouma()) {
                        room.setMaxChouma(rp1.getBetChouma());
                    }

                    room.setPoolChip(room.getPoolChip() + minChouma);
                }
                logger.debug("[R-{} : RS={}][U-{} : {}] initBetChip ： betChouma={},anteCount={}",
                        room.getRoomId(), room.getRoomStatus()
                        ,rp1.getUserId(),rp1.getUserInfo().getNikeName(),
                        rp1.getBetChouma(),rp1.getAnteCount());
            }
        }
        logger.debug("设置小盲位, 底池={}", room.getPoolChip());
        RoomPersion rp = null;
        room.setLastBetPlayer(room.getCurrentNumber()); // 设置最后一个操作玩家
        nextCurrentNumber2();  // 设置下一个操作玩家

        rp = room.getRoomPersions()[room.manzhuNumber]; //设置小盲位
        rp.setRole(roleUsers[0]);
        if (rp.getNowcounma() <= room.getManzhu()) {  //  全下了
            rp.setBetChouma(rp.getBetChouma() + rp.getNowcounma());
            rp.setAnteCount(rp.getBetChouma());
            rp.setNowcounma(0);
            rp.setStatus(3);
            rp.setClientStatus(4);
            room.getDealer().addAllinCnt(rp);
            singleProcess = SingleProcess.builder().role(rp.getRole()).userName(rp.getUserInfo().getNikeName())
                    .action(7).wager(rp.getBetChouma()).chip(rp.getNowcounma()).build();
        } else {
            rp.setBetChouma(rp.getBetChouma() + room.getManzhu());
            rp.setAnteCount(rp.getBetChouma());
            rp.setNowcounma(rp.getNowcounma() - room.getManzhu());
            rp.setStatus(2);
            rp.setClientStatus(1);
            singleProcess = SingleProcess.builder().role(rp.getRole()).userName(rp.getUserInfo().getNikeName())
                    .action(7).wager(room.getManzhu()).chip(rp.getNowcounma()).build();
        }

        room.setPoolChip(room.getPoolChip() + room.getManzhu());
        logger.debug("小盲的保存, 底池={}", room.getPoolChip());
        //小盲的保存
        historyDao.insertSingleGameProcess(room.getRoomId(), room.getStage(), 1, singleProcess);


        rp = room.getRoomPersions()[room.damanzhuNumber]; //  大盲
        if (rp.getNowcounma() <= room.getDamanzhu()) { //  全下了
            rp.setBetChouma(rp.getBetChouma() + rp.getNowcounma());
            rp.setAnteCount(rp.getBetChouma());
            rp.setNowcounma(0);
            rp.setStatus(3);
            rp.setClientStatus(4);
            if (room.getMaxChouma() < rp.getBetChouma()) {
                room.setMaxChouma(rp.getBetChouma());
            }
            room.setLastBetPlayerOp(true);
            room.getDealer().addAllinCnt(rp);
            singleProcess = SingleProcess.builder().role(rp.getRole()).userName(rp.getUserInfo().getNikeName())
                    .action(8).wager(rp.getAnteNumber()).chip(rp.getNowcounma()).build();
        } else {
            rp.setBetChouma(rp.getBetChouma() + room.getDamanzhu());
            rp.setAnteCount(rp.getBetChouma());
            rp.setNowcounma(rp.getNowcounma() - room.getDamanzhu());
            rp.setStatus(2);
            rp.setClientStatus(1);
            if (room.getMaxChouma() < rp.getBetChouma()) {
                room.setMaxChouma(rp.getBetChouma());
            }
            singleProcess = SingleProcess.builder().role(rp.getRole()).userName(rp.getUserInfo().getNikeName())
                    .action(8).wager(room.getDamanzhu()).chip(rp.getNowcounma()).build();
        }

        room.setPoolChip(room.getPoolChip() + room.getDamanzhu());
        logger.debug("大盲的保存, 底池={}", room.getPoolChip());
        //大盲的保存
        historyDao.insertSingleGameProcess(room.getRoomId(), room.getStage(), 1, singleProcess);


        if (singleProcessStraddle != null) {
            historyDao.insertSingleGameProcess(room.getRoomId(), room.getStage(), 1, singleProcessStraddle);
        }
        rp = room.getRoomPersions()[room.getLastBetPlayer()];
        if (rp == null || rp.getStatus() == 3) {
            room.setLastBetPlayerOp(true);
        } else {
            room.setLastBetPlayerOp(false);
        }
    }

    /**
     * 往后推一位 下注 返回玩家ID
     */
    public int nextCurrentNumber2() {
        if (room.getCurrentNumber() < 0) {
            room.setCurrentNumber(room.damanzhuNumber);
        }
        int CurrentNumber = room.getCurrentNumber();
        RoomPersion roomPersion = null;
        for (int i3 = 0; i3 < room.getRoomPersions().length; i3++) {
            CurrentNumber = (CurrentNumber + 1) % room.getPlayerCount();
            roomPersion = room.getRoomPersions()[CurrentNumber];
            if (roomPersion != null && roomPersion.getStatus() != 3
                    && roomPersion.getStatus() != -2
                    && roomPersion.getStatus() != -3
                    && roomPersion.getOnlinerType() == 1
            ) {
                room.setCurrentNumber(CurrentNumber);
                return roomPersion.getUserId();
            } else {
            }
        }
        return -1;
    }

    /**
     * 玩家没有坐下离开
     *
     * @param userId
     * @return
     */
    public synchronized RoomPersion noDownLeave(int userId) {
        RoomPersion person = room.getAudMap().get(userId);

        if (person != null) {
            boolean needNotify = false;
            logger.debug("noDownLeave userid={},seat={}", userId, person.getSize());
            if (person.getSize() > -1) {
                updateUserBasicInfo(person);
            }
            room.addLeaveUser(userId);  //增加到离开的观众集合中
            room.getDealer().setLeftChip(userId, person.getNowcounma());
            room.delRoomAud(userId, RemoveUserCode.NOT_SITDOWN_LEAVE);
            room.getDealer().removeWatcher(person.getUserId());// 离开房间从观察者中干掉

            RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
            if (null != roomPlayer) {
                if (4 == roomPlayer.getSeatStatus() || 3 == roomPlayer.getSeatStatus()) {
                    needNotify = true;
                    logger.debug("noDownLeave the stand up user is occuping seat,userid={},seat={},seatstatus={}", userId, person.getSize(), roomPlayer.getSeatStatus());
                }

                room.getRoomService().cancelOccupySeat(userId, 0);
                if (needNotify) {
                    room.getRoomService().notifyOccupyUserStand(2, userId);
                }

                RabbitMqUtil.leaveChatGroupRoom(room.getRoomId(), userId);
                gameEventService.emitLeaveRoom(room, userId);
                logger.debug("noDownLeave succefully,userid={},roomid={}", userId, room.getRoomId());
            }

            // 更改RoomPlayerList
            String roomPlayerList = processRoomPlayerList();
            logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
            RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);
        }

        return person;
    }

    /**
     * 玩家离开房间包括未坐下离开
     * 更新更新用户基本信息 (最大手牌,最大手牌类型等信息)
     *
     * @param roomPersion
     */
    public UserInfo updateUserBasicInfo(RoomPersion roomPersion) {
        logger.debug("updateUserBasicInfo,isgame={},seat={}", roomPersion.getIsGame(), roomPersion.getSize());
        room.getDealer().setLeftChip(roomPersion.getUserId(), roomPersion.getNowcounma()); // 用户在房间没结束之前退出房间，将用户筹码保存
        if (roomPersion.getIsGame() > 0 || roomPersion.getSize() > -1) { //有玩过游戏

            UserInfo userInfo = roomPersion.getUserInfo();

            try {
                String nut = "";
                if (userInfo.getPocer()[0] != null) {
                    nut = getNut(userInfo.getPocer()[0].getSize1()) + getNut(userInfo.getPocer()[1].getSize1()) + getNut(userInfo.getPocer()[2].getSize1()) +
                            getNut(userInfo.getPocer()[3].getSize1()) + getNut(userInfo.getPocer()[4].getSize1());
                    userInfo.setNutHand(nut);
                }
                userInfo.setNutHandType(userInfo.getPocerType());

                int maxChip = roomPersion.getNowcounma() + roomPersion.getChouma();
                if (maxChip > userInfo.getHighestHave()) {
                    userInfo.setHighestHave(maxChip);
                }

                UserInfoDao userInfoDao = new UserInfoDaoImp();
                userInfoDao.updateUserInfo(userInfo); //更新玩家积分最大手牌等信息
            } catch (SQLException e) {
                logger.error("fail to update user info", e);
            }

            roomPersion.setLastChouma(roomPersion.getBetChouma()); //对于游戏中离开的玩家给用记录

            return userInfo;
        }
        return roomPersion.getUserInfo();

    }

    /**
     * 玩家房间结束或者提前离桌时,更新玩家信息(基本信息和金豆数量)
     *
     * @param userId
     * @param chipsCode 更新类型
     */
    public void updateUserInfo(int userId, ChipsCode chipsCode) {
        RoomPersion roomPersion = room.getAudMap().get(userId);
        if (null != roomPersion) {
            updateUserBasicInfo(roomPersion);  //更新基本信息

            if (roomPersion.getRequestChip() > 0) { //玩家有未计算的带入需要加上
                RoomRequest.bindUserChip(roomPersion, roomPersion.getRequestChip(), room);
                roomPersion.setRequestChip(0);
            }
        }
        updateUserChip(userId, chipsCode); //更新金豆
    }

    /**
     * 更新金豆
     *
     * @param userId
     * @param chipsCode 更新类型
     */
    private void updateUserChip(int userId, ChipsCode chipsCode) {
        ChipUtils.updateChipWithCommission(userId, room, chipsCode);

    }

    private String getNut(int i) {
        if (i < 10) {
            return "0" + i;
        }
        return String.valueOf(i);
    }

    private int countRoomPerson(Room room) {
        int count = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            if (room.getRoomPersions()[i] != null) {
                count++;
            }
        }
        return count;
    }

    /**
     * 重新开始一局
     */
    public synchronized void nextGame() {
        logger.info("[R-{}] --------------------重新开始一局-----------------", room.getRoomId());
        room.setFirstHandChip(0);
        setBringInRatio(room);
        int countRoomPerson1 = countRoomPerson(room);

        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null && rp.getOnlinerType() < 0) {
                RoomPersion rp1 = room.getAudMap().get(rp.getUserId());
                if (rp1 != null && rp1.getOnlinerType() < 0) {
                    likai2(rp, LeaveRoomCode.TIMEDOUT_LEAVE);
                }
                room.getRoomPersions()[i] = null;
                rp = null;
            }
            if (rp != null && rp.getStatus() == -2 && rp.getZdlk() == -1) {  //清理掉线玩家
                zhanqi2(rp, StandUpRoomCode.NEXT_GAME_OFFLINE_STANDUP);
            }

            boolean needOccupySeat = false;
            if (rp != null) {
                RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
                // 检查用户是否有足够的筹码，筹码不够站起
                boolean checkUserNowCouMa = rp.getNowcounma() + rp.getRequestChip() < room.getDamanzhu() + room.getQianzhu() && rp.getType() != 6;
                logger.debug("nextGame, userId=" + rp.getUserId() + " checkUserNowCouMa=" + checkUserNowCouMa + " nowcounma=" + rp.getNowcounma() + " requestChip=" + rp.getRequestChip() + " minChip=" + room.getMinChip() + " type=" + rp.getType());
                if (checkUserNowCouMa) {
                    if (rp.isAutoOp()) {// 当前玩家在托管模式
                        logger.debug("this user is on auto op, do not occupy seat!!");
                        zhanqi2(rp, StandUpRoomCode.NEXT_GAME_NOT_ENOUGH_CHIP_STANDUP);
                    } else { // 自动进入离桌留座状态
                        logger.debug("nextGame, userId=" + rp.getUserId() + " need occupy seat auto");
                        needOccupySeat = true;
                    }
                }

                if (needOccupySeat) { // 下一局开局时，(非托管玩家输光筹码)玩家自动进入离桌留座状态
                    logger.debug("nextGame, userId=" + rp.getUserId() + " seatStatus=" + roomPlayer.getSeatStatus() + " on seat=" + rp.getSize());
                    standAndOccupySeat(rp);
                }

                if (rp.isKickOut()) {
                    room.addLeaveUser(rp.getUserId());  //增加到离开的观众集合中
                    zhanqi2(rp, StandUpRoomCode.NEXT_GAME_FORCE_OUT_STANDUP);
                    likai2(rp, LeaveRoomCode.KICK_OUT_LEAVE);
                } else if (rp.isStandUp()) {
                    zhanqi2(rp, StandUpRoomCode.NEXT_GAME_FORCE_OUT_STANDUP);
                } else if (1 == roomPlayer.getAheadLeave()) {
                    if (rp.getRequestChip() > 0) {        // 上局有加筹码请求
                        RoomRequest.bindUserChip(rp, rp.getRequestChip(), room);
                        rp.setRequestChip(0);    // 上局请求清0
                    }
                    zhanqi2(rp, StandUpRoomCode.NEXT_GAME_AHEAD_LEAVE_STANDUP);
                }

                if (rp.getDelUserInfo() == 1) {
                    room.delRoomAud(rp.getUserId(), RemoveUserCode.NOT_SITDOWN_LEAVE); //从房间中删除该玩家
                }
            }
            rp = room.getDdRoomPersions()[i];
            if (rp != null && rp.getOnlinerType() < 0 && rp.isCanPlay()) {
                room.getRoomPersions()[i] = null;
            }

        }

        int countRoomPerson2 = countRoomPerson(room);
        boolean statusOfAutoMode = AiRoomManager.getStatusOfAutoMode(room.getRoomId());
        if (statusOfAutoMode && Constant.ROOM_PATH_NORMAL == room.getRoomPath() && countRoomPerson1 != countRoomPerson2) {
            logger.debug("[R-{}] 坐下人數變化了 ({} -> {}), 再派遣ai", room.getRoomId(), countRoomPerson1, countRoomPerson2);
            AiDispatch.disPatchAi(room);
        }
        if (!statusOfAutoMode && !room.isStopDispatchAutoti() && Constant.ROOM_PATH_NORMAL == room.getRoomPath()) { //如果派遣开关关了且未执行过终止派遣任务，需要执行终止派遣任务
            AiDispatch.stopDispatch(room);
        }

        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null) {
                rp.setType(4);//坐下等待开始,解除玩家游戏状态
                if (rp.getPassHand() < 2) {
                    rp.setPassHand(rp.getPassHand() + 1);
                    rp.setPreStage(room.getStage());
                }
                //logger.debug("nextGame roomId:" + room.getRoomId() + " userId=" + rp.getUserId() + " getRequestChip=" + rp.getRequestChip());
                if (rp.getRequestChip() > 0) {        // 上局有加筹码请求
                    RoomRequest.bindUserChip(rp, rp.getRequestChip(), room);
                    rp.setRequestChip(0);    // 上局请求清0
                }

                if (AiRuleTemplate.isAiUser(rp.getUserId())) { //判断ai状态,是否需要补充带入和站起
                    AiHelper.checkStatus(room, rp.getUserId());
                }
            }
        }

        if (AiRoomManager.getStatusOfAutoMode(room.getRoomId()) && Constant.ROOM_PATH_NORMAL == room.getRoomPath()) {
            AiAddChips.checkOccupySeatAiNeedAddChip(room); //检查留座离桌的ai是否需要补充带入
        }
        //if (Constant.ROOM_PATH_NORMAL == room.getRoomPath()) {
        //   AiMtDisPatch.dispatchMtPlayer(room); //检查是否需要派遣mt玩家
        //}

        if (null != room.getReaWalletService() && room.getActivityConfig().isSkyRedWallet()) { //检查牌桌上的玩家是否满足红包资格
            room.getReaWalletService().updateAllUser();
        }

        room.reset();
        room.nextGame();
    }

    private void setBringInRatio(Room room) {
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null) {
                int userId = rp.getUserId();
                RoomPersion aiRoomPerson = room.getAudMap().get(userId);
                if(aiRoomPerson == null){//已经离开房间，直接返回(主动离开房间或者被管理员踢出房间)
                    return;
                }

                RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
                AiPlayer aiPlayer = roomPlayer.getAiPlayer();
                int needBringRate = AiRoomManager.randomBringinRatio(EAiType.fromValue(aiPlayer.getAiOpreateType()),
                        EBringinType.bringin, userId, room.getRoomId(), aiPlayer.getEAiMode()); //获取随机的带入倍数
                aiPlayer.setNeedBringRate(needBringRate);
                logger.debug("【设置次带入的倍率】【成功获取带入倍率】room id={},user id = {} needBringRate={}",room.getRoomId(),userId,needBringRate);
            }

        }


    }


    /**
     * 坐下等待态－>打牌态
     *
     * @param reset  是否重置玩家能否打牌状态 true:使用status指定状态重置 false: 不重置，使用玩家坐下时的打牌状态
     * @param status 是否可以打牌 true:可以（房间第一手) false:不可以（半路进来）
     */
    public void wait2Play(boolean reset, boolean status) {
        boolean needToDispacthAI=false;
        //落实等待的人
        for (int i = 0; i < room.getDdRoomPersions().length; i++) {
            RoomPersion rp = room.getDdRoomPersions()[i];
            if (rp != null) {
                if (reset) {
                    rp.setCanPlay(status);
                }
                if (rp.getPreStage() < room.getStage()) {
                    //rp.setPassHand(rp.getPassHand() + 1);
                    rp.setPreStage(room.getStage());
                }
                room.getRoomPersions()[i] = rp;
                room.getDdRoomPersions()[i] = null;
                // 真人坐下需要派AI
                if (!needToDispacthAI && !AiRuleTemplate.isAiUser(rp.getUserId())) {
                    needToDispacthAI = true;
                    // ISS-168 真人坐下时触发AI派遣任务；
                    // @FIXME 这样做比较直接粗暴, AI的功能污染了房间服务,nextGame方法也这样用了，那也就这样了。
                    logger.info("[R-{}][U-{}:{}] 真人坐下时触发AI派遣", room.getRoomId(), rp.getUserId(), rp.getUserInfo().getNikeName());
                    AiDispatch.disPatchAi(room);
                }
            }
        }
    }

    /**
     * 只有一个有效玩家通知结果
     *
     * @param winRP
     * @param oneWinInsureFee(一个人赢 本手投保未中需要支付的保险费用总额)
     * @return
     */
    private int oneWinNotice(RoomPersion winRP, int oneWinInsureFee) {
        logger.debug("pool chip={},person bet chouma={},person last chouma={},person chip={}", room.getPoolChip(), winRP.getBetChouma(), winRP.getLastChouma(), winRP.getNowcounma());

        final int winCount = 1;
        Integer[] iswin = new Integer[winCount];
        Integer[] seatIdArray = new Integer[winCount];
        Integer[] playerIdArray = new Integer[winCount];
        Integer[] chipsArray = new Integer[winCount];
        Integer[] cardsTypeArray = new Integer[winCount];
        Integer[] winChipsArray = new Integer[winCount];
        Integer[] cardsSort = {-1, -1, -1, -1, -1};
        Integer[] newChipsArray = new Integer[room.getPlayerCount()];
        Integer[] allIds = new Integer[room.getPlayerCount()];
        Integer[] firstCardArray = new Integer[room.getPlayerCount()];
        Integer[] secondCardArray = new Integer[room.getPlayerCount()];
        Integer[] ismaxcardArray = new Integer[room.getPlayerCount()];
        Integer[] jackPotScoreArray = new Integer[winCount];
        Integer[] jackPotBetScoreArray = new Integer[winCount];
        iswin[0] = 0;
        chipsArray[0] = room.getPoolChip() - oneWinInsureFee;
        playerIdArray[0] = winRP.getUserId();
        cardsTypeArray[0] = 0;

        IJackpotService jpService = room.getJackpotService();
//        int winRPBetJp = null == jpService?0:jpService.getCurrentJpBet(winRP.getUserId());

        winChipsArray[0] = winRP.getYq();

        try {
            room.getRoomRecord().setGameResult(room, false, playerIdArray);// 记录游戏结果数据
            // 统计数据
            int roomStatus = 3;
            if (room.getPocer()[4] != null) {
                roomStatus = 6;
            } else if (room.getPocer()[3] != null) {
                roomStatus = 5;
            } else if (room.getPocer()[2] != null) {
                roomStatus = 4;
            }
            logger.debug("one win status:" + roomStatus);
            room.getDealer().addWinCountData(winRP.getUserId(), roomStatus);
            room.getDealer().addWinData(room.getRoomId(), winRP);

        } catch (Exception e) {
            logger.error("set game result error", e);
        }

        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];

            if (rp != null && rp.getOnlinerType() > 0) {
                if (winRP == rp) {
                    seatIdArray[0] = i;
                    jackPotScoreArray[0] = null == jpService ? 0 : jpService.getCurrentJpBet(rp.getUserId());
                    jackPotBetScoreArray[0] = null == jpService ? 0 : jpService.getCurrentJpReward(rp.getUserId());
                }
                newChipsArray[i] = rp.getNowcounma();
                allIds[i] = rp.getUserId();


                ismaxcardArray[i] = -1;
            } else {
                newChipsArray[i] = -1;
                allIds[i] = -1;
                ismaxcardArray[i] = -1;
            }
        }

        Arrays.fill(firstCardArray, -1);
        Arrays.fill(secondCardArray, -1);
        byte[] bytes;
        Set<Integer> noticedUser = new HashSet<Integer>();
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null && rp.getOnlinerType() > 0) {
                firstCardArray[i] = -1;
                secondCardArray[i] = -1;
                if (rp.getShowCardsId() == 1) {
                    firstCardArray[i] = rp.getPocers()[0].getSize1();
                    secondCardArray[i] = -1;
                } else if (rp.getShowCardsId() == 2) {
                    firstCardArray[i] = -1;
                    secondCardArray[i] = rp.getPocers()[1].getSize1();
                } else if (rp.getShowCardsId() == 3) {
                    firstCardArray[i] = rp.getPocers()[0].getSize1();
                    secondCardArray[i] = rp.getPocers()[1].getSize1();
                }
            }
        }

        // 只有一人赢筹码 盖牌的默认补0给客户端
        Integer[] muckIdsArr = new Integer[room.getPlayerCount()];
        for (int i = 0; i < room.getPlayerCount(); i++) {
            muckIdsArr[i] = 0;
        }
        logger.debug("muckIdsArr:" + Arrays.toString(muckIdsArr));
        logger.debug("jackPotBetScoreArray:" + Arrays.toString(jackPotBetScoreArray));
        logger.debug("jackPotScoreArray:" + Arrays.toString(jackPotScoreArray));
        logger.debug("firstCardArray:" + Arrays.toString(firstCardArray));
        logger.debug("secondCardArray:" + Arrays.toString(secondCardArray));

        gameEventService.emitBipaiResult(room, playerIdArray, iswin);

        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null && rp.getOnlinerType() > 0) {
                int firstCard = firstCardArray[i];
                int secondCard = secondCardArray[i];
                firstCardArray[i] = rp.getPocers()[0].getSize1();
                secondCardArray[i] = rp.getPocers()[1].getSize1();
                //logger.debug("firstCardArray:" + Arrays.toString(firstCardArray));
                //logger.debug("secondCardArray:" + Arrays.toString(secondCardArray));
                Object[][] objs = {
                    {130, seatIdArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},        // 收筹码玩家座位号
                    {131, playerIdArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},      // 收筹码玩家id
                    {132, chipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},         // 收筹码数
                    {133, cardsTypeArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 赢牌类型 0无 1-10正常
                    {134, iswin, I366ClientPickUtil.TYPE_INT_1_ARRAY},              // 收筹码玩家是否是赢家 0是 1否
                    {135, winChipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},      // 赢家赢的筹码数
                    {136, cardsSort, I366ClientPickUtil.TYPE_INT_1_ARRAY},          // 哪几张牌赢的 系统牌0-4 底牌5-6
                    {137, newChipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},      // 每个玩家最后显示筹码
                    {138, allIds, I366ClientPickUtil.TYPE_INT_4_ARRAY},             // 每个玩家id
                    {140, firstCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 玩家第一张牌 有牌 0-51 无牌－1
                    {141, secondCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},    // 玩家第二张牌 有牌 0-51 无牌－1
                    {142, ismaxcardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 是否是最大手牌 0是 1不是
                    {143, rp.getNowcounma() + rp.getRequestChip(), I366ClientPickUtil.TYPE_INT_4},   // 玩家下局筹码
                    {144, muckIdsArr, I366ClientPickUtil.TYPE_INT_1_ARRAY},      // 盖牌列表 1盖牌 0非盖牌
                    {147, jackPotScoreArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                    {148, jackPotBetScoreArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                };
                bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_WINNER);
                PublisherUtil.sendByUserId(room, bytes, rp.getUserId());        // 通知赢牌玩家
                noticedUser.add(rp.getUserId());
                firstCardArray[i] = firstCard;
                secondCardArray[i] = secondCard;
            }
        }

        //logger.debug("jackPotBetScoreArray:" + Arrays.toString(jackPotBetScoreArray));
        //logger.debug("jackPotScoreArray:" + Arrays.toString(jackPotScoreArray));

        for (Integer key : room.getAudMap().keySet()) {
            if (room.getAudMap().get(key) != null) {
                RoomPersion rp = room.getAudMap().get(key);
                if (rp.getOnlinerType() > 0 && !noticedUser.contains(rp.getUserId())) {
                    Object[][] objs1 = {
                        {130, seatIdArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},        // 收筹码玩家座位号
                        {131, playerIdArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},        // 收筹码玩家id
                        {132, chipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},            // 收筹码数
                        {133, cardsTypeArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},        // 赢牌类型 0无 1-10正常
                        {134, iswin, I366ClientPickUtil.TYPE_INT_1_ARRAY},                // 收筹码玩家是否是赢家 0是 1否
                        {135, winChipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},        // 赢家赢的筹码数
                        {136, cardsSort, I366ClientPickUtil.TYPE_INT_1_ARRAY},            // 哪几张牌赢的 系统牌0-4 底牌5-6
                        {137, newChipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},        // 每个玩家最后显示筹码
                        {138, allIds, I366ClientPickUtil.TYPE_INT_4_ARRAY},                // 每个玩家id
                        {140, firstCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},        // 玩家第一张牌 有牌 0-51 无牌－1
                        {141, secondCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},    // 玩家第二张牌 有牌 0-51 无牌－1
                        {142, ismaxcardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},        // 是否是最大手牌 0是 1不是
                        {143, rp.getNowcounma() + rp.getRequestChip(), I366ClientPickUtil.TYPE_INT_4},   // 玩家下局筹码
                        {144, muckIdsArr, I366ClientPickUtil.TYPE_INT_1_ARRAY},      // 盖牌列表 1盖牌 0非盖牌
                        {147, jackPotScoreArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                        {148, jackPotBetScoreArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                    };
                    bytes = I366ClientPickUtil.packAll(objs1, Constant.REQ_GAME_RECV_WINNER);
                    PublisherUtil.sendByUserId(room, bytes, rp.getUserId());
                }
            }
        }
        noticedUser.clear();

        if (room.isCardControl()) {  //记录控制发牌赢牌的玩家信息
            logger.debug("need to record cardcontrol info, roomid: " + room.getRoomId());
            room.getPocerLink().getPokerControl().recordControlInfoToMongo(playerIdArray, winChipsArray, jackPotBetScoreArray);
        }
        return 1;
    }


    /**
     * 通知比牌结果
     */
    private int bipaijieguo(RoomPersion[][] rpss) {

        // 添加不用加注直接比赛的数据
        for (int i = 1; i < room.getRoomReplay().getAnteActions().length; i++) {
            AnteAction anteAction = room.getRoomReplay().getAnteActions()[i];
            if (room.getPocer()[i + 1] != null) {
                if (anteAction == null) {
                    anteAction = new AnteAction();
                    anteAction.setPot(room.getPoolChip());
                    String card = "";
                    if (i == 1) {
                        card = room.getPocer()[0].getSize1() + " " + room.getPocer()[1].getSize1() + " ";
                    }
                    card += room.getPocer()[i + 1].getSize1();
                    anteAction.setCard(card);
                    room.getRoomReplay().setAnteAction(i, anteAction);
                }
            } else {
                break;
            }
        }

        Integer[] iswin = new Integer[room.getPlayerCount()];
        Integer[] seatIdArray = new Integer[room.getPlayerCount()];
        Integer[] playerIdArray = new Integer[room.getPlayerCount()];
        Integer[] chipsArray = new Integer[room.getPlayerCount()];
        Integer[] cardsTypeArray = new Integer[room.getPlayerCount()];
        Integer[] cardsSort = new Integer[room.getPlayerCount() * 7];
        Integer[] newChipsArray = new Integer[room.getPlayerCount()];
        Integer[] allIds = new Integer[room.getPlayerCount()];
        Integer[] winChipsArray = new Integer[room.getPlayerCount()];

        Integer[] firstCardArray = new Integer[room.getPlayerCount()];
        Integer[] secondCardArray = new Integer[room.getPlayerCount()];
        Integer[] ismaxcardArray = new Integer[room.getPlayerCount()];
        Integer[] jackPotScoreArray = new Integer[room.getPlayerCount()];
        Integer[] jackPotBetScoreArray = new Integer[room.getPlayerCount()];
        int k1 = 0;
        int k2 = 0;
        int maxPocerType = 11;
        int level = -1;
        for (int i1 = 0; i1 < rpss.length; i1++) {
            RoomPersion[] rps = rpss[i1];

            //玩家要拿回去的筹码...不一定是赢的玩家
            IJackpotService jpService = room.getJackpotService();
            if (rps != null && rps.length > 0 && rps[0] != null) {//&& (rps[0].getLastChouma() + rps[0].getYq()) > 0) {
                for (int j1 = 0; j1 < rps.length; j1++) {
                    RoomPersion rp = rps[j1];
// if (rp != null && rp.getOnlinerType() == 1 && room.getAudMap().get(rp.getUserId()) != null) {
                    if (rp != null) {
                        if (rp.getBetChouma() + rp.getYq() > 0) {
                            // 赢牌人数为最大牌人数
                            if (maxPocerType == 11) {
                                maxPocerType = rp.getPocerType();
                                level = i1;
                            }
                            if (level == i1 && maxPocerType == rp.getPocerType() && (rp.getYq() >= 0
                                    || room.getWinWithoutInsuranceUserIds().contains(rp.getUserId()))) {
                                iswin[k1] = 0;
                                seatIdArray[k1] = rp.getSize();
                                playerIdArray[k1] = rp.getUserId();
                                chipsArray[k1] = rp.getYq() + rp.getBetChouma();
                                cardsTypeArray[k1] = rp.getPocerType();
                                winChipsArray[k1] = rp.getYq();
                                jackPotScoreArray[k1] = null == jpService ? 0 : jpService.getCurrentJpBet(rp.getUserId());
                                jackPotBetScoreArray[k1] = null == jpService ? 0 : jpService.getCurrentJpReward(rp.getUserId());
                                room.getDealer().addWinData(room.getRoomId(), rp);
                                k1++;
                            }
                            // 寻找最大的牌位置
                            int totalPocers = 5;
                            Set<Integer> maxCard = new HashSet<Integer>();
                            for (int j = 0; j < rp.getZuidaPocers().length; j++) {
                                maxCard.add(rp.getZuidaPocers()[j].getSize1());
                            }
                            for (int n = 0; n < room.getPocer().length; n++) {
                                Pocer pocer1 = room.getPocer()[n];
                                if (maxCard.contains(pocer1.getSize1())) {
                                    cardsSort[k2] = n;
                                    k2++;
                                    totalPocers--;
                                    maxCard.remove(pocer1.getSize1());
                                }
                            }
                            if (totalPocers > 0) {
                                for (int n = 5; n < 7; n++) {
                                    Pocer pocer1 = rp.getPocers()[n - 5];
                                    if (maxCard.contains(pocer1.getSize1())) {
                                        cardsSort[k2] = n;
                                        k2++;
                                        totalPocers--;
                                        maxCard.remove(pocer1.getSize1());
                                    }
                                    if (totalPocers == 0) {
                                        break;
                                    }
                                }
                            }
                        }
                        // 输钱的人
                        room.getDealer().addAllinLoseCnt(rp);
                        // 摊牌赢的钱(胜负都计入)
                        room.getDealer().addTanpaiEarn(rp);
                        // 记录赢牌数据
                        if (rp.getYq() > 0) {
                            room.getDealer().addWinCountData(rp.getUserId(), 7);
                        }
                    } else {
                        break;
                    }
                }
            } else {
                break;
            }
        }

        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null && rp.getOnlinerType() > 0) {

                newChipsArray[i] = rp.getNowcounma();
                allIds[i] = rp.getUserId();
                //玩家第一,二 张牌 是否取得最大赢取
                if (rp.getStatus() == -3) { // 弃牌玩家
                    firstCardArray[i] = -1;
                    secondCardArray[i] = -1;
                    //logger.debug("==== getShowCardsId=" + rp.getShowCardsId());
                    if (rp.getShowCardsId() == 1) {
                        firstCardArray[i] = rp.getPocers()[0].getSize1();
                    } else if (rp.getShowCardsId() == 2) {
                        secondCardArray[i] = rp.getPocers()[1].getSize1();
                    } else if (rp.getShowCardsId() == 3) {
                        firstCardArray[i] = rp.getPocers()[0].getSize1();
                        secondCardArray[i] = rp.getPocers()[1].getSize1();
                    }
                } else {
                    firstCardArray[i] = rp.getPocers()[0].getSize1();
                    secondCardArray[i] = rp.getPocers()[1].getSize1();
                }
                if (rp.getUserInfo().getIsBiggestwinRate() == 1) {
                    ismaxcardArray[i] = 0;
                } else {
                    ismaxcardArray[i] = 1;
                }
            } else {
                newChipsArray[i] = -1;
                allIds[i] = -1;
                //玩家第一,2 张牌 是否取得最大赢取
                firstCardArray[i] = -1;
                secondCardArray[i] = -1;
                ismaxcardArray[i] = -1;
            }
//logger.debug("=====firstCardArray: " + firstCardArray[i] + " secondCardArray=" + secondCardArray[i]);
        }

        room.setBigCardNum(0);
        // 大牌
        if (maxPocerType <= 4) {
            room.setBigCardNum(k1);
        }

        if (k1 == 0) {
            logger.error("not final ..");
            return 0;
        }

 //logger.debug("jackPotBetScoreArray: " + Arrays.toString(jackPotBetScoreArray));
        //logger.debug("jackPotScoreArray: " +  Arrays.toString(jackPotScoreArray));
        Integer[] seatIdArray_ = new Integer[k1];
        Integer[] playerIdArray_ = new Integer[k1];
        Integer[] chipsArray_ = new Integer[k1];
        Integer[] cardsTypeArray_ = new Integer[k1];
        Integer[] iswin_ = new Integer[k1];
        Integer[] winChipsArray_ = new Integer[k1];
        Integer[] betJackPotArray_ = new Integer[k1];//击中奖励
        Integer[] winRewardArray_ = new Integer[k1];//jackpot投入
        Integer[] cardsSort_ = new Integer[k2];

        System.arraycopy(seatIdArray, 0, seatIdArray_, 0, k1);
        System.arraycopy(playerIdArray, 0, playerIdArray_, 0, k1);
        System.arraycopy(chipsArray, 0, chipsArray_, 0, k1);
        System.arraycopy(cardsTypeArray, 0, cardsTypeArray_, 0, k1);
        System.arraycopy(iswin, 0, iswin_, 0, k1);
        System.arraycopy(winChipsArray, 0, winChipsArray_, 0, k1);
        System.arraycopy(cardsSort, 0, cardsSort_, 0, k2);
        System.arraycopy(jackPotBetScoreArray, 0, betJackPotArray_, 0, k1);
        System.arraycopy(jackPotScoreArray, 0, winRewardArray_, 0, k1);

        // 盖牌用户手牌不能下发
        Integer[] muckIdArr = Muck.getMuckSeatIds(room);

        try {
            room.getRoomRecord().setGameResult(room, true, playerIdArray_);// 记录游戏结果数据
        } catch (Exception e) {
            logger.error("set game result error", e);
        }

        // 2017.4.26新增需求：客户端增加一个盖牌开关。0 关闭盖牌开关，则全部亮牌；1 打开盖牌开关，走之前的逻辑。（默认）
        if (room.getMuckSwitch() == 1) {
            // 盖牌用户手牌不能下发
            for (int i = 0; i < room.getPlayerCount(); i++) {
                if (muckIdArr[i] == 1) {
                    RoomPersion roomPersion = room.getRoomPersions()[i];
                    if (null == roomPersion) {//没有对应玩家可以返回-1
                        firstCardArray[i] = -1;
                        secondCardArray[i] = -1;
                    } else {
                        if (roomPersion.getShowCardsId() == 0) {//不亮牌
                            firstCardArray[i] = -1;
                            secondCardArray[i] = -1;
                        } else if (roomPersion.getShowCardsId() == 1) {//亮第一张牌
                            secondCardArray[i] = -1;
                        } else if (roomPersion.getShowCardsId() == 2) {//亮第二张牌
                            firstCardArray[i] = -1;
                        }
                    }
                }
            }
        }

        StringBuilder cardsTypeStr = new StringBuilder();
        StringBuilder cardsSortStr = new StringBuilder();
        StringBuilder chipsArrayStr = new StringBuilder();
        StringBuilder newChipsArrayStr = new StringBuilder();
        StringBuilder winChipsArrayStr = new StringBuilder();
        StringBuilder firstCardsArrayStr = new StringBuilder();
        StringBuilder secondCardsArrayStr = new StringBuilder();
        for (int i = 0; i < k1; i++) {
            cardsTypeStr.append(" " + cardsTypeArray_[i]);
            chipsArrayStr.append(" " + chipsArray_[i]);
            newChipsArrayStr.append(" " + newChipsArray[i]);
            winChipsArrayStr.append(" " + winChipsArray[i]);
        }
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            firstCardsArrayStr.append(" " + firstCardArray[i]);
            secondCardsArrayStr.append(" " + secondCardArray[i]);
        }
        for (int i = 0; i < k2; i++) {
            cardsSortStr.append(" " + cardsSort_[i]);
        }

        logger.debug("cards type: " + cardsTypeStr.toString());
        logger.debug("chips array: " + chipsArrayStr.toString());
        logger.debug("new chips: " + newChipsArrayStr.toString());
        logger.debug("win chips: " + winChipsArrayStr.toString());
        logger.debug("cards sort: " + cardsSortStr.toString());
        logger.debug("first cards: " + firstCardsArrayStr.toString());
        logger.debug("second cards: " + secondCardsArrayStr.toString());
        logger.debug("muck seats:" + Arrays.toString(muckIdArr));
        logger.debug("win chips array:" + Arrays.toString(winChipsArray_));
        logger.debug("seat id array:" + Arrays.toString(seatIdArray_));
        logger.debug("playerid  array:" + Arrays.toString(playerIdArray_));

        logger.debug("jackPotBetScoreArray:" + Arrays.toString(betJackPotArray_));
        logger.debug("jackPotScoreArray:" + Arrays.toString(winRewardArray_));

        gameEventService.emitBipaiResult(room, playerIdArray_, iswin_);

        for (Integer key : room.getAudMap().keySet()) {
            if (room.getAudMap().get(key) != null) {
                RoomPersion rp = room.getAudMap().get(key);
                Object[][] objs = {
                    {130, seatIdArray_, I366ClientPickUtil.TYPE_INT_1_ARRAY},       // 收筹码玩家座位号
                    {131, playerIdArray_, I366ClientPickUtil.TYPE_INT_4_ARRAY},     // 收筹码玩家id
                    {132, chipsArray_, I366ClientPickUtil.TYPE_INT_4_ARRAY},        // 收筹码数
                    {133, cardsTypeArray_, I366ClientPickUtil.TYPE_INT_1_ARRAY},    // 赢牌类型 0无 1-10正常
                    {134, iswin_, I366ClientPickUtil.TYPE_INT_1_ARRAY},             // 收筹码玩家是否是赢家 0是 1否
                    {135, winChipsArray_, I366ClientPickUtil.TYPE_INT_4_ARRAY},     // 赢家赢的筹码数
                    {136, cardsSort_, I366ClientPickUtil.TYPE_INT_1_ARRAY},         // 哪几张牌赢的 系统牌0-4 底牌5-6
                    {137, newChipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},      // 每个玩家最后显示筹码
                    {138, allIds, I366ClientPickUtil.TYPE_INT_4_ARRAY},             // 每个玩家id
                    {140, firstCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 玩家第一张牌 有牌 0-51 无牌-1
                    {141, secondCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},    // 玩家第二张牌 有牌 0-51 无牌-1
                    {142, ismaxcardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},     // 是否是最大手牌 0是 1不是
                    {143, rp.getNowcounma() + rp.getRequestChip(), I366ClientPickUtil.TYPE_INT_4},   // 玩家下局筹码
                    {144, muckIdArr, I366ClientPickUtil.TYPE_INT_1_ARRAY},           // 盖牌座位号
                    {147, winRewardArray_, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                    {148, betJackPotArray_, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_WINNER);
                PublisherUtil.sendByUserId(room, bytes, rp.getUserId());
            }
        }

        if (room.isCardControl()) {//记录控制发牌赢牌的玩家信息
            logger.debug("need to record cardcontrol info, roomid: " + room.getRoomId());
            room.getPocerLink().getPokerControl().recordControlInfoToMongo(playerIdArray_, winChipsArray_, betJackPotArray_);
        }

        return k1;
    }

    /**
     * 池底分配算法
     *
     * @param betChipsArray [[userId, betChip], [userId, betChip], ...]
     * @return
     */
    public ArrayList<PoolChip> allotPoolChips(ArrayList<Integer[]> betChipsArray) {
        ArrayList<PoolChip> poolList = new ArrayList<PoolChip>();
        int preChip = 0;

        for (int i = 0; i < betChipsArray.size(); i++) {
            // sort betChipsArray by betChip in ascending order
            int minIndex = i;
            int minBetChip = betChipsArray.get(i)[1];
            for (int j = i + 1; j < betChipsArray.size(); j++) {
                int betChip = betChipsArray.get(j)[1];
                if (betChip < minBetChip) {
                    minBetChip = betChip;
                    minIndex = j;
                }
            }
            if (minIndex != i) {
                Integer[] tmp = betChipsArray.get(i);
                betChipsArray.set(i, betChipsArray.get(minIndex));
                betChipsArray.set(minIndex, tmp);
            }

            Integer[] betChips = betChipsArray.get(i);
            int userId = betChips[0];
            int betChip = betChips[1];
            for (int j = 0; j < poolList.size(); j++) {
                PoolChip subPool = poolList.get(j);
                subPool.getUserIds().add(userId);
                subPool.setTotalChips(subPool.getTotalChips() + subPool.getUnitChips());
                poolList.set(j, subPool);
            }
            if (betChip != preChip) {   // 产生新边池
                PoolChip subPool = new PoolChip();
                subPool.getUserIds().add(userId);          // 边池用户集合
                subPool.setUnitChips(betChip - preChip);    // 边池单位筹码
                subPool.setTotalChips(subPool.getUnitChips());  // 边池总筹码
                poolList.add(subPool);
                preChip = betChip;
            }
        }
        // 人數最多的池為主池
        int maxCount = 0;
        for (int i = 0; i < poolList.size(); i++) {
            int count = poolList.get(i).getUserIds().size();
            if (count > maxCount) {
                poolList.get(i).setUnit(1);
                if (maxCount != 0) {
                    poolList.get(i - 1).setUnit(0);
                }
                maxCount = count;
            }
        }
        printPoolList(poolList);

        return poolList;
    }

    public void printPoolList(ArrayList<PoolChip> poolList) {
        for (int i = 0; i < poolList.size(); i++) {
            PoolChip subPool = poolList.get(i);
            logger.debug("pool unit: " + subPool.getUnitChips() + ", pool total: " + subPool.getTotalChips() +
                    ", pool user size: " + subPool.getUserIds().size());
            logger.debug(" {}", subPool.getUserIds());
        }
    }

    /**
     * @param poolList     池底
     * @param userId       分配筹码玩家
     * @param equalUserIds 同种牌型还未分配玩家数
     * @return
     */
    public WinPoolChip allotChips(ArrayList<PoolChip> poolList, int userId, Set<Integer> equalUserIds, int maxFee) {
        int earn = 0;
        int shui1 = 0;

        Iterator<PoolChip> it = poolList.iterator();
        while (it.hasNext()) {
            PoolChip subPool = it.next();
            // 边池筹码分配完，清空
            if (subPool.getUserIds().isEmpty() || subPool.getTotalChips() == 0) {
                it.remove();
                continue;
            }
            if (subPool.getUserIds().contains(userId)) {
                int playerNum = equalUserIds.size();
                if (playerNum > 1) {
                    Set<Integer> interSection = new HashSet<Integer>(subPool.getUserIds());
                    interSection.retainAll(equalUserIds);
                    playerNum = interSection.size();
                    interSection.clear();
                }
                int earnCurrentSubPool = 0;
//                int shui = 0;
                // 拿回自己部分
                if (equalUserIds.isEmpty()) {
                    earnCurrentSubPool = subPool.getUnitChips();
                } else {
                    if (subPool.getTotalChips() % playerNum == 0) {
                        earnCurrentSubPool = subPool.getTotalChips() / playerNum;
                    } else {
                        earnCurrentSubPool = subPool.getTotalChips() / playerNum + 1;
                    }
                }
                subPool.setTotalChips(subPool.getTotalChips() - earnCurrentSubPool);

//                if (room.getPumpingMode() == Constant.BOTTOM_POOL_PUMP && maxFee > 0 &&
//                        earnCurrentSubPool != subPool.getUnitChips() &&
//                        subPool.getUserIds().size() != playerNum) {
//                    shui = Math.min((int) (earnCurrentSubPool * room.getPump()), maxFee);
//                }
//                shui1 += shui;
                earn += earnCurrentSubPool;
                subPool.getUserIds().remove(userId);
                // 边池筹码分配完，清空
                if (subPool.getUserIds().isEmpty() || subPool.getTotalChips() == 0) {
                    it.remove();
                }
            } else {
                break;
            }
        }
        printPoolList(poolList);
        // 不计保险营收的赢牌用户
        if (earn > 0) {
            room.setWinWithoutInsuranceUserIds(userId);
        }

        // 如果买了保险又没中 扣掉保险的费用移到外围方便记录数据
//        if (room.getInsurance() == 1&&earn>0) {
//            Map<Integer, Integer> insuranceFeeMap = room.getInsurer().getHolderFeeMap();
//            if (insuranceFeeMap.containsKey(userId)) {
//                earn -= insuranceFeeMap.get(userId);
//            }
//        }
        return new WinPoolChip(earn, maxFee, shui1);
    }

    public RoomDao getRoomDao() {
        return roomDao;
    }

    /**
     * 比牌
     *
     * @param lastTurn  最后没有弃牌玩家数
     * @param turnIndex 轮到谁
     * @return
     */
    public synchronized Object[] bipai(int lastTurn, int turnIndex) throws SQLException {
        if (room.getPoolChip() > room.getDealer().getMaxPot()) {// 比牌的时候先记下当前底池有多少（用于算该房间最大的底池）
            room.getDealer().setMaxPot(room.getPoolChip());
        }

        Object[] objs = new Object[10];
        logger.debug("bi pai ...");
        RoomPersion[] rps = room.getRoomPersions();
        RoomPersion[][] rpss = new RoomPersion[room.getPlayerCount()][room.getPlayerCount()]; // 分组集合

        int oneWinInsureFee = 0;
        if (lastTurn == 1) {
            int maxFee = room.getMaxbet();
            int damazhu = 0;

            for (int i = 0; i < rps.length; i++) {
                if (rps[i] != null) {
                    JPPool jpPool = null;
                    int userId1 = rps[i].getUserId();
                    UserInfo userInfo = rps[i].getUserInfo();
                    //保存用户数据
                    ServiceFee serviceFee = new ServiceFee(room.getRoomId(), room.getName(), room.getManzhu(), room.getRoomPath(),
                            room.getStage(), room.getClubRoomType(),
                            room.getVigilante().isHighRisk(userId1) ? 1 : 0,
                            room.getVigilante().isThreatNeutralized() ? 1 : 0,
                            room.getVigilante().isInterveneFailed() ? 1 : 0);
                    serviceFee.setUserId(userId1);
                    serviceFee.setUserName(userInfo.getNikeName());

                    String s = String.valueOf(userId1);
                    serviceFee.setEnding(Integer.parseInt(s.substring(s.length() - 1)));
                    serviceFee.setClubId(userInfo.getClubId());
                    serviceFee.setTribeId(userInfo.getTribeId());

                    int nowChouma = rps[i].getNowcounma() + room.getPoolChip();
                    int earn = -rps[i].getBetChouma();
                    if (i == turnIndex) {
                        // 赢家
                        // 如果买了保险又没中 扣掉保险的费用(一人赢筹码的特殊情况)
                        if (room.getInsurance() == 1) {
                            Map<Integer, Integer> insuranceFeeMap = room.getInsurer().getHolderFeeMap();

                            if (insuranceFeeMap.containsKey(userId1)) {
                                oneWinInsureFee = insuranceFeeMap.get(userId1);
                            }
                        }
                        //公共牌
                        AnteAction[] anteActions = room.getRoomReplay().getAnteActions();
                        //如果没有发公共牌则不收取服务费
//                        int fee = 0;
                        if (anteActions[1] == null) { //0-翻公共牌前 1-翻公共牌 2-转牌 3-河牌
                            earn += room.getPoolChip() - oneWinInsureFee;
                            rps[i].setNowcounma(nowChouma - oneWinInsureFee);
                        } else {
                            earn += room.getPoolChip();
                            rps[i].setNowcounma(nowChouma);
                        }
                        rps[i].setYq(earn);
//                        serviceFee.setFee(fee);

                        if (room.getClubRoomType() == 0 && room.isJackPot() && room.getCurrentPlayerCount() >= 4) {
                            String nickName = "";
                            if (null != rps[i].getUserInfo()) {
                                nickName = rps[i].getUserInfo().getNikeName();
                            }

                            /**
                             * 当手牌盈利数额达到10BB时，抽取一个小盲计入总奖池
                             */
//                            int earn = rps[i].getYq();
                            if (earn >= 18 * room.getDamanzhu() && damazhu == 0) {
                                jpPool = room.getJackpotService().betToJackPot(userId1, nickName, earn, 1);
                                earn = jpPool.getEarn();
                                int commission = jpPool.getContribution();  // 抽佣
                                logger.debug("lastTurn= 1, userid:{}  earn 10BB nickname:{}  roomid:{} ,reduce:{}", rps[i].getUserId(), rps[i].getUserInfo().getNikeName(), room.getRoomId(), commission);
                                damazhu = commission;
                                rps[i].setYq(earn);
                                rps[i].setNowcounma(rps[i].getNowcounma() - commission);
                            }
                            /**
                             * 判断是否击中jackpot
                             * 赢家在所有玩家弃牌前选择亮2张手牌
                             * 手牌和已发出的公共牌组合满足击中牌型
                             */
                            int publicCardNum = 0;
                            for (int m = 0; m < room.getPocer().length; m++) {
                                if (room.getPocer()[m] != null) {
                                    publicCardNum++;
                                }
                            }

                            int showCardFlag = rps[i].getShowCardsId();
                            logger.debug("checkIfJackpotBet : rid={},lastTurn={},uid={},publicCardNum={},showCardFlag={}",
                                    room.getRoomId(), lastTurn, userId1, publicCardNum, showCardFlag);
                            boolean neddHelpShowCard = false;//是否需要系统帮忙亮牌  只有符合牌型的玩家才有该标志位
                            if (showCardFlag != 3) {
                                neddHelpShowCard = true;
                            }

                            if (publicCardNum >= 2) {//公共牌大于等于2  才有可能组成最小的四条牌型
                                // 比牌,计算玩家的牌型和最大牌
                                Pocer[] pocer = new Pocer[publicCardNum + 2];
                                pocer[0] = rps[i].getPocers()[0];
                                pocer[1] = rps[i].getPocers()[1];
                                for (int m = 0; m < publicCardNum; m++) {
                                    if (room.getPocer()[m] != null) {
                                        pocer[m + 2] = room.getPocer()[m];
                                    }
                                }
                                Object[] obj = zuidapai1(pocer);
                                pocer = (Pocer[]) obj[0];
                                rps[i].setZuidaPocers(pocer);
                                rps[i].setPocerType((Integer) obj[1]);

                                /**
                                 * 判断牌型是否正确
                                 */
                                int maxPocerPocers = 0;//公共牌在最大组合的数量
                                for (Pocer maxPocer : pocer) {
                                    if ((rps[i].getPocers()[0].getSize1() == maxPocer.getSize1()) ||
                                            (rps[i].getPocers()[1].getSize1() == maxPocer.getSize1())) {
                                        maxPocerPocers++;
                                    }
                                }

                                logger.debug("maxPocerPocers: " + maxPocerPocers);

                                boolean jackpotBet = false;
                                if (maxPocerPocers == 2) { //只有两张手牌同时使用才符合
                                    if (rps[i].getPocerType() == 1 || rps[i].getPocerType() == 2 ||
                                            rps[i].getPocerType() == 3) {
                                        jackpotBet = true;
                                    }
                                }
                                //盈利且牌型吻合 构成击中
                                if (jackpotBet) {
                                    if (neddHelpShowCard) {
                                        rps[i].setShowCardsId(3);//设置为全部亮牌
                                    }
                                    double v = room.getJackpotService().rewardFromJackPot(userId1, nickName, rps[i].getPocerType(),
                                            rps[i].getZuidaPocers(), room.getPocer());
                                    if (v > 0) {
                                        //computeFeeDistribution(userInfo, (int) v, Constant.JP_FEE, serviceFee);
                                    }
                                }
                            }
                        }

                        serviceFee.setEarn(rps[i].getYq());
                        if (jpPool != null) {
                            serviceFee.setSign(jpPool.getSign());
                            serviceFee.setContribution(jpPool.getContribution());
                        }
                    } else {
                        rps[i].setYq(earn);
                        serviceFee.setEarn(rps[i].getYq());
                    }
                    room.getFeeManager().calculateFee(userInfo.getUserId(), userInfo.getClubId(), earn, 0, 0, serviceFee);
                    logger.trace("bipai: lastTurn={} roomId={} userId={} poolChip={} bet={} pump={} earn={} fee={} maxFee={} roomCharge={}",
                            lastTurn, room.getRoomId(), userId1, room.getPoolChip(), rps[i].getBetChouma(), room.getPump(), serviceFee.getEarn(), serviceFee.getFee(), maxFee, room.getRoomChargeTotal());
                    historyDao.insertFee(serviceFee);
                    historyDao.updateSingleCardScore(SingleCardScore.builder().roomId(room.getRoomId())
                            .brandType(0).userName(userInfo.getNikeName()).profit(rps[i].getYq())
                            .cardeType(rps[i].getYq() > 0 ? 0 : 1).version(room.getStage()).showCard(0)
                            .build());
                    room.addRoomChargeTotal(serviceFee.getFee());
                }
            }
            reduceStandUpUserChip(room.getPoolChip());
        } else if (lastTurn > 1) {
            //算牌
            int size = -1;
            Map<Integer, Boolean> userJpBetMap = new HashMap<>();
            for (int i = 0; i < rps.length; i++) {
                if (rps[i] != null) {
                    Pocer[] pocer = new Pocer[7];
                    pocer[0] = room.getPocer()[0];
                    pocer[1] = room.getPocer()[1];
                    pocer[2] = room.getPocer()[2];
                    pocer[3] = room.getPocer()[3];
                    pocer[4] = room.getPocer()[4];
                    pocer[5] = rps[i].getPocers()[0];
                    pocer[6] = rps[i].getPocers()[1];
                    Object[] obj = zuidapai(pocer);

                    pocer = (Pocer[]) obj[0];

                    rps[i].setZuidaPocers(pocer);
                    rps[i].setPocerType((Integer) obj[1]);
                    rps[i].setLastChouma(rps[i].getBetChouma());
                    rps[i].setYq(0);
                    rps[i].setYs(-1);

                    /**
                     * 判断牌型是否正确
                     */
                    int maxPocerPocers = 0;//公共牌在最大组合的数量
                    for (Pocer maxPocer : pocer) {
                        if ((rps[i].getPocers()[0].getSize1() == maxPocer.getSize1()) || (rps[i].getPocers()[1].getSize1() == maxPocer.getSize1())) {
                            maxPocerPocers++;
                            continue;
                        }
                    }
                    //只有两张手牌同时使用才符合
                    if (maxPocerPocers == 2) {
                        if (rps[i].getPocerType() == 1 || rps[i].getPocerType() == 2 || rps[i].getPocerType() == 3) {
                            userJpBetMap.put(rps[i].getUserId(), true);
                        }
                    }

                    size++;
                }
            }
            RoomPersion[] rps2 = new RoomPersion[size + 1];
            for (RoomPersion roomPersion : rps) {
                if (roomPersion != null) {
                    //更新最大手牌
                    if (roomPersion.getUserInfo().getPocer()[0] == null) {
                        roomPersion.getUserInfo().setPocer(roomPersion.getZuidaPocers());
                        roomPersion.getUserInfo().setPocerType(roomPersion.getPocerType());
                    } else {
                        RoomPersion r = new RoomPersion();
                        r.setZuidaPocers(roomPersion.getUserInfo().getPocer());
                        r.setPocerType(roomPersion.getUserInfo().getPocerType());
                        if (bipai2(roomPersion, r) == 0) {
                            roomPersion.getUserInfo().setPocer(roomPersion.getZuidaPocers());
                            roomPersion.getUserInfo().setPocerType(roomPersion.getPocerType());
                        }
                    }
                    rps2[size] = roomPersion;
                    size--;
                }
            }

            int[] geshu = new int[room.getPlayerCount()];
            for (int i = 0; i < room.getPlayerCount(); i++) {
                geshu[i] = -1;
            }
            //rps2冒泡排序
            //1 对牌大小进行排序 重小到大
            for (int i = 0; i < rps2.length; i++) {
                for (int j = 0; j < rps2.length - i - 1; j++) {
                    if (bipai2(rps2[j], rps2[j + 1]) == 1) {// 小的往上冒,由大到小
                        swap(rps2, j, j + 1);
                    }
                }
            }

            int jackPotUserId = 0;
            if (room.isJackPot()) {
                RoomPersion rp = rps2[0];
                jackPotUserId = rp.getUserId();
                for (int i = 1; i < rps2.length; i++) {
                    if (bipai2(rp, rps2[i]) == -1) {//不进此逻辑代表只有一个人赢得最终比牌
                        jackPotUserId = 0;
                    }
                }
            }

            int g_ = 0;
            //将牌按照 排名组装成二维数组
            for (int i = 0; i < rps2.length - 1; i++) {
                int j = bipai2(rps2[i], rps2[i + 1]);
                if (j == -1) {
                    if (geshu[g_] == -1) {
                        geshu[g_] = 0;
                    }
                    geshu[g_] = geshu[g_] + 1;
                    rpss[g_][geshu[g_] - 1] = rps2[i];
                    rpss[g_][geshu[g_]] = rps2[i + 1];
                } else {
                    if (geshu[g_] == -1) {
                        geshu[g_] = 0;
                    }
                    rpss[g_][geshu[g_]] = rps2[i];

                    g_++;
                    geshu[g_] = 0;
                    rpss[g_][geshu[g_]] = rps2[i + 1];
                }
            }

            if (rps2.length == 1) {
                geshu[0] = 0;
                rpss[0][0] = rps2[0];
            }

            // 池底分配
            ArrayList<Integer[]> betChipsArray = new ArrayList<Integer[]>();
            Set<Integer> uidMap = new HashSet<Integer>();
            // 桌上玩家
            for (int i = 0; i < rps2.length; i++) {
                Integer[] betChips = new Integer[2];
                betChips[0] = rps2[i].getUserId();
                betChips[1] = rps2[i].getBetChouma();
                betChipsArray.add(betChips);
                uidMap.add(betChips[0]);
            }
            // 站起玩家
            Iterator<Integer> it = room.getStandUpUserInfo().keySet().iterator();
            while (it.hasNext()) {
                int userId = it.next();
                StandUpUserInfo standUpUserInfo = room.getStandUpUserInfo().get(userId);
                int leftChip = standUpUserInfo.getLeftChip();
                Integer[] betChips = new Integer[2];
                betChips[0] = userId;
                betChips[1] = leftChip;
                if (!uidMap.contains(betChips[0])) {
                    betChipsArray.add(betChips);
                    uidMap.add(betChips[0]);
                } else {
                    it.remove();
                }
            }
            uidMap.clear();
            ArrayList<PoolChip> poolList = allotPoolChips(betChipsArray);
            int maxFee = room.getMaxbet();
            int damazhu = room.getDamanzhu();
            for (int i = 0; i < rpss.length; i++) {
                if (geshu[i] == -1) {
                    break;
                }
                logger.debug("level " + i + " num: " + (geshu[i] + 1));

                Set<Integer> equalUserIds = new HashSet<Integer>(); // 同大小有筹码分配玩家集合
                for (int j = 0; j <= geshu[i]; j++) {
                    if (rpss[i][j].getBetChouma() <= 0) {
                        continue;
                    }
                    equalUserIds.add(rpss[i][j].getUserId());
                }
                for (int j = 0; j <= geshu[i]; j++) {
                    RoomPersion rp = rpss[i][j];
                    if (rp.getBetChouma() <= 0) {
                        continue;
                    }

                    JPPool jpPool = null;
                    int userId = rp.getUserId();
                    UserInfo userInfo = rp.getUserInfo();
                    ServiceFee serviceFee = new ServiceFee(room.getRoomId(), room.getName(), room.getManzhu(), room.getRoomPath(),
                            room.getStage(), room.getClubRoomType(),
                            room.getVigilante().isHighRisk(userId) ? 1 : 0,
                            room.getVigilante().isThreatNeutralized() ? 1 : 0,
                            room.getVigilante().isInterveneFailed() ? 1 : 0);
                    String nickName = null != rp.getUserInfo() ? rp.getUserInfo().getNikeName() : "";
                    serviceFee.setUserId(userId);
                    serviceFee.setUserName(nickName);
                    serviceFee.setEnding(Integer.parseInt(String.valueOf(userId).substring(String.valueOf(userId).length() - 1)));
                    serviceFee.setClubId(rp.getUserInfo().getClubId());
                    serviceFee.setTribeId(rp.getUserInfo().getTribeId());
                    WinPoolChip winPoolChip = allotChips(poolList, userId, equalUserIds, maxFee);
                    //Player player = room.getDealer().getPlayers().get(userId);
                    int poolChip = winPoolChip.getEarn();
                    int insuranceFee = room.getInsurer().getHolderFeeMap().getOrDefault(userId, 0);

                    //int poolMinusFee = poolChip - fee;
                    int poolMinusFee = poolChip;

                    if (room.isJackPot() && room.getCurrentPlayerCount() >= 4 &&
                            room.getClubRoomType() == 0) {
                        //当手牌盈利数额达到18BB时，抽取一个盲计入总奖池，计算盈利时需要加上保险赔付的盈利
                        if ((poolMinusFee - rp.getBetChouma()) >= (room.getDamanzhu() * 18) && damazhu != 0) {
                            jpPool = room.getJackpotService().betToJackPot(userId, nickName,
                                    poolMinusFee, equalUserIds.size());
                            poolMinusFee = jpPool.getEarn();
                            damazhu = Math.max(damazhu - jpPool.getContribution(), 0);
                            serviceFee.setContribution(jpPool.getContribution());
                            serviceFee.setSign(jpPool.getSign());
                        }
                        boolean jackpotBet = userJpBetMap.getOrDefault(userId, false);
                        //盈利且牌型吻合 构成击中
                        if (jackpotBet && userId == jackPotUserId) {
                            double jpFee = room.getJackpotService().rewardFromJackPot(userId, nickName,
                                    rp.getPocerType(), rp.getZuidaPocers(), room.getPocer());
                            //if (jpFee > 0) {
                            //    computeFeeDistribution(userInfo, (int) jpFee, Constant.JP_FEE, serviceFee);
                            //}
                        }
                    }
                    equalUserIds.remove(userId);

                    int profit;
                    int paiPuProfit = poolMinusFee - rp.getBetChouma();
                    int insuranceExpenditure = -(rp.getInsuranceLast());

                    if (rp.getWinInsuranceLast() > 0) {
                        profit = paiPuProfit + rp.getInsuranceLast();
                    } else {
                        profit = paiPuProfit - insuranceFee;
                    }
                    rp.setNowcounma(rp.getNowcounma() + poolMinusFee - insuranceFee);

                    int insuranceChipOne = 0;
                    int insuranceChipTwo = 0;
                    //保险的支出
                    if (room.getInsurance() == 1 && insuranceFee > 0) {
                        insuranceChipOne = room.getOneInsurer().getOrDefault(userId, 0);
                        insuranceChipTwo = room.getTwoInsurer().getOrDefault(userId, 0);
                    }
                    serviceFee.setInsuranceProfitOne(insuranceChipOne);
                    serviceFee.setInsuranceProfitTwo(insuranceChipTwo);
                    serviceFee.setInsuranceExpenditure(insuranceExpenditure);
                    //serviceFee.setFee(fee);
                    serviceFee.setEarn(profit);
                    rp.setYq(profit);

                    room.getFeeManager().calculateFee(userInfo.getUserId(), userInfo.getClubId(), profit, insuranceFee, rp.getWinInsuranceLast(), serviceFee);
                    logger.trace("bipai: lastTurn={} roomId={} userId={} poolChip={} bet={} pump={} earn={} fee={} maxFee={} roomCharge={} insurance={} winInsurance={} insuranceFee={} level={}",
                            lastTurn, room.getRoomId(), userId, poolChip, rp.getBetChouma(), room.getPump(), profit, 0, maxFee, room.getRoomChargeTotal(), rp.getInsuranceLast(), rp.getWinInsuranceLast(), insuranceFee, i);

                    if (jpPool != null) {
                        serviceFee.setSign(jpPool.getSign());
                        serviceFee.setContribution(jpPool.getContribution());
                    }
                    List<Integer> bigCard = Arrays.stream(rp.getZuidaPocers()).filter(Objects::nonNull)
                            .map(Pocer::getSize1).collect(Collectors.toList());

                    historyDao.insertFee(serviceFee);
                    historyDao.updateSingleCardScore(SingleCardScore.builder().roomId(room.getRoomId())
                            .version(room.getStage()).profit(paiPuProfit).userName(nickName).liangPai(bigCard)
                            .brandType(rp.getClientStatus() == 6 ? 0 : rp.getPocerType()).insurer(rp.getInsuranceLast())
                            .cardeType(rp.getYq() > 0 ? 0 : 1).showCard(rp.getClientStatus() == 6 ? 0 : 1).build());
                    //room.addRoomChargeTotal(fee);
                }
            }
            returnStandUpChip(poolList);// 归还用户未被分配的筹码
        } else {
            // 没有赢家，归还筹码
            for (RoomPersion r1 : room.getAudMap().values()) {
                if (r1 != null && r1.getBetChouma() != 0) {
                    int leftChip = r1.getNowcounma() + r1.getBetChouma();
                    if (r1.getOnlinerType() != -1) {
                        r1.setNowcounma(leftChip);
                    } else {
                        RoomPersion r2 = room.getAudMap().get(r1.getUserId());
                        if (r2 != null) { // 玩家重新进入房间
                            r2.setNowcounma(r2.getNowcounma() + r1.getBetChouma());
                        } else {
                            room.getDealer().setLeftChip(r1.getUserId(), leftChip);
                        }
                    }
                }
            }
            returnStandUpChip();// 归还用户未被分配的筹码
        }

        for (int userId : room.getStandUpUserInfo().keySet()) {
            //离桌时计入牌谱数据
            ServiceFee serviceFee = new ServiceFee(room.getRoomId(), room.getName(), room.getManzhu(), room.getRoomPath(),
                    room.getStage(), room.getClubRoomType(),
                    room.getVigilante().isHighRisk(userId) ? 1 : 0,
                    room.getVigilante().isThreatNeutralized() ? 1 : 0,
                    room.getVigilante().isInterveneFailed() ? 1 : 0);
            StandUpUserInfo standUpUserInfo = room.getStandUpUserInfo().get(userId);
            int winChip = standUpUserInfo.getWinChip();
            String s = String.valueOf(userId);
            serviceFee.setUserId(userId);
            serviceFee.setUserName(room.getRoomPlayers().get(userId).getNickName());
            serviceFee.setEnding(Integer.parseInt(s.substring(s.length() - 1)));
            serviceFee.setEarn(winChip);
            serviceFee.setClubId(room.getDealer().getPlayers().get(userId).getClubId());
            serviceFee.setTribeId(room.getDealer().getPlayers().get(userId).getTribeId());
            room.getFeeManager().calculateFee(userId, standUpUserInfo.getClubId(), winChip, 0, 0, serviceFee);
            logger.trace("bipai: lastTurn={} roomId={} standUpUserId={} poolChip={} bet={} pump={} earn={} fee={} maxFee={} roomCharge={}",
                    lastTurn, room.getRoomId(), userId, room.getPoolChip(), 0, room.getPump(), serviceFee.getEarn(), 0, 0, room.getRoomChargeTotal());
            historyDao.insertFee(serviceFee);
            historyDao.updateSingleCardScore(SingleCardScore.builder().roomId(room.getRoomId())
                    .userName(room.getRoomPlayers().get(userId).getNickName()).version(room.getStage()).brandType(0)
                    .profit(winChip).cardeType(1).showCard(0).build());
        }

        if (lastTurn > 0) {
            for (RoomPersion r1 : room.getRoomPersions()) {
                if (r1 != null && r1.getBetChouma() != 0) {
                    LogManage.putGameReport(r1, room);//记录正常场log
                    if (r1.getOnlinerType() == -1) {
                        RoomPersion r2 = room.getAudMap().get(r1.getUserId());
                        if (r2 != null) { // 玩家重新进入房间
                            r2.setNowcounma(r2.getNowcounma() + r1.getYq() + r1.getBetChouma());
                        } else {
                            room.getDealer().setLeftChip(r1.getUserId(), r1.getNowcounma());
                        }
                    }
                }
            }
            setStandUpUserGameReport();// 记录游戏中站起玩家统计数据
        }
        List<RoomPersion> persions = new ArrayList<>(room.getAudMap().values());
        // 胜负 积分控制
        for (RoomPersion rp_ : persions) {
            if (rp_.getBetChouma() != 0) {
                //记录场数
                rp_.setAc(rp_.getAc() + 1);
                if (rp_.getYq() > 0) {
                    rp_.setWc(rp_.getWc() + 1);
                    rp_.getUserInfo().setWin(rp_.getUserInfo().getWin() + 1);
                    rp_.setYs(1);
                    if ((rp_.getYq() + rp_.getBetChouma()) > rp_.getUserInfo().getBiggestwinRate()) {
                        rp_.getUserInfo().setBiggestwinRate(rp_.getYq() + rp_.getBetChouma());
                        rp_.getUserInfo().setIsBiggestwinRate(1);
                    } else {
                        rp_.getUserInfo().setIsBiggestwinRate(-1);
                    }
                } else {
                    rp_.getUserInfo().setLose(rp_.getUserInfo().getLose() + 1);
                    rp_.setYs(-1);
                }
            }
        }

        if (lastTurn == 1) {
            objs[0] = oneWinNotice(rps[turnIndex], oneWinInsureFee);
        } else if (lastTurn > 1) {
            objs[0] = bipaijieguo(rpss);
        } else {
            objs[0] = 0;
            // 记录游戏结果数据
            try {
                room.getRoomRecord().setGameResult(room, false, null);
            } catch (Exception e) {
                logger.error("set game result error", e);
            }
        }
        logger.debug("bi pai ...OK");

        gameEventService.emitUserChips(room);

        PersionChipBindDao pcb = new PersionChipBindDaoImp();
        try {
            pcb.updatBindAll(room.getRoomPersions());
        } catch (Exception e) {
            logger.error("PersionChipBindDao ..ERROR", e);
        }
        for (RoomPersion rp : room.getRoomPersions()) {
            if (rp != null) {
                rp.setType(4);
            }
        }

        return objs;
    }

    /**
     * 計算服务费的分配
     * @param userInfo 用户資訊
     * @param amount 服务费
     * @param type 0-CHIP_FEE, 1-INSURANCE_FEE, 2-JP_FEE
     * @param serviceFee 服务费記錄
     * @deprecated
     */
    private synchronized void computeFeeDistribution(UserInfo userInfo, int amount, int type, ServiceFee serviceFee) {
        int userId = userInfo.getUserId();
        if (type == Constant.CHIP_FEE) {
            int fee = 0;
            if (room.getPlFee().containsKey(userId)) {
                fee = room.getPlFee().get(userId);
            }
            room.getPlFee().put(userId, fee + amount);
        }
        // |<------------------fee------------------>|
        // |<--system-->|<-----------club----------->|  club only
        // |<--system-->|<-----club----->|<--tribe-->|  club w/ tribe
        int system = amount;
        int tribeUserFee = 0;
        int tribeId = userInfo.getTribeId();
        double tribeRatio = userInfo.getTribeProportion() * 0.01;
        int clubUserFee = 0;
        int clubId = userInfo.getClubId();
        double clubRatio = userInfo.getClubProportion() * 0.01;
        if (tribeId != 0 && room.getTribeRoomType() == 1) {
            Map<Integer, Integer> tribeFee = room.getTribeFee();
            Map<Integer, Integer> tribeInsurance = room.getTribeInsurance();
            int fee;
            tribeUserFee = (int) Math.round(tribeRatio * system);
            if (type == Constant.CHIP_FEE) {
                fee = tribeFee.computeIfAbsent(tribeId, k -> 0);
                tribeFee.put(tribeId, fee + tribeUserFee);
            } else if (type == Constant.INSURANCE_FEE) {
                fee = tribeInsurance.computeIfAbsent(tribeId, k -> 0);
                tribeInsurance.put(tribeId, fee + tribeUserFee);
            }
            system -= tribeUserFee;
        }
        if (clubId != 0 && room.getClubRoomType() == 1) {
            Map<Integer, Integer> clubFee = room.getClubFee();
            Map<Integer, Integer> clubInsurance = room.getClubInsurance();
            int fee;
            clubUserFee = (int) (clubRatio * system);
            if (type == Constant.CHIP_FEE) {
                fee = clubFee.computeIfAbsent(clubId, k -> 0);
                clubFee.put(clubId, fee + clubUserFee);
            } else if (type == Constant.INSURANCE_FEE) {
                fee = clubInsurance.computeIfAbsent(clubId, k -> 0);
                clubInsurance.put(clubId, fee + clubUserFee);
            }
            system -= clubUserFee;
        }
        if (type == Constant.CHIP_FEE) {
            room.setSystemFee(room.getSystemFee() + system);
            serviceFee.setClubFee(clubUserFee);
            serviceFee.setTribeFee(tribeUserFee);
        } else if (type == Constant.INSURANCE_FEE) {
            room.setInsureSystemFee(room.getInsureSystemFee() + system);
            serviceFee.setClubInsuranceExpenditure(clubUserFee);
            serviceFee.setTribeInsuranceExpenditure(tribeUserFee);
        }
        logger.debug("playerId={} clubId/ratio={}/{} tribeId/ratio={}/{} type={} fee={} system={} clubFee={} tribeFee={}",
                userId, clubId, clubRatio, tribeId, tribeRatio, type, amount, system, clubUserFee, tribeUserFee);
    }

    /**
     * 扣除游戏中站起玩家的下注筹码
     *
     * @param chip
     * @return
     */
    private int reduceStandUpUserChip(int chip) {
        logger.debug("reduce chip: " + chip);
        int reductTotal = 0;
        for (int userId : room.getStandUpUserInfo().keySet()) {
            StandUpUserInfo standUpUserInfo = room.getStandUpUserInfo().get(userId);
            int leftChip = standUpUserInfo.getLeftChip();
            int winChip = standUpUserInfo.getWinChip();
            logger.debug("left chip: " + leftChip);
            logger.debug("win chip: " + winChip);
            if (leftChip > 0) {
                int b = leftChip - chip;
                if (b >= 0) {
                    reductTotal += chip;
                    winChip -= chip;
                    leftChip -= chip;
                } else {
                    reductTotal += leftChip;
                    winChip -= leftChip;
                    leftChip = 0;
                }
            }
            room.setStandUpUserInfo(userId, new StandUpUserInfo(leftChip, winChip, standUpUserInfo.getClubId()));
        }
        return reductTotal;
    }

    /**
     * 分配筹码结束后站起玩家剩余筹码归还
     */
    private void returnStandUpChip(ArrayList<PoolChip> poolList) {
        for (int userId : room.getStandUpUserInfo().keySet()) {
            StandUpUserInfo standUpUserInfo = room.getStandUpUserInfo().get(userId);
            int leftChip = standUpUserInfo.getLeftChip();
            int winChip = standUpUserInfo.getWinChip();
            logger.debug("userId: " + userId + ", left chip: " + leftChip);
            if (leftChip > 0) {
                WinPoolChip winPoolChip = allotChips(poolList, userId, new HashSet<Integer>(), 0);
                int chips = winPoolChip.getEarn();
                winChip = chips - leftChip;
                RoomPersion rp = room.getAudMap().get(userId);
                if (rp != null && rp.getOnlinerType() > 0) {  // 玩家还在房间
                    rp.setNowcounma(rp.getNowcounma() + chips);
                    logger.debug("left chip: " + leftChip + ", win chip: " + winChip + ", chips: " + chips);
                } else {  // 用户已经退出房间
                    room.getDealer().addLeftChip(userId, chips);
                    logger.debug("left chip: " + leftChip + ", win chip: " + winChip + ", chips: " + chips);
                }
            }
            room.setStandUpUserInfo(userId, new StandUpUserInfo(leftChip, winChip, standUpUserInfo.getClubId()));
        }
    }

    /**
     * 分配筹码结束后站起玩家剩余筹码归还
     */
    private void returnStandUpChip() {
        for (int userId : room.getStandUpUserInfo().keySet()) {
            StandUpUserInfo standUpUserInfo = room.getStandUpUserInfo().get(userId);
            int leftChip = standUpUserInfo.getLeftChip();
            logger.debug("left chip: " + leftChip);
            if (leftChip > 0) {
                RoomPersion rp = room.getAudMap().get(userId);
                if (rp != null && rp.getOnlinerType() > 0) {        // 玩家还在房间
                    rp.setNowcounma(rp.getNowcounma() + leftChip);
                } else {                // 用户已经退出房间
                    room.getDealer().addLeftChip(userId, leftChip);
                }
            }
        }
    }

    /**
     * 记录游戏中站起玩家实时战绩
     */
    private void setStandUpUserGameReport() {
        for (int userId : room.getStandUpUserInfo().keySet()) {
            StandUpUserInfo standUpUserInfo = room.getStandUpUserInfo().get(userId);
            int leftChip = standUpUserInfo.getLeftChip();
            int winChip = standUpUserInfo.getWinChip();
            logger.debug("left chip: " + leftChip);
            logger.debug("setStandUpUserGameReport roomId=" + room.getRoomId() + " userid: " + userId + " chip: " + winChip);
            LogManage.putGameReport(room, userId, leftChip, winChip);
        }
    }

    /**
     * 最大牌 牌类型 和
     *
     * @param pocer 需要比的七张牌
     * @return
     */
    private static Object[] zuidapai(Pocer[] pocer) {
        return BiPai.zuidapai(pocer);
    }

    /**
     * 最大牌 牌类型 和
     *
     * @param pocer 需要比的五至七张牌
     * @return
     */
    public static Object[] zuidapai1(Pocer[] pocer) {
        return BiPai.zuidapai1(pocer);
    }

    /**
     * 清空 本轮玩家已经下的筹码
     */
    public void clearAuneCount() {
        for (RoomPersion r : room.getRoomPersions()) {
            if (r != null) {
                r.setAnteCount(0);
            }
        }
    }

    /**
     * 两对牌对比查看那个大 -1一样大 0：rp1大       1：rp2大
     *
     * @param rp1
     * @param rp2
     * @return
     */
    public static int bipai2(RoomPersion rp1, RoomPersion rp2) {
        return BiPai.bipai2(rp1, rp2);
    }


    protected static void swap(Object[] array, int i, int j) {
        Object temp;
        temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    /**
     * 获取当前房间情况数据
     *
     * @return
     */
    private byte[] getRoomData2(int userId, int mySeatID, int chip, int nowChip) {
        int status = 0;

        int gamestatus = -1;
        int countdowntimes = -1;
        int roomStatus = room.getRoomStatus();
        if (room.getIsPause()) {
            roomStatus = room.getLastRoomStatus();
        }

        if ((3 <= roomStatus && roomStatus <= 6) || roomStatus == 9) {
            gamestatus = 1;
        } else if (roomStatus == 2) {
            gamestatus = 0;
            long t = (Constant.STATUS_2_DC - (System.currentTimeMillis() - room.getT2())) / 1000;
            countdowntimes = (int) t;
        } else if (roomStatus == 0) {
            gamestatus = -2; //等待房主开始游戏状态
        }

        logger.debug("[R-{}][U-{}]getRoomData2,roomstatus={},pause={},gamestatus={}",room.getRoomId(),userId, roomStatus, room.getIsPause(), gamestatus);

        Integer[] cards = {-1, -1, -1, -1, -1};
        Integer[] pocerType = {0, 0, 0, 0, 0};
        for (int i = 0; i < room.getPocer().length; i++) {
            if (room.getPocer()[i] != null) {
                cards[i] = room.getPocer()[i].getSize1();
            } else {
                break;
            }
        }
        for (int i = 0; i < room.getPocerType().length; i++) {
            pocerType[i] = room.getPocerType()[i];
        }

        Integer[] playerIdArray = new Integer[room.getPlayerCount()];
        //1:下注  2:跟注  3:加注  4:全下 5:让牌  6:弃牌 7托管 8等待下一盘 15:留座离桌 18:占座
        Integer[] statusArray = new Integer[room.getPlayerCount()];
        Integer[] anteArray = new Integer[room.getPlayerCount()];
        StringBuilder nickString = new StringBuilder();
        Integer[] chipsArray = new Integer[room.getPlayerCount()];
        StringBuilder headPicString = new StringBuilder();
        Integer[] firstCardArray = new Integer[room.getPlayerCount()];
        Integer[] secondCardArray = new Integer[room.getPlayerCount()];
        Integer[] sexArray = new Integer[room.getPlayerCount()];
        Integer[] canPlayArray = new Integer[room.getPlayerCount()];
        Integer[] passHandArray = new Integer[room.getPlayerCount()];       // 0:none 1:toast >0 气泡
        Integer[] leftOccupyTime = new Integer[room.getPlayerCount()];      // 剩余占座时间
        Integer[] occupyStatusArray = new Integer[room.getPlayerCount()];   //占座状态

        int maxAnte = 0;            // 本轮最大下注筹码
        int showCardsId = 0;        // 0 不亮 1亮第一张 2亮第二张 3亮两张
        int timeoutCheckCount = 0;  // 当前操作的人未操作次数
        int posting = 0;            // 0不用弹补盲窗 1需要弹
        int genzhu = 0;             // 本人操作，最少跟注筹码

        Integer[] occupySeats = {-1, -1, -1, -1, -1, -1, -1, -1, -1};
        String[] heads = new String[room.getPlayerCount()];
        String[] names = new String[room.getPlayerCount()];
        logger.debug("[R-{}][U-{}]occupy seat users={}", room.getRoomId(),userId, room.getOccupySeatPlayers().keySet().toString());
        Integer[] poolArray = null;
        if (room.getAllinCount() != 0) {
            poolArray = getPoolInfo(false);
        } else {
            poolArray = new Integer[]{room.getPoolChip()};
        }
        int occupySeat = -1;
        for (Integer id : room.getOccupySeatPlayers().keySet()) { // 当前房间留座离桌玩家
            RoomPlayer roomPlayer = room.getOccupySeatPlayers().get(id);
            if (null != roomPlayer) {
                int seat = roomPlayer.getSeatSize();
                if (3 == roomPlayer.getSeatStatus() && seat >= 0 && seat < room.getPlayerCount()) {
                    statusArray[seat] = 15;                     // 占座模式
                    chipsArray[seat] = roomPlayer.getNowChipWhenOccupySeat();
                    sexArray[seat] = roomPlayer.getSex();
                    playerIdArray[seat] = id;
                    leftOccupyTime[seat] = 180 - (int) (System.currentTimeMillis() / 1000) + roomPlayer.getOccupyTime();

                    occupySeats[seat] = seat;
                    heads[seat] = roomPlayer.getHeader();
                    if (roomPlayer.getUseCustom() != null) {
                        if (roomPlayer.getUseCustom().equals(1)) {
                            if (!StringUtils.isBlank(roomPlayer.getCustomUrl())) {
                                heads[seat] = roomPlayer.getCustomUrl();
                            }
                        }
                    }
                    names[seat] = roomPlayer.getNickName();
                    if (id == userId) {
                        occupySeat = seat;
                        logger.debug("[R-{}][U-{}] user is occuping seat now,occupySeat={}", room.getRoomId(), userId, occupySeat);
                    }
                }
            }
        }

        for (int userid : room.getRoomPlayers().keySet()) {  // 当前房间占座玩家
            RoomPlayer roomPlayer = room.getRoomPlayers().get(userid);
            if (null != roomPlayer) {
                int seat = roomPlayer.getSeatSize();
                if (4 == roomPlayer.getSeatStatus() && seat >= 0 && seat < room.getPlayerCount()) {
                    statusArray[seat] = 18;                     // 占座模式
                    sexArray[seat] = roomPlayer.getSex();
                    playerIdArray[seat] = userid;
                    chipsArray[seat] = 0;
                    if (roomPlayer.isApplyFor()) {
                        occupyStatusArray[seat] = (int) (180 - (System.currentTimeMillis() - roomPlayer.getApplyForBeginTime()) / 1000);
                    } else {
                        occupyStatusArray[seat] = -1;
                    }

                    occupySeats[seat] = seat;
                    heads[seat] = roomPlayer.getHeader();
                    if (roomPlayer.getUseCustom() != null) {
                        if (roomPlayer.getUseCustom().equals(1)) {
                            logger.debug("UseCustom---id: {}", roomPlayer.getUserId());
                            if (!StringUtils.isBlank(roomPlayer.getCustomUrl())) {
                                logger.debug("CustomUrl--- {}", roomPlayer.getCustomUrl());
                                heads[seat] = roomPlayer.getCustomUrl();
                            }
                        }
                    }
                    names[seat] = roomPlayer.getNickName();
                    if (userid == userId) {
                        occupySeat = seat;
                        logger.debug("[R-{}][U-{}] user is zhanzuo now,occupySeat={}", room.getRoomId(), userId, occupySeat);
                    }
                }
            }
        }
        logger.debug("heads--- {}", JSONObject.toJSONString(heads));

        for (int i = 0; i < room.getRoomPersions().length; i++) {  //获取座位上玩家数据
            RoomPersion roomPersion = room.getRoomPersions()[i];
            if (roomPersion == null || roomPersion.getOnlinerType() == -1) {
                roomPersion = room.getDdRoomPersions()[i];
            }

            if (roomPersion == null || roomPersion.getOnlinerType() == -1) {

                String name = "null";
                String head = "null";
                anteArray[i] = -1;

                if (occupySeats[i] == i) {
                    name = StringUtil.trimString(names[i]);
                    head = heads[i];
                    logger.debug("[R-{}][U-{}] seat is occupied,occupySeat={},name={}",room.getRoomId(),userId, occupySeat, name);
                } else {
                    leftOccupyTime[i] = -1;
                    chipsArray[i] = -1;
                    statusArray[i] = -1;
                    sexArray[i] = -1;
                    playerIdArray[i] = -1;
                    occupyStatusArray[i] = -1;
                }
                if (i == 0) {
                    nickString.append(name);
                    headPicString.append(head);
                } else {
                    nickString.append(sp).append(name);
                    headPicString.append(sp).append(head);
                }
                firstCardArray[i] = -1;
                secondCardArray[i] = -1;
                canPlayArray[i] = 0;
                passHandArray[i] = 0;

            } else {

                if (roomPersion.isApplyFor() && roomPersion.getType() == 6) {
                    occupyStatusArray[i] = (int) (180 - (System.currentTimeMillis() - roomPersion.getApplyForBeginTime()) / 1000);
                } else {
                    occupyStatusArray[i] = -1;
                }

                leftOccupyTime[i] = -1;
                String nick = roomPersion.getUserInfo().getNikeName(userId, roomPersion.getUserId());
                if (i == 0) {
                    nickString.append(StringUtil.trimString(nick));
                    String head = roomPersion.getUserInfo().getHead();
                    if (roomPersion.getUserInfo().getUseCustom().equals(1)) {
                        if (!StringUtils.isBlank(roomPersion.getUserInfo().getCustomUrl())) {
                            head = roomPersion.getUserInfo().getCustomUrl();
                        }
                    }
                    headPicString.append(head);
                } else {
                    nickString.append(sp).append(StringUtil.trimString(nick));
                    String head = roomPersion.getUserInfo().getHead();
                    if (roomPersion.getUserInfo().getUseCustom().equals(1)) {
                        if (!StringUtils.isBlank(roomPersion.getUserInfo().getCustomUrl())) {
                            head = roomPersion.getUserInfo().getCustomUrl();
                        }
                    }
                    headPicString.append(sp).append(head);
                }
                sexArray[i] = roomPersion.getUserInfo().getSex();
                playerIdArray[i] = roomPersion.getUserId();
                int hand = room.getDealer().getHandCnt(roomPersion.getUserId());

                if (roomPersion.getType() == 2 || roomPersion.getType() == 4) {
                    statusArray[i] = 8; //等待下一盘
                    anteArray[i] = 0;
                } else if (roomPersion.getType() == 6 && roomPersion.getNowcounma() <= 0) {
                    statusArray[i] = 18; //占座状态
                } else {

                    statusArray[i] = roomPersion.getClientStatus();
                    if (room.getRoomStatus() == 3 || roomPersion.getAnteCount() == 0) {
                        if (room.getRoomStatus() == 3 && roomPersion.getSize() == room.getStraddleNumber()) {
                            statusArray[i] = 10;
                        }
                    }
                    if (hand == 0) {
                        if (roomPersion.getNowcounma() <= 0) {  //第一次进入房间且没申请过带入
                            statusArray[i] = 18;
                        } else {
                            statusArray[i] = 8;   //第一次进入房间申请了带入
                        }
                    } else {  //已经该房间打过  站起又坐下 此时type为6
                        if (roomPersion.getType() == 6) {
                            statusArray[i] = 8; //等待下一盘
                        }
                    }

                    if (room.getRoomStatus() == 3) { // 客户端前注动画需要加的判断
//                        anteArray[i] = roomPersion.getAnteCount() - room.getQianzhu();
                        anteArray[i] = roomPersion.getAnteCount();
                        if (roomPersion.getSize() == room.getManzhuNumber() || roomPersion.getSize() == room.getDamanzhuNumber() ||
                                roomPersion.getSize() == room.getStraddleNumber() || room.getBuMangPlay().containsKey(roomPersion.getUserId())) {
                            anteArray[i] = roomPersion.getBetChouma() - room.getQianzhu();
                        }
                    } else {
                        anteArray[i] = roomPersion.getAnteCount();
                    }

                    if (anteArray[i] == -1) {
                        anteArray[i] = 0;
                    }
                    if (room.getRoomStatus() == 3) {
                        int anteCount = roomPersion.getAnteCount();
                        if (roomPersion.getSize() == room.getManzhuNumber()) {
                            anteCount += room.getManzhu();
                        } else if (roomPersion.getSize() == room.getDamanzhuNumber()) {
                            anteCount += room.getDamanzhu();
                        } else if (roomPersion.getSize() == room.getStraddleNumber()) {
                            anteCount += roomPersion.getStraddleChip();
                        }

                        if (maxAnte < anteCount) {
                            maxAnte = anteCount;
                        }

                    } else {
                        if (maxAnte < roomPersion.getAnteCount()) {
                            maxAnte = roomPersion.getAnteCount();
                        }
                    }

                }

                if (18 == statusArray[i]) {
                    chipsArray[i] = 0;
                } else {
                    chipsArray[i] = roomPersion.getNowcounma();
                }
                canPlayArray[i] = roomPersion.isCanPlay() ? 1 : 0;
                passHandArray[i] = roomPersion.getPassHand();

                if (roomPersion.getUserId() == userId) {
                    if (roomPersion.getType() == 3) {  //玩家处于游戏中
                        genzhu = room.getMaxChouma() - roomPersion.getBetChouma();
                        if (room.isCanSeeHandCard()) {
                            firstCardArray[i] = roomPersion.getPocers()[0] == null ? -1 : roomPersion.isCanSeeHandCard() ? roomPersion.getPocers()[0].getSize1() : -1;
                            secondCardArray[i] = roomPersion.getPocers()[1] == null ? -1 : roomPersion.isCanSeeHandCard() ? roomPersion.getPocers()[1].getSize1() : -1;
                        } else {
                            firstCardArray[i] = roomPersion.getPocers()[0] == null ? -1 : roomPersion.getPocers()[0].getSize1();
                            secondCardArray[i] = roomPersion.getPocers()[1] == null ? -1 : roomPersion.getPocers()[1].getSize1();
                        }

                        chip = roomPersion.getChouma();
                        nowChip = roomPersion.getNowcounma();
                        showCardsId = roomPersion.getShowCardsId();
                        timeoutCheckCount = roomPersion.getTimeoutCheckOpTimes();
                    } else {
                        firstCardArray[i] = -1;
                        secondCardArray[i] = -1;
                        if (roomPersion.getPlayType() == 0 && room.getStage() > 0) {
                            posting = 1;
                        }
                    }

                    mySeatID = i;
                } else {
                    firstCardArray[i] = -1;
                    secondCardArray[i] = -1;
                }
                // 如果当前处于保险状态 参与比牌的人牌型全部亮出来
                //logger.debug("roomType={},status={}",roomPersion.getType(),roomPersion.getStatus());
                if (roomStatus == 9 && roomPersion.getUserId() != userId && roomPersion.getType() == 3 && roomPersion.getStatus() > 0) {   //在游戏中且未弃牌的玩家
                    firstCardArray[i] = roomPersion.getPocers()[0] == null
                            ? -1 : roomPersion.getPocers()[0].getSize1();
                    secondCardArray[i] = roomPersion.getPocers()[1] == null
                            ? -1 : roomPersion.getPocers()[1].getSize1();
                }
            }
        }
//        if (room.getRoomStatus() == 3) { // 客户端前注动画需要加的判断
//            maxAnte = maxAnte - room.getQianzhu();
//        }

        if (occupySeat > -1) {
            mySeatID = occupySeat;
        }
        int leftOperateTime = 0;    // 当前操作玩家剩余时间
        if (room.getRoomStatus() >= 2) {
            leftOperateTime = (int) ((room.getOpTime() - 3) * 1000 - (System.currentTimeMillis() - room.getT3()));

            if (room.getCurrentNumber() > -1) {
                RoomPersion roomPersion = room.getRoomPersions()[room.getCurrentNumber()];
                if (room.getRoomStatus() == 3 && roomPersion.getUserId() == room.getFirstOpSize()) { //如果进房时是每手第一行动人且还未操作过,需要考虑延时的时间
                    logger.debug("[R-{}]进房时是每手第一行动人且还未操作过",room.getRoomId());
                    leftOperateTime = leftOperateTime + Constant.FIRST_OPERATE_DELAY_TIME * 1000;
                }
            }

            if (leftOperateTime < 0) {
                leftOperateTime = 0;
            }
        }

        int roomLeftTime = -1;    // 牌局剩余时间(s), 未开始返回－1
        if (room.getRoomStatus() >= 1) {    // 牌局开始
            roomLeftTime = (int) ((room.getGameBeginTime() + room.getMaxPlayTime() * 60000 - System.currentTimeMillis()) / 1000);
        }

        int pauseLeftTime = 0;    // 牌局暂停剩余时间
        if (room.getIsPause()) {
            pauseLeftTime = (int) ((room.getPauseBeginTime() + Constant.PAUSE_TIME - System.currentTimeMillis()) / 1000);
            pauseLeftTime = Math.max(pauseLeftTime, 0);
            leftOperateTime = (int) room.getPauseUserLeftOpTime();    // 取暂停状态下玩家剩余操作时间
        }
        int lastMinRate = room.getRequestMinRate() > 0 ? room.getRequestMinRate() : room.getMinRate();
        int lastMaxRate = room.getRequestMaxRate() > 0 ? room.getRequestMaxRate() : room.getMaxRate();

        logger.debug("[R-{}] leftOperateTime={},pauseLeftTime={},roomLeftTime={}",room.getRoomId(), leftOperateTime, pauseLeftTime, roomLeftTime);

        int canRaise = 1;
        if (room.getAttackId() != -1 && mySeatID == room.getAttackId()) {
            canRaise = 0;
        }
        // 是否在其他房间有留盲代打
        Map<String, String> hash = RedisService.getRedisService().getAutoOp(userId, room.getRoomId());
        boolean isAutoOp = hash == null && RedisService.getRedisService().hasAutoOp(userId);

        logger.debug("[R-{}] canRaise={},isAutoOp={},posting={}", room.getRoomId(), canRaise, isAutoOp, posting);

        String showCardsUserName = room.getShowCardsUserName(); //花钻石看底牌的玩家名字

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        int aheadLeave = 0;  //是否提前离桌(0否 1是)
        int isBringIn = 0;   //是否带入过(0否 1是)
        int userDelayTimes = 0; //用户延时操作次数
        int selectClubId = 0;
        if (roomPlayer != null) {
            aheadLeave = roomPlayer.getAheadLeave();
            isBringIn = roomPlayer.getIsBringIn();
            userDelayTimes = roomPlayer.getUserDelayTimes();
            selectClubId = roomPlayer.getSelectClubId();
        }

        int creditValue = Integer.MAX_VALUE; //为了让玩家可以避开前端信用分不足的提前判断，不管什么局都返回最大值
        int serviceFee = Integer.parseInt(Cache.p.getProperty("service.fee")); //服务费
        logger.debug("user now chip: " + nowChip + ", user left operate time:" + leftOperateTime + ", isAutoOp: " + isAutoOp + ", genzhu: " + genzhu);
        logger.debug("canPlayArray: " + Arrays.toString(canPlayArray));
        logger.debug("passHandArray " + Arrays.toString(passHandArray));
        logger.debug("playerIdArray: " + Arrays.toString(playerIdArray));
        logger.debug("statusArray: " + Arrays.toString(statusArray));
        logger.debug("chipsArray: " + Arrays.toString(chipsArray));
        logger.debug("occupyStatusArray: " + Arrays.toString(occupyStatusArray));
        logger.debug("returned gps limit: " + room.isLimitGPS());
        logger.debug("returned getTribeId: " + room.getTribeId());
        logger.debug("returned nickString: " + nickString.toString());
        logger.debug("returned headString: " + headPicString.toString());
        logger.debug("returned pocerType: " + Arrays.toString(pocerType));
        logger.debug("returned showCards userName: " + showCardsUserName);
        logger.debug("returned leftOccupyTime: " + Arrays.toString(leftOccupyTime));
        logger.debug("aheadLeave： " + aheadLeave);
        logger.debug("isBringIn： " + isBringIn);
        logger.debug("aheadMode:" + room.getAheadLeaveMode());
        logger.debug("serviceFee:" + serviceFee);
        logger.debug("userDelayTimes:" + userDelayTimes);
        logger.debug("selectClubId:" + selectClubId);
        logger.debug("myseat id: " + mySeatID);
        logger.debug("tribeRoomType: " + room.getTribeRoomType());

        int betChip = room.getAudMap().get(userId).getAnteCount();
        int chae = 0;
        if (room.getRoomStatus() == 3) {
            int firstAnteCount = 0;
            RoomPersion persion = room.getAudMap().get(userId);
            if (room.getDamanzhuNumber() == persion.getSize()) {
                firstAnteCount = room.getDamanzhu();
            } else if (room.getManzhuNumber() == persion.getSize()) {
                firstAnteCount = room.getManzhu();
            } else if (room.getStraddleNumber() == persion.getSize()) {
                firstAnteCount = room.getDamanzhu() + room.getDamanzhu();
            }
            if (room.getLastBet() > 0 && room.getLastAndLastBet() > 0 && room.getJiaZhuCount() > 0) {
                chae = room.getMin2JaZhu(betChip + firstAnteCount, 0);
            }
        } else {
            if (room.getJiaZhuCount() > 1) {
                chae = room.getMin2JaZhu(betChip, 0);
            }
        }
        Object[][] objs = {
            {60, status, I366ClientPickUtil.TYPE_INT_1},
            {61, gamestatus, I366ClientPickUtil.TYPE_INT_1},
            {62, countdowntimes, I366ClientPickUtil.TYPE_INT_1},
            {65, room.getDamanzhuNumber(), I366ClientPickUtil.TYPE_INT_1},
            {66, room.getManzhuNumber(), I366ClientPickUtil.TYPE_INT_1},
            {67, room.getZhuangjiaNumber(), I366ClientPickUtil.TYPE_INT_1},
            {68, room.getCurrentNumber(), I366ClientPickUtil.TYPE_INT_1},
            {69, room.getOpTime() - 3, I366ClientPickUtil.TYPE_INT_1},    // 每个玩家实际可操作最大时间（s）
            {70, mySeatID, I366ClientPickUtil.TYPE_INT_1},
            {71, Constant.MAX_TIMEOUT_CHECK_TIME, I366ClientPickUtil.TYPE_INT_1},   // 最大允许超时check次数
            {72, timeoutCheckCount, I366ClientPickUtil.TYPE_INT_1},                 // 当前操作的人超时check次数
            {131, cards, I366ClientPickUtil.TYPE_INT_1_ARRAY},
            {132, playerIdArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
            {133, statusArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},                // 玩家状态
            {134, anteArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},
            {135, nickString.toString(), I366ClientPickUtil.TYPE_STRING_UTF16},     // 昵称
            {136, chipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},                 // 剩余筹码
            {137, headPicString.toString(), I366ClientPickUtil.TYPE_STRING_UTF16},  // 头像
            {138, firstCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},
            {139, secondCardArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},
            {140, room.getDamanzhu(), I366ClientPickUtil.TYPE_INT_4},
            {141, room.getManzhu(), I366ClientPickUtil.TYPE_INT_4},
            {142, room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4},
            {143, maxAnte, I366ClientPickUtil.TYPE_INT_4},
            {144, room.getOwner(), I366ClientPickUtil.TYPE_INT_4},
            {145, String.valueOf(room.getGameBeginTime()), I366ClientPickUtil.TYPE_STRING_UTF16},
            {146, room.getMaxPlayTime(), I366ClientPickUtil.TYPE_INT_4},
            {147, 0, I366ClientPickUtil.TYPE_INT_1},    // 是否开启带入控制 1开启 0不开启
            {148, chip, I366ClientPickUtil.TYPE_INT_4},                            // 用户筹码
            {149, nowChip, I366ClientPickUtil.TYPE_INT_4},                        // 用户房间筹码
            {150, roomLeftTime, I366ClientPickUtil.TYPE_INT_4},                    // 牌局剩余时间
            {151, pauseLeftTime, I366ClientPickUtil.TYPE_INT_4},                // 房间暂停剩余时间 0 非暂停状态
            {152, lastMinRate, I366ClientPickUtil.TYPE_INT_4},                    // 上次修改未生效记录
            {153, lastMaxRate, I366ClientPickUtil.TYPE_INT_4},                    // 上次修改未生效记录
            {154, room.getMinRate(), I366ClientPickUtil.TYPE_INT_4},            // 当前最小带入倍数
            {155, room.getMaxRate(), I366ClientPickUtil.TYPE_INT_4},            // 当前最大带入倍数
            {156, leftOperateTime / 1000, I366ClientPickUtil.TYPE_INT_4},            // 当前操作玩家剩余操作时间(s)
            {157, sexArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},                // 桌面玩家性别
            {158, showCardsId, I366ClientPickUtil.TYPE_INT_4},                  // 0 不亮 1亮第一张 2亮第二张 3亮两张
            {159, room.getQianzhu(), I366ClientPickUtil.TYPE_INT_4},            // 前注
            {160, 0, I366ClientPickUtil.TYPE_INT_4},                            // 上次控制带入请求剩余等待时间
            {161, poolArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},              // 主池边池数据
            {162, room.getMinRaiseNumber() / 2, I366ClientPickUtil.TYPE_INT_4},     // 最小加注金额
            {163, canRaise, I366ClientPickUtil.TYPE_INT_4},                     // 是否能加注
            {164, room.getInsurance(), I366ClientPickUtil.TYPE_INT_4},          // 保险模式
            {165, canPlayArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},           // 是否能打牌
            {166, passHandArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},          // 0:none 1:toast >0 气泡
            {167, 0, I366ClientPickUtil.TYPE_INT_4},                      // 0过庄 1补盲 2正常
            {168, creditValue, I366ClientPickUtil.TYPE_INT_4},                  // 用户信用额度，MAX_VALUE表示无限制
            {169, 2, I366ClientPickUtil.TYPE_INT_4},     // 房间类型 (1:普通房 2:社区有限时长局 3:社区无限时长局 )
            {170, 0, I366ClientPickUtil.TYPE_INT_4},    // 需要带入的筹码数 (只有roomType=2时有效,值0表示第一次进入房间)
            {171, isAutoOp ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},             // 是否有被托管
            {172, genzhu, I366ClientPickUtil.TYPE_INT_4},                       // 轮到本人操作需要跟注筹码
            {173, pocerType, I366ClientPickUtil.TYPE_INT_1_ARRAY},              // 底牌类型 (0:正常发牌 1:玩家花钻石发牌)
            {174, 0, I366ClientPickUtil.TYPE_INT_4},                            // 是否在其他房间有离桌留座
            {175, leftOccupyTime, I366ClientPickUtil.TYPE_INT_4_ARRAY},          // 剩余留座时间 单位s
            {176, showCardsUserName, I366ClientPickUtil.TYPE_STRING_UTF16},      // 花钻石看底牌的玩家名字
            {177, room.isLimitIp() ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},      // ip控制 0否 1是
            {178, room.isLimitGPS() ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},     // gps控制
            {179, room.getTribeId(), I366ClientPickUtil.TYPE_INT_4},             // 房间的联盟id
            {180, occupyStatusArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},       // 占座等待状态
            {181, isBringIn, I366ClientPickUtil.TYPE_INT_4},             // 该玩家是否有在本牌局中带入过，0否1是
            {182, aheadLeave, I366ClientPickUtil.TYPE_INT_4},             // 是否已经提前离桌，0否1是，前端结合181字段判断是否要在菜单栏中显示“提前离桌”按钮
            {183, room.getSpectatorVoiceSwitch(), I366ClientPickUtil.TYPE_INT_1},//玩家语音功能是否开启
            {184, "1.0.0", I366ClientPickUtil.TYPE_STRING_UTF16},//当前客户端对应最新版本号
            {185, room.getAheadLeaveMode(), I366ClientPickUtil.TYPE_INT_1},//提前离桌
            {186, room.getCurrentRoomActionTimes(), I366ClientPickUtil.TYPE_INT_4},//当前房间操作累计次数
            {187, 0, I366ClientPickUtil.TYPE_INT_1},//是否定向房间
            {188, serviceFee, I366ClientPickUtil.TYPE_INT_4},//服务费百分比
            {189, 0, I366ClientPickUtil.TYPE_INT_1},//是否开启信用控制
            {190, room.getOpTime() - 3, I366ClientPickUtil.TYPE_INT_4},//思考时间
            {191, userDelayTimes, I366ClientPickUtil.TYPE_INT_4},//用户延时操作次数
            {192, room.getFirstHandChip() * 2 + room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4}, //最小po
            {193, chae, I366ClientPickUtil.TYPE_INT_4},//计算出的差额
            {194, chae == 0 ? 0 : room.getLastBet(), I366ClientPickUtil.TYPE_INT_4},//上个人的有效下注
            {195, room.getRoomStatus() == 3 ? 0 : 1, I366ClientPickUtil.TYPE_INT_4},
            {198, room.getClubRoomType(), I366ClientPickUtil.TYPE_INT_4},
            {199, room.getTribeRoomType(), I366ClientPickUtil.TYPE_INT_4},
            {200, selectClubId, I366ClientPickUtil.TYPE_INT_4},
            {201, room.getMinVpip(), I366ClientPickUtil.TYPE_INT_4},    // 最小入池率
            {202, room.isCanSeeHandCard() ? 1 : 0, I366ClientPickUtil.TYPE_INT_1},  // 是否可以看底牌
            {203, room.getStage(), I366ClientPickUtil.TYPE_INT_4}, // 第几手
        };
        return I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_ENTER_ROOM);
    }

    /**
     * 通知开始游戏 设置玩家状态 并发两张底牌
     */
    public synchronized void beginGameOne() {
        logger.info("[R-{}] RoomService.java通知开始游戏.....",room.getRoomId());
        int count = 0;
        room.getDealer().resetGameStatus();
        room.getVigilante().onNewHand();
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            if (room.getRoomPersions()[i] != null) {
                room.getDealer().resetPersonDataStatus(room.getRoomPersions()[i]);
                if (room.getRoomPersions()[i].getNowcounma() >= room.getMinChip()) {
                    count++;
                }
                room.getRoomPersions()[i].setInsuranceLast(0);
                room.getRoomPersions()[i].setWinInsuranceLast(0);
            }
        }
        if (count < 2) {
            room.setRoomStatus(1);  //  重新开始等待
            return;
        }

        room.setStage(room.getStage() + 1); //设置房间手数
        room.setStageFin(false);

        //  记录牌局开局信息
        room.setRoomReplay(new RoomReplay());
        room.getRoomReplay().setStartTime(System.currentTimeMillis() / 1000);
        room.getRoomReplay().setStage(room.getStage());
        room.getRoomReplay().setRoomId(room.getRoomId());
        room.getRoomReplay().setId(room.getRoomUUID());
        room.getRoomReplay().setRoomName(room.getName());
        room.getRoomReplay().setOwner(room.getOwner());
        room.getRoomReplay().setStraddle(room.getStraddle() ? 1 : 0);

        int beginHandTotolChouma = 0;  //记录所有玩家每手初始筹码总值
        int beginHandPlayerCount = 0;  //记录每手开始时玩家总数
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            if (room.getRoomPersions()[i] != null) {
                if (room.getRoomPersions()[i].getNowcounma() > room.getMinChip()) {
                    room.getRoomPersions()[i].setHandBeginChip(room.getRoomPersions()[i].getNowcounma());
                    beginHandTotolChouma += room.getRoomPersions()[i].getNowcounma();
                    beginHandPlayerCount++;
                }

            }
        }

        //logger.debug("beginGameOne,roomid={},beginHandTotolChouma={},beginHandPlayerCount={}",room.getRoomId(),beginHandTotolChouma,beginHandPlayerCount);

        room.setTotalBeginHandChouma(beginHandTotolChouma);
        room.setBeginHandPlayerCount(beginHandPlayerCount);

        nextmanzhu(); //  指定大小盲注和前注
        Integer[] chipsArray = new Integer[room.getPlayerCount()];
        //----
        Integer[] canPlayArray = new Integer[room.getPlayerCount()];
        Integer[] passHandArray = new Integer[room.getPlayerCount()];
        Integer[] beforeAllinChipArray = new Integer[room.getPlayerCount()];    //不够前注时,玩家all in之前的筹码数
        Set<Integer> playingUsers = new HashSet<Integer>();
        room.getStraddlePlayers().clear();
        int i = room.getZhuangjiaNumber();
        int cnt = 0;
        boolean isNeedStraddle = true;
        while (cnt < room.getPlayerCount()) {
            RoomPersion p = room.getRoomPersions()[i];
            if (p != null) {
                if (p.getAddUpTime() < 1) {
                    p.setAddUpTime(System.currentTimeMillis());
                }
                playingUsers.add(p.getUserId());
                p.setIsGame(1);
                p.setType(3);
                p.setAnteNumber(0);

                beforeAllinChipArray[i] = p.getBeforeAllinScore();
                chipsArray[i] = p.getNowcounma();
                canPlayArray[i] = 1;
                passHandArray[i] = p.getPassHand();
                if (room.getStraddle() && playingUsers.size() >= 4 && isNeedStraddle) {// 强制straddle，utg强制下注
                    int straddleChip = room.getDamanzhu() * ((int) Math.pow(2, playingUsers.size() - 3));
                    room.getStraddlePlayers().put(p.getUserId(), straddleChip);
                    p.setStraddleChip(straddleChip);
                    isNeedStraddle = false;
                }
            } else {
                chipsArray[i] = -1;
                canPlayArray[i] = 0;
                passHandArray[i] = 0;
                beforeAllinChipArray[i] = -1;
            }
            cnt++;
            i = (i + 1) % room.getPlayerCount();
        }

        for (i = 0; i < room.getDdRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getDdRoomPersions()[i];
            if (roomPersion != null) {
                passHandArray[i] = roomPersion.getPassHand();
            }
        }

        boolean needStraddle = false; //是否需要straddle
        if (room.getStraddle() && playingUsers.size() >= 4 && room.getStraddlePlayers().size() > 0) {
            needStraddle = true;
        }

        room.setStraddleFin(false);
        //记录了初始化派普
        GameProcess gameProcess = GameProcess.builder().roomId(room.getRoomId()).shoushuCode(1).publicCard(new ArrayList<>())
                .chip(room.getPoolChip()).users(count).singleProcess(new ArrayList<>()).version(room.getStage()).build();
        historyDao.insertGameProcess(gameProcess);

        beginNewHand(needStraddle);

        for (Integer key : room.getAudMap().keySet()) {
            RoomPersion rp = room.getAudMap().get(key);
            if (rp != null) {
                int straddleChip = 0;
                if (room.getStraddlePlayers().get(key) != null) {
                    straddleChip = room.getStraddlePlayers().get(key);
                }
                Object[][] objs = {
                    {60, room.getZhuangjiaNumber(), I366ClientPickUtil.TYPE_INT_1},     // 庄家编号
                    {130, needStraddle ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},         // 牌局是否有straddle 0否 1是
                    {131, straddleChip > 0 ? 1 : 0, I366ClientPickUtil.TYPE_INT_4},     // 当前用户是否straddle 0否 1是
                    {132, canPlayArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},           // 能否打牌
                    {133, passHandArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},          // 0 none 1 toast >0 气泡
                    {134, chipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},             // 每个座位上玩家的筹码
                    {135, straddleChip, I366ClientPickUtil.TYPE_INT_4},                 // straddle用户消耗的筹码
                    {138, beforeAllinChipArray, I366ClientPickUtil.TYPE_INT_4_ARRAY}    // 不够前注时,玩家all in之前的筹码数
                };
                byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_START_STRADDLE);
                PublisherUtil.sendByUserId(room, bytes, key);
            }
        }
        for (RoomPersion persion : room.getRoomPersions()) {
            if (persion != null) {
                List<Integer> shouCard = Arrays.stream(persion.getPocers()).filter(Objects::nonNull).map(Pocer::getSize1)
                        .collect(Collectors.toList());
                historyDao.updateSingleCardScore(SingleCardScore.builder().roomId(room.getRoomId()).brandType(0)
                        .userName(persion.getUserInfo().getNikeName()).cardeType(0).version(room.getStage())
                        .card(shouCard).showCard(0).build());
            }
        }
    }

    public void beginNewHand(boolean needStraddle) {
        logger.info("R-{}:{} beginNewHand .....needStraddle={}", room.getRoomId(), room.getStage(), needStraddle);

        room.setStraddleFin(true);
        room.setCardControl(false); //重置控制发牌开关

        //PreFlopInterveneResult result = new PreFlopInterveneResult();
        try {
            //IUserWeightService userWeightService = new UserWeightServiceImpl(room);
            //result = userWeightService.doPrepFlopIntervene();
            room.getPocerLink().newShuffle(); // 洗牌
        } catch (Exception e) {
            logger.error("翻前干预手牌异常", e);
        }

        initBetChip(needStraddle);// 扣筹码

        gameEventService.emitUserBringIn(room);
        gameEventService.emitUserChips(room);

//        if (Cache.p.getProperty("cards.control", "0").equals("1")) {// 指定发牌
//            room.getPocerLink().initPlayerPoker(room.getRoomPersions());
//        }

        room.getPocerLink().getPokerControl().checkCardControl(room);// 是否控制发牌


        Integer[] firstCardArray = new Integer[room.getPlayerCount()];
        Integer[] secondCardArray = new Integer[room.getPlayerCount()];
        Integer daojishi[] = new Integer[room.getPlayerCount()];
        Integer honus[] = new Integer[room.getPlayerCount()];

        Integer[] chipsArray = new Integer[room.getPlayerCount()];
        //----
        Integer[] canPlayArray = new Integer[room.getPlayerCount()];
        Integer[] passHandArray = new Integer[room.getPlayerCount()];
        Integer[] postingArray = new Integer[room.getPlayerCount()];    // 补盲数组 0否 1补盲玩家
        Integer[] straddleArray = new Integer[room.getPlayerCount()];   // straddle气泡

        Set<Integer> playingUsers = new HashSet<Integer>();
        room.getStraddlePlayers().clear();
        int i = room.getZhuangjiaNumber();
        int cnt = 0;
        while (cnt < room.getPlayerCount()) {
            RoomPersion p = room.getRoomPersions()[i];
            straddleArray[i] = 0;
            if (p != null) {
                playingUsers.add(p.getUserId());
                p.setIsGame(1);
                p.setType(3);
                //p.setAnteCount(0);
                p.setAnteNumber(0);

                chipsArray[i] = p.getNowcounma();
                canPlayArray[i] = 1;
                passHandArray[i] = p.getPassHand();
                postingArray[i] = 0;
                if (p.getStraddleChip() > 0) {
                    if (p.getNowcounma() == 0) {
                        straddleArray[i] = p.getBetChouma() - room.getQianzhu();
                    } else {
                        straddleArray[i] = p.getStraddleChip();
                    }

                } else if (p.getPlayType() == 1) {  // 补盲用户
                    postingArray[i] = 1;
                }
                p.setPlayType(2);   // 设置为正常用户
                p.setGameType(-1);
            } else {
                RoomPersion roomPersion = room.getDdRoomPersions()[i];
                if (roomPersion != null) {  //获取过庄补盲用户积分
                    chipsArray[i] = roomPersion.getNowcounma();
                } else {
                    chipsArray[i] = -1;
                }

                firstCardArray[i] = -1;
                secondCardArray[i] = -1;
                canPlayArray[i] = 0;
                passHandArray[i] = 0;
                postingArray[i] = 0;
            }
            daojishi[i] = -1;
            honus[i] = -1;
            i = (i + 1) % room.getPlayerCount();
            cnt++;
        }

        for (i = 0; i < room.getDdRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getDdRoomPersions()[i];
            if (roomPersion != null) {
                passHandArray[i] = roomPersion.getPassHand();
            }
        }
        int currentPlayerCount = 0;//当前手有手牌的玩家数
        // 发手牌，从小盲位开始，分两轮，一轮发一张
        int start = room.getManzhuNumber();
        int lastSize = -1;
        for (i = 0; i < 2 * room.getRoomPersions().length; i++) {
            int current = (start + i) % room.getRoomPersions().length;
            RoomPersion rp = room.getRoomPersions()[current];
            if (rp != null) {

                Pocer pocer = room.getPocerLink().robotNext(0, 0);
                if (i < room.getRoomPersions().length) {    // 第一张牌
                    rp.setLastSize(lastSize);
                    lastSize = rp.getSize();
//                    if (result.getResultCode() == 1 && result.getUserId() == rp.getUserId()) {
//                        pocer = result.getHandPocer()[0];
//                    }

                    // 控制发牌
                    if (Cache.p.getProperty("cards.control", "0").equals("1")) {
                        pocer = room.getPocerLink().getNext(rp.getUserId(), 0);
                    }

                    if (room.isCardControl()) {
                        pocer = room.getPocerLink().getNext(rp.getUserId(), 0);
                    }

                    //pocer.setResType(1);
                    rp.getPocers()[0] = pocer;
                    firstCardArray[current] = pocer.getSize1();
                    if (firstCardArray[current] != -1) { // 将发过的牌保存到列表，做二次校验，避免发一样的牌
                        sendedCards.add(firstCardArray[current]);
                    }
                } else {    // 第二张牌
//                    if (result.getResultCode() == 1 && result.getUserId() == rp.getUserId()) {
//                        pocer = result.getHandPocer()[1];
//                    }
                    // 控制发牌
                    if (Cache.p.getProperty("cards.control", "0").equals("1")) {
                        pocer = room.getPocerLink().getNext(rp.getUserId(), 1);
                    }

                    if (room.isCardControl()) {
                        pocer = room.getPocerLink().getNext(rp.getUserId(), 1);
                    }

                    //pocer.setResType(1);
                    rp.getPocers()[1] = pocer;
                    secondCardArray[current] = pocer.getSize1();
                    if (secondCardArray[current] != -1) { // 将发过的牌保存到列表，做二次校验，避免发一样的牌
                        sendedCards.add(secondCardArray[current]);
                    }
                    currentPlayerCount++;
                    historyDao.insertSingleCardScore((SingleCardScore.builder().roomId(room.getRoomId()).role(rp.getRole())
                            .liangPai(new ArrayList<>()).brandType(rp.getClientStatus() == 6 ? 0 : 1).card(new ArrayList<>())
                            .userName(rp.getUserInfo().getNikeName()).userId(rp.getUserId()).profit(0).cardeType(0).version(room.getStage()).build()));
                }
            }
        }
        room.setCurrentPlayerCount(currentPlayerCount);

        for (i = 0; i < room.getDdRoomPersions().length; i++) {
            RoomPersion roomPersion = room.getDdRoomPersions()[i];
            if (roomPersion != null) {
                passHandArray[i] = roomPersion.getPassHand();
            }
        }

        logger.debug("canPlayArray: " + Arrays.toString(canPlayArray));
        logger.debug("passHandArray: " + Arrays.toString(passHandArray));
        logger.debug("postingArray: " + Arrays.toString(postingArray));
        logger.debug("straddleArray: " + Arrays.toString(straddleArray));

        // 设置记录，保存手牌
        room.getRoomRecord().initHandPoker(room);

        gameEventService.emitHandCards(room, firstCardArray, secondCardArray);

        byte[] bytes = null;

        Map<Integer, RoomPersion> auds = new HashMap<Integer, RoomPersion>(room.getAudMap());
        Integer[] firstCardArray1 = new Integer[room.getPlayerCount()];
        Integer[] secondCardArray1 = new Integer[room.getPlayerCount()];

        for (Integer key : auds.keySet()) {
            int creditValue = Integer.MAX_VALUE;
            if (auds.get(key) != null) {
                RoomPersion rp = auds.get(key);
                int genzhu = 0;
                Arrays.fill(firstCardArray1, 0, firstCardArray1.length, -1);
                Arrays.fill(secondCardArray1, 0, secondCardArray1.length, -1);
                if (playingUsers.contains(rp.getUserId()) && rp.getSize() >= 0
                        && rp.getSize() < room.getPlayerCount()) {
                    if (room.isCanSeeHandCard()) {
                        // Bug 1568: 开启延迟看牌后出现手牌看不到的bug
                        // 翻牌前，盲抓位剛好被扣除所有剩餘籌碼時，因為不再有操作，導致不能通過協議 44 獲得手牌。
                        // 這時開局便馬上發送手牌給前端。
                        boolean straddleAllin = rp.getNowcounma() == 0 && rp.getSize() == room.getStraddleNumber();
                        if (rp.getSize() == room.getCurrentNumber() || straddleAllin) {//轮到当前操作 需求不发给非当前操作玩家看到手牌
                            rp.setCanSeeHandCard(true);//设置可查看手牌状态
                            firstCardArray1[rp.getSize()] = firstCardArray[rp.getSize()];
                            secondCardArray1[rp.getSize()] = secondCardArray[rp.getSize()];
                        } else {
                            firstCardArray1[rp.getSize()] = -1;
                            secondCardArray1[rp.getSize()] = -1;
                        }
                    } else {
                        firstCardArray1[rp.getSize()] = firstCardArray[rp.getSize()];
                        secondCardArray1[rp.getSize()] = secondCardArray[rp.getSize()];
                    }
                    genzhu = room.getMaxChouma() - rp.getBetChouma();
                }

                int straddleStatus = 2;
                if (room.getStraddleNumber() > -1) {
                    straddleStatus = 0;
                } else {
                    straddleStatus = 1;
                }

                rp.setStraddleChip(0);
                int minJia = room.getMin2JaZhu(rp.getBetChouma() - room.getQianzhu(), 0);
                logger.debug("R-{}:{} U-{}:{} bet={} qianzhu={} minjia={} damanzhu={} current={}",
                        room.getRoomId(), room.getStage(), rp.getUserId(), rp.getUserInfo().getNikeName(),
                        rp.getBetChouma(), room.getQianzhu(), minJia,
                        room.getDamanzhuNumber(), room.getCurrentNumber());
                Object[][] objs = {
                    {60, room.getZhuangjiaNumber(), I366ClientPickUtil.TYPE_INT_1},
                    {61, room.getDamanzhuNumber(), I366ClientPickUtil.TYPE_INT_1},
                    {62, room.getManzhuNumber(), I366ClientPickUtil.TYPE_INT_1},
                    {63, room.getCurrentNumber(), I366ClientPickUtil.TYPE_INT_1},
                    {130, firstCardArray1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                    {131, secondCardArray1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                    {132, room.getManzhu(), I366ClientPickUtil.TYPE_INT_4},
                    {133, room.getDamanzhu(), I366ClientPickUtil.TYPE_INT_4},
                    {134, chipsArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},   // 玩家手上筹码
                    {135, daojishi, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                    {136, honus, I366ClientPickUtil.TYPE_INT_4_ARRAY},
                    {137, rp.getChouma(), I366ClientPickUtil.TYPE_INT_4},        // 玩家剩余筹码
                    {138, room.getMinRate(), I366ClientPickUtil.TYPE_INT_4},    // 房间最小带入倍数
                    {139, room.getMaxRate(), I366ClientPickUtil.TYPE_INT_4},    // 房间最大带入倍数
                    {140, minJia / 2, I366ClientPickUtil.TYPE_INT_4},   // 最小加注金额
                    // {140, 0, I366ClientPickUtil.TYPE_INT_4},   // 最小加注金额
                    {141, 1, I366ClientPickUtil.TYPE_INT_4},                            // 是否能加注
                    {142, canPlayArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},           // 能否打牌
                    {143, passHandArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},          // 0 none 1 toast >0 气泡
                    {144, room.getStage(), I366ClientPickUtil.TYPE_INT_4},                // 牌局第几手
                    {145, postingArray, I366ClientPickUtil.TYPE_INT_1_ARRAY},            // 是否要补盲 0否 1是
                    {146, creditValue, I366ClientPickUtil.TYPE_INT_4},                  // 玩家信用额度值
                    {147, straddleArray, I366ClientPickUtil.TYPE_INT_4_ARRAY},          // straddle玩家
                    {148, straddleStatus, I366ClientPickUtil.TYPE_INT_4},   // 0straddle成功，1straddle失败，2不处理
                    {149, genzhu, I366ClientPickUtil.TYPE_INT_4},
                    {151, room.getCurrentRoomActionTimes(), I366ClientPickUtil.TYPE_INT_4},
                    {152, room.getFirstHandChip() * 2 + room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4},
                    {153, 0, I366ClientPickUtil.TYPE_INT_4},//计算出的差额
                    {154, 0, I366ClientPickUtil.TYPE_INT_4},//上个人的有效下注
                    {155, room.getRoomStatus() == 2 ? 0 : 1, I366ClientPickUtil.TYPE_INT_4},
                    {156, room.getGenZhuCount(), I366ClientPickUtil.TYPE_INT_4},//call的人数
                    {157, room.getQiPaiCount(), I366ClientPickUtil.TYPE_INT_4},//弃牌的人数
                };

                bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_START_INFOR);
                logger.debug("R-{}:{} U-{}:{} 响应数据....協議 27-REQ_GAME_RECV_START_INFOR",
                        room.getRoomId(), room.getStage(), rp.getUserId(), rp.getUserInfo().getNikeName());
                if (logger.isTraceEnabled()) {
                    logger.trace("R-{} U-{} objs={}", room.getRoomId(), rp.getUserId(), I366ClientPickUtil.stringify(objs));
                }
                PublisherUtil.sendByUserId(room, bytes, rp.getUserId());
            }
        }

        for (Integer key : room.getAudMap().keySet()) {
            RoomPersion p = room.getAudMap().get(key);
            if (p != null && p.getOnlinerType() != -1 && p.getStatus() >= 0) {
                if (p.getUserInfo().isPrivilege()) {
                    showCardsAndPrivilegeUser(p.getUserId());
                }
            }
        }
        // 累加每个玩家在本房间的手数
        for (RoomPersion p : room.getRoomPersions()) {
            if (p != null && p.getOnlinerType() != -1 && p.getStatus() >= 0) {
                room.getDealer().addHandCnt(p.getUserId());
            }
        }

        //本轮玩家设置下注的筹码
        //设置相关状态
        room.setRoomStatus(3);//准备第一轮投注
        room.setT3(System.currentTimeMillis());
        logger.info("R-{}:{} beginNewHand .....OK", room.getRoomId(), room.getStage());
        room.roomProcedure.beginStatus3();
    }

    private void logs() {
        logger.error("||||||--------------begin error-----------------||||||");
        try {
            logger.debug("roomId/roomStatus" + room.getRoomId() + "/" + room.getRoomStatus());
            logger.debug("room audMap:");
            for (Integer key : room.getAudMap().keySet()) {
                if (room.getAudMap().get(key) != null) {
                    RoomPersion rp = room.getAudMap().get(key);
                    logger.debug("userId/chip/nowChip/type/status:" + rp.getUserId() + "/" + rp.getChouma() + "/" + rp.getNowcounma() + "/" + rp.getType() + "/" + rp.getStatus());
                }
            }
            logger.debug("roomPersions :");
            for (RoomPersion rp : room.getRoomPersions()) {
                if (rp != null) {
                    logger.debug("userId/chip/nowChip/type/status:" + rp.getUserId() + "/" + rp.getChouma() + "/" + rp.getNowcounma() + "/" + rp.getType() + "/" + rp.getStatus());
                }
            }
        } catch (Exception e2) {
            logger.error("roomThreadTrim err + roomid:" + room.getRoomId(), e2);
        }
        logger.error("||||||--------------2manzhu error-----------------||||||");
    }

    /**
     * 通知玩家操作
     *
     * @param playerId     当前操作玩家编号
     * @param seatId       座位编号
     * @param actionStatus 玩家操作
     * @param anteNumber   下注筹码数
     * @param operationId  下一个下注玩家的座位id
     */
    public int gameAction(int playerId, int seatId, int actionStatus, int anteNumber, int operationId) {
        int canRaise = 1;
        if (operationId != -1 && operationId == room.getAttackId()) {
            canRaise = 0;
        }

        RoomPlayer roomPlayer = room.getRoomPlayers().get(playerId); //玩家每次操作后,需要重置延时次数字段
        if (null != roomPlayer) {
            roomPlayer.setUserDelayTimes(0);
        }

        logger.debug("gameAction: roomid" + room.getRoomId() + "opId: " + operationId + ", minraise: " + room.getMinRaiseNumber() + ", canRaise: " + canRaise + " playerId: " + playerId);
        int genzhu = 0;
        int nextUserGenzhu = 0;

        int opUserId = 0; //下一个操作玩家的userid
        if (operationId != -1) {
            RoomPersion opRoomPerion = room.getRoomPersions()[operationId];
            opUserId = opRoomPerion.getUserId();
            if (room.isCanSeeHandCard()) {
                room.getRoomPersions()[operationId].setCanSeeHandCard(true);//设置玩家可以查看手牌，下手开始前重置
                //给下个操作玩家发手牌数据
                Object[][] showCardObjs = {
                    {60, operationId, I366ClientPickUtil.TYPE_INT_1},        // 座位下标
                    {61, opRoomPerion.getPocers()[0].getSize1(), I366ClientPickUtil.TYPE_INT_1},        // 第一张手牌
                    {62, opRoomPerion.getPocers()[1].getSize1(), I366ClientPickUtil.TYPE_INT_1},       // 第二张手牌
                };

                byte[] showCardBytes = I366ClientPickUtil.packAll(showCardObjs, Constant.REQ_GAME_SHOW_CARD_FOR_GAME_END);
                PublisherUtil.sendByUserId(room, showCardBytes, opUserId);
            }
        }

        if (!AiRuleTemplate.isAiUser(playerId)) {
            // notify human players only
            RabbitMqUtil.sendSns(playerId, EMessageCode.SNS_PLAYER_TURN.getCode());
        }

        logger.debug("opUserId: " + opUserId);
        for (Entry<Integer, RoomPersion> entry : room.getAudMap().entrySet()) {
            int userId = entry.getKey();
            RoomPersion rp = entry.getValue();

            if (operationId != -1) {
                if (rp != null) {
                    genzhu = room.getMaxChouma() - rp.getBetChouma();
                    //与当前操作玩家座位号相同且userid也相同，防止之前有玩家在这个座位上做过导致跟注值计算错误
                    if (rp.getSize() == room.getCurrentNumber() && opUserId == rp.getUserId()) {
                        nextUserGenzhu = genzhu;
                    }
                }
            }
            int betChip = 0;
            RoomPersion persion = null;
            if (operationId != -1) {
                persion = room.getRoomPersions()[operationId];
                if (persion != null) {
                    betChip = persion.getAnteCount();
                }
            }

            int chae = 0;
            int firstAnteCount = 0;
            int minJia = 0;
            int lastBet;
            if (room.getRoomStatus() == 3) {
                if (persion != null) {
                    if (room.getDamanzhuNumber() == persion.getSize()) {
                        firstAnteCount = room.getDamanzhu();
                    } else if (room.getManzhuNumber() == persion.getSize()) {
                        firstAnteCount = room.getManzhu();
                    } else if (room.getStraddleNumber() == persion.getSize()) {
                        firstAnteCount = room.getDamanzhu() + room.getDamanzhu();
                    } else if (room.getBuMangPlay().containsKey(persion.getUserId())) {
                        firstAnteCount = room.getBuMangPlay().get(persion.getUserId());
                    }
                    //抓头算一次加注
                    if ((room.getJiaZhuCount() != 0 && room.getStraddleNumber() == -999) ||
                            (room.getJiaZhuCount() > 1 && room.getStraddleNumber() > -1)) {
                        chae = room.getMin2JaZhu(betChip + firstAnteCount, 0);
                    }
                    minJia = room.getMin2JaZhu(betChip + firstAnteCount, 0);
                }
                lastBet = chae == 0 ? 0 : room.getLastBet();
            } else {
                if (room.getJiaZhuCount() > 1) {
                    chae = room.getMin2JaZhu(betChip, 0);
                }
                minJia = room.getMin2JaZhu(betChip, 0);
                lastBet = -1;
                if (room.getLastBet() * 6 > room.getPoolChip()) {
                    lastBet = chae == 0 ? 0 : room.getLastBet();
                }
            }

            Object[][] objs = {
                {61, seatId, I366ClientPickUtil.TYPE_INT_1}, // 座位ID
                {62, actionStatus, I366ClientPickUtil.TYPE_INT_1}, //
                {64, operationId, I366ClientPickUtil.TYPE_INT_1}, // 下一个操作玩家id
                {130, anteNumber, I366ClientPickUtil.TYPE_INT_4},
                {131, playerId, I366ClientPickUtil.TYPE_INT_4},
                {132, minJia / 2, I366ClientPickUtil.TYPE_INT_4}, // 最小加注筹码
                {133, canRaise, I366ClientPickUtil.TYPE_INT_4}, // 是否可加注
                {134, genzhu, I366ClientPickUtil.TYPE_INT_4}, // 下一个操作玩家跟注筹码数
                {135, (room.getCurrentRoomActionTimes() + 1), I366ClientPickUtil.TYPE_INT_4},
                //{ 136, room.getFirstHandChip()*2+room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4},
                {136, room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4},
                {137, chae, I366ClientPickUtil.TYPE_INT_4},//计算出的差额
                {138, lastBet, I366ClientPickUtil.TYPE_INT_4},//上个人本轮下注值
                {139, room.getRoomStatus() == 3 ? 0 : 1, I366ClientPickUtil.TYPE_INT_4},
                {140, room.getGenZhuCount(), I366ClientPickUtil.TYPE_INT_4},//call的人数
                {141, room.getQiPaiCount(), I366ClientPickUtil.TYPE_INT_4},//弃牌的人数
            };

            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_ACTION);
            PublisherUtil.sendByUserId(room, bytes, userId);
        }

        logger.debug("nextUserGenzhu: " + nextUserGenzhu);
        return nextUserGenzhu;
    }

    /**
     * 发公共牌
     *
     * @param count 发几张
     * @param size  当前操作玩家
     */
    public synchronized void sendCards(int count, int size) {
        sendCards2(count, size);
    }

    /**
     * 发公共牌
     *
     * @param count 发几张
     * @param size  当前操作玩家
     */
    public void sendCards2(int count, int size) {
        logger.debug("sendCards2 roomId={} count={} size={} insuranceActive={}", room.getRoomId(), count, size, room.getInsuranceActive());
        if (count == 0) {
            return;
        }

        Integer[] systemCards = new Integer[count];
        int onSize = -1; // which slot to begin dealing
        for (int i = 0; i < room.getPocer().length; i++) {
            if (room.getPocer()[i] == null) {
                onSize = i;
                break;
            }
        }

        if (onSize == -1) {
            return;
        }

//        room.getVigilante().onBeforeSendCard(count);

        //IUserWeightService userWeightService = new UserWeightServiceImpl(room);
        //FlopInterveneResult flopInterveneResult = new FlopInterveneResult();
//        if (count == 3) {
//            try {
//                flopInterveneResult = userWeightService.doFlopIntervene();
//            } catch (Exception e) {
//                logger.error("翻牌干预异常", e);
//            }
//        }

        for (int i = 0; i < count; i++) {
            Pocer p;
            if (room.getInsurance() == 1 && count == 1 && room.getInsuranceActive() && room.getRoomStatus() == 7) {
                p = room.getPocerLink().insureNext(room.getDealer().getHolderPool(), room.isCardControl() || room.getVigilante().isThreatNeutralized(), sendedCards, room);
            } else {
                do {
                    if (!room.getControlPublicCards().isEmpty() && room.getControlPublicCards().get(0) != null) {
                        p = room.getPocerLink().robotNext(4, room.getControlPublicCards().get(0));
                        room.getControlPublicCards().remove(0);
                    } else {
                        p = room.getPocerLink().robotNext(0, 0);
                    }
                } while (sendedCards.contains(p.getSize1())); //发现获取的牌已经发了就继续找下一张
            }

            /** 发牌规则优先级  控制发牌>加权>保险爆牌 **/
//            if (room.getRoomStatus() > 3 && count == 1) {
//                try {
//                    AfterFlopInterveneResult afterFlopInterveneResult = userWeightService.doAfterFlopIntervene();
//                    if (afterFlopInterveneResult.getResultCode() == 1) {
//                        p = afterFlopInterveneResult.getHandPocer()[0];
//                        logger.debug("满足干预翻后发牌规则,干预后的公共牌={}", afterFlopInterveneResult.getHandPocer()[0].getSize1());
//                    }
//                } catch (Exception e) {
//                    logger.error("翻后干预异常", e);
//                }
//            }

//            if (count == 3) {
//                if (flopInterveneResult.getResultCode() == 1) {
//                    p = flopInterveneResult.getHandPocer()[i];
//                }
//            }

            // 指定发牌
            if (Cache.p.getProperty("cards.control", "0").equals("1")) {
                p = room.getPocerLink().getPublic(i + onSize);
            }

            if (room.isCardControl()) {
                p = room.getPocerLink().getPublic(i + onSize);
            }

            room.getPocer()[i + onSize] = p;
            systemCards[i] = room.getPocer()[i + onSize].getSize1();
            logger.debug("sendCards2 card={} sendedCards={}", systemCards[i], sendedCards);
            if (sendedCards.contains(systemCards[i])) { // 发现有相同的牌，解散
                room.roomProcedure.forceCloseRoom = true;
                logger.debug("发现有相同的牌，解散房间,rid={},now={}", room.getRoomId(), LocalDateTime.now());
                room.roomProcedure.timesup(RoomFinishCode.SAME_CARDS_FINISH);
                return;
            } else {
                sendedCards.add(systemCards[i]);
            }
        }

        if (room.getPocer()[4] != null) {
            if (room.getRoomReplay().getAnteActions()[3] == null) {
                room.getRoomReplay().setAnteAction(3, new AnteAction());
                room.getRoomReplay().getAnteActions()[3].setPot(room.getPoolChip());
            }
            room.getRoomReplay().getAnteActions()[3].setCard(String.valueOf(room.getPocer()[4].getSize1()));
        } else if (room.getPocer()[3] != null) {
            if (room.getRoomReplay().getAnteActions()[2] == null) {
                room.getRoomReplay().setAnteAction(2, new AnteAction());
                room.getRoomReplay().getAnteActions()[2].setPot(room.getPoolChip());
            }
            room.getRoomReplay().getAnteActions()[2].setCard(String.valueOf(room.getPocer()[3].getSize1()));
        } else if (room.getPocer()[2] != null) {
            if (room.getRoomReplay().getAnteActions()[1] == null) {
                room.getRoomReplay().setAnteAction(1, new AnteAction());
                room.getRoomReplay().getAnteActions()[1].setPot(room.getPoolChip());
            }
            String card = room.getPocer()[0].getSize1() + " " + room.getPocer()[1].getSize1() + " "
                    + room.getPocer()[2].getSize1();
            room.getRoomReplay().getAnteActions()[1].setCard(card);
        }

        int canRaise = 1;
        if (size != -1 && room.getAttackId() == size) {
            canRaise = 0;
        }

        // 打个日志
//        StringBuilder sysCardStr = new StringBuilder();
//        for (int j = 0; j < systemCards.length; j++) {
//            sysCardStr.append(systemCards[j]).append(",");
//        }

        Object[][] objs = {
            {60, size, I366ClientPickUtil.TYPE_INT_1},
            {130, systemCards, I366ClientPickUtil.TYPE_INT_1_ARRAY},
            {131, room.getMinRaiseNumber() / 2, I366ClientPickUtil.TYPE_INT_4},
            {132, canRaise, I366ClientPickUtil.TYPE_INT_4},
            {135, (room.getCurrentRoomActionTimes() + 1), I366ClientPickUtil.TYPE_INT_4},
            {136, room.getFirstHandChip() * 2 + room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4},
            {137, room.getRoomStatus() == 3 ? 0 : 1, I366ClientPickUtil.TYPE_INT_4},//计算出的差额
            {138, room.getGenZhuCount(), I366ClientPickUtil.TYPE_INT_4},//call的人数
            {139, room.getQiPaiCount(), I366ClientPickUtil.TYPE_INT_4},//弃牌的人数
        };
        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_CARDS);
        PublisherUtil.send(room, bytes);

        for (RoomPersion roomPersion : room.getRoomPersions()) {
            if (null != roomPersion) {
                //记录牌谱
                if (count == 3) {
                    historyDao.updateSingleCardScore(SingleCardScore.builder().roomId(room.getRoomId()).role(roomPersion.getRole())
                    .userName(roomPersion.getUserInfo().getNikeName()).profit(0).cardeType(0).version(room.getStage()).build());
                } else {
                    historyDao.updateSingleCardScore(SingleCardScore.builder().roomId(room.getRoomId()).userName(roomPersion.getUserInfo().getNikeName()).
                    role(roomPersion.getRole()).cardeType(roomPersion.getClientStatus() == 6 ? 1 : 0).version(room.getStage()).build());
                }
                if (roomPersion.getClientStatus() != 4 && roomPersion.getClientStatus() != 6) { //非allin 弃牌玩家在新一轮需要重置下注状态(客户端需要根据该字段更新气泡状态)
                    roomPersion.setClientStatus(1);
                }
            }
        }


        List<Integer> publicCard = Arrays.stream(room.getPocer()).filter(Objects::nonNull).map(Pocer::getSize1).collect(Collectors.toCollection(LinkedList::new));

        historyDao.insertGameProcess(GameProcess.builder().roomId(room.getRoomId()).shoushuCode(room.getRoomStatus() - 2).publicCard(publicCard).chip(room.getPoolChip())
                .users(room.getCurrentPlayerCount()).singleProcess(new ArrayList<>()).version(room.getStage()).build());

        gameEventService.emitPublicCards(room, publicCard);
    }

    /**
     * 玩家花钻石翻底牌
     *
     * @param count  需要翻的底牌个数
     * @param userId
     */
    public int showCardsByUser2(int count, int userId) {
        if (count == 0) {
            return 1;
        }
        Integer[] systemCards = new Integer[count];
        int onSize = -1;
        for (int i = 0; i < room.getPocer().length; i++) {
            if (room.getPocer()[i] == null) {
                onSize = i;
                break;
            }
        }

        if (onSize == -1) {
            return 1;
        }
        for (int i = 0; i < count; i++) {
            Pocer p = room.getPocerLink().robotNext(-1, 0);
            // 指定发牌
            if (Cache.p.getProperty("cards.control", "0").equals("1")) {
                p = room.getPocerLink().getPublic(i + onSize);
            }

            if (room.isCardControl()) {
                p = room.getPocerLink().getPokerControl().getShowPokers()[i + onSize];
            }

            room.getPocer()[i + onSize] = p;
            room.getPocerType()[i + onSize] = 1;    // 底牌类型为1：玩家花钻石发牌
            systemCards[i] = room.getPocer()[i + onSize].getSize1();
        }

        // 打个日志
        logger.debug("returned new card: " + Arrays.toString(systemCards) + " by " + userId);
        RoomPersion rp = room.getAudMap().get(userId);
        if (null != rp && null != rp.getUserInfo()) {
            String userName = rp.getUserInfo().getNikeName();
            for (Integer key : room.getAudMap().keySet()) {
                String notiflyName = userName;
                if (room.getAudMap().get(key) != null) {
                    rp = room.getAudMap().get(key);
                    UserInfo userInfo = rp.getUserInfo();
                    if (userInfo == null) {
                        logger.error("err-----send err userId/userRoomId is : " + rp.getUserId() + " this roomId is:"
                                + room.getRoomId());
                        continue;
                    }

                    UserInfo userInfo1 = Cache.getOnlineUserInfo(rp.getUserId(), room.getRoomId());
                    if (userInfo1 != null && rp.getOnlinerType() == 1) {
                        logger.debug("return userName=" + notiflyName);
                        Object[][] objs = {
                            {60, -1, I366ClientPickUtil.TYPE_INT_1},
                            {130, systemCards, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                            {131, room.getMinRaiseNumber() / 2, I366ClientPickUtil.TYPE_INT_4},
                            {132, 0, I366ClientPickUtil.TYPE_INT_4},
                            {133, notiflyName, I366ClientPickUtil.TYPE_STRING_UTF16},
                            {135, (room.getCurrentRoomActionTimes() + 1), I366ClientPickUtil.TYPE_INT_4},
                            {136, room.getFirstHandChip() * 2 + room.getPoolChip(), I366ClientPickUtil.TYPE_INT_4},
                            {137, room.getRoomStatus() == 3 ? 0 : 1, I366ClientPickUtil.TYPE_INT_4},//计算出的差额
                            {138, room.getGenZhuCount(), I366ClientPickUtil.TYPE_INT_4},//call的人数
                            {139, room.getQiPaiCount(), I366ClientPickUtil.TYPE_INT_4},//弃牌的人数
                    };
                    logger.debug("showCardsByUser2===> currentActionTimes===>" + (room.getCurrentRoomActionTimes() + 1));
                        byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_CARDS);
                        PublisherUtil.publisher(userInfo, bytes);
                    } else {
                        logger.error("send err userId is : " + rp.getUserId()
                                + ", onlinerType is : " + rp.getOnlinerType()
                                + ", this roomId is : " + room.getRoomId());
                    }
                }
            }
            return 0;
        } else {
            logger.debug(userId + " is not in AudMap ???");
            return 1;
        }
    }

    /**
     * 玩家花钻石翻赢家手牌
     *
     * @param count  需要翻的底牌个数
     * @param userId
     */
    public int showCardsBWinyUser2(int count, int userId) {
        logger.info("showCardsBWinyUser2 翻看赢家手牌:{},{}", count, userId);
        if (count == 0) {
            return 1;
        }
        UserInfo userInfo = room.getAudMap().get(userId).getUserInfo();
        room.getAudMap().forEach((userIds, rp) -> {
            logger.info("所有玩家数据:{}，rp数据:{}", userIds, rp.getYq());
            if (rp.getYq() >= 0 || room.getWinWithoutInsuranceUserIds().contains(rp.getUserId())) {
                // 牌谱
                Integer[] poker = new Integer[2];
                poker[0] = rp.getPocers()[0].getSize1();
                poker[1] = rp.getPocers()[1].getSize1();
                logger.info("赢家数据:{},sitNum:{},牌局:{},{}", rp.getUserInfo().getNikeName(), rp.getSize(), poker[0], poker[1]);
                Object[][] showCardObjs = {
                    {60, rp.getSize(), I366ClientPickUtil.TYPE_INT_1},        // 座位下标
                    {61, poker[0], I366ClientPickUtil.TYPE_INT_1},        // 第一张手牌
                    {62, poker[1], I366ClientPickUtil.TYPE_INT_1},       // 第二张手牌
            };
            byte[] showCardBytes = I366ClientPickUtil.packAll(showCardObjs, Constant.REQ_GAME_SHOW_CARD_FOR_GAME_END);
                PublisherUtil.send(room, showCardBytes, userIds);
            }
        });
        return 0;
    }

    public static UserInfo setUserChannel(Request request, int roomId) {
        UserInfo userInfo = Cache.getOnlineUserInfo(request.getUserId(), roomId);
        if (userInfo != null) {
            userInfo.setOnlineTime(System.currentTimeMillis());
            userInfo.setChannel(request.getChannel());
        }
        return userInfo;
    }


    /**
     * 玩家超时操作
     * 心跳检测机制 20秒轮询玩家是否掉线  判断标准玩家上次在线时间与现在时间是否大于60秒
     * 检测到玩家掉线
     * 在游戏中
     * 未托管状态   先托管
     * 托管
     * 是否开启最短上桌时间
     * 开启最短上桌时间
     * 是否已经达到最短上桌时间
     * 是 离开
     * 否 不需要离开
     * 未开启最短上桌时间
     * 是否处于allin 跟注 加注状态
     * 是  不需要离开，要先进行比牌
     * 否  离开
     * 不在游戏  离开
     *
     * @param userInfo
     * @param type     1:用户离线 2:用户被踢
     */
    public boolean userTimeOut2(UserInfo userInfo, int type) {
        if (room != null) {
            RoomPersion persion = room.getAudMap().get(userInfo.getUserId());
            if (persion != null) {
                logger.debug("time out person " + persion.getUserId());
                boolean leaving = false;

                if (type == 1) {
                    int leftTime = (int) RoomAutoOp.needAutoOp(room, persion.getUserId());  //返回该牌局的最短打牌时间
                    boolean isPlaying = false;
                    if (persion.getSize() >= 0 && persion.getSize() < room.getPlayerCount()
                            && room.getRoomPersions()[persion.getSize()] != null) {
                        isPlaying = true;
                    }

                    if (room.getRoomStatus() < 2) {  //房间都不在游戏中时，该玩家肯定不处于打牌状态，需要将其移除在线玩家集合
                        isPlaying = false;
                    }

                    logger.debug(" isPlaying: " + isPlaying + " leftTime: " + leftTime + " isautoOp: " + persion.isAutoOp() + " status: " + persion.getStatus());
                    if (isPlaying) { // 正在打牌
                        if (!persion.isAutoOp()) {  // 非托管状态设置成托管状态
                            RoomAutoOp.setUserAutoOp(room, persion, true, true);
                            Integer[] opStatus = RoomAutoOp.autoOpStatus(room);
                            // 通知其他人
                            PublisherUtil.send(room, RoomAutoOp.autoOpPus(0, 5, 0, false, false,
                                    opStatus, ""), persion.getUserId());
                            // 通知自己
                            PublisherUtil.sendByUserId(room, RoomAutoOp.autoOpPus(0, 1, leftTime, false,
                                    false, opStatus, ""), persion.getUserId());

                            return false;
                        } else {
                            if (room.getMinPlayTime() > 0) {  //开启最短上桌时间
                                if (leftTime <= 0) {
                                    leaving = true;
                                } else {
                                    logger.debug(" user didn't play enough time, leftTime: " + leftTime);
                                }
                            } else {
                                if (leftTime <= 0 && (persion.getStatus() == 3 || persion.getStatus() == 2)) { //托管状态下,超过最短打牌时间则直接离开 leftTime=0时,表示该房间未开启最短上桌时间
                                    logger.debug(" user all in status now need to bipai!!!");
                                    return false;
                                } else {
                                    leaving = true;
                                }
                            }
                        }

                    } else {
                        leaving = true;
                    }
                } else {
                    leaving = true;
                }
                if (leaving) {
                    RoomPlayer roomPlayer = room.getRoomPlayers().get(persion.getUserId());
                    roomPlayer.setAheadLeave(1);
                    likai2(persion, LeaveRoomCode.TIMEDOUT_LEAVE);
                }
            }
        }
        return true;
    }

    /**
     * 玩家超时操作
     *
     * @param userInfo
     */
    public synchronized boolean userTimeOut(UserInfo userInfo) {
        logger.debug("user online time out");
        return userTimeOut2(userInfo, 1);
    }

    /**
     * 暂停房间
     */
    public synchronized void pauseRoom() {
        if (!room.getIsPause()) {
            room.setIsPause(true);
            room.setPauseBeginTime(System.currentTimeMillis());
            room.setLastRoomStatus(room.getRoomStatus());    // 记录房间当前状态
            room.setRoomStatus(8);
            room.setPauseUserLeftOpTime((room.getOpTime() - 3) * 1000 - (System.currentTimeMillis() - room.getT3()));

            // 超时任务
            Map<Integer, Object> map = new HashMap<Integer, Object>();
            map.put(1, room.getPauseBeginTime());
            Task pauseTask = new Task(TaskConstant.TASK_PAUSE_ROOM, map, room.getRoomId(), room.getRoomPath());
            WorkThreadService.submitDelayTask(room.getRoomId(), pauseTask, Constant.PAUSE_TIME);

            // 暂停延时消息
            room.roomProcedure.pauseDelayTask();
        }
    }

    /**
     * 关闭暂停
     */
    public synchronized void continueRoom() {
        if (room.getIsPause()) {
            room.setIsPause(false);
            // 累计暂停时间
            room.setPauseTime(System.currentTimeMillis() - room.getPauseBeginTime());
            room.setPauseBeginTime(0);
            room.setRoomStatus(room.getLastRoomStatus());    // 恢复房间状态

            // 恢复最短打牌时间
            if (room.getMinPlayTime() > 0) {
                for (RoomPersion rp : room.getRoomPersions()) {
                    if (rp != null) {
                        long leftTime = RoomAutoOp.needAutoOp(room, rp.getUserId());
                        if (leftTime > 0) {
                            RoomPlayer roomPlayer = room.getRoomPlayers().get(rp.getUserId());
                            roomPlayer.setDownTime(roomPlayer.getDownTime() + room.getPauseTime() / 1000);
                        }
                    }
                }
            }

            // 继续延时任务
            room.roomProcedure.continueDelayTask();
        }
    }

    /**
     * 展示用户牌
     */
    public void showCards() {
        Integer[] playerSeats = new Integer[room.getPlayerCount()];
        Integer[] firstCards = new Integer[room.getPlayerCount()];
        Integer[] secondCards = new Integer[room.getPlayerCount()];
        int playingNum = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            // 需要比牌玩家
            if (rp != null && rp.getOnlinerType() != -1 && rp.getStatus() != -2
                    && rp.getStatus() != -3) {
                playerSeats[playingNum] = i;
                firstCards[playingNum] = rp.getPocers()[0].getSize1();
                secondCards[playingNum] = rp.getPocers()[1].getSize1();
                playingNum++;
            }
        }
        logger.debug("show cards player number: " + playingNum);
        if (playingNum > 0) {
            Integer[] playerSeats1 = new Integer[playingNum];
            Integer[] firstCards1 = new Integer[playingNum];
            Integer[] secondCards1 = new Integer[playingNum];
            System.arraycopy(playerSeats, 0, playerSeats1, 0, playingNum);
            System.arraycopy(firstCards, 0, firstCards1, 0, playingNum);
            System.arraycopy(secondCards, 0, secondCards1, 0, playingNum);
            logger.debug("playerSeats1:" + Arrays.toString(playerSeats1));
            logger.debug("firstCards1:" + Arrays.toString(firstCards1));
            logger.debug("secondCards1:" + Arrays.toString(secondCards1));
            Object[][] objs = {
                {130, playerSeats1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                {131, firstCards1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                {132, secondCards1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
            };
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_AHEAD_SHOW_CARDS);
            PublisherUtil.send(room, bytes);
        }
    }

    /**
     * 作弊用户新增方法展示用户牌
     */
    public void showCardsAndPrivilegeUser(int userId) {
        Integer[] playerSeats = new Integer[room.getPlayerCount()];
        Integer[] firstCards = new Integer[room.getPlayerCount()];
        Integer[] secondCards = new Integer[room.getPlayerCount()];
        int playingNum = 0;
        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            // 需要比牌玩家
            if (rp != null && rp.getOnlinerType() != -1) {
                playerSeats[playingNum] = i;
                firstCards[playingNum] = rp.getPocers()[0].getSize1();
                secondCards[playingNum] = rp.getPocers()[1].getSize1();
                playingNum++;
            }
        }
        if (playingNum > 0) {
            Integer[] playerSeats1 = new Integer[playingNum];
            Integer[] firstCards1 = new Integer[playingNum];
            Integer[] secondCards1 = new Integer[playingNum];
            System.arraycopy(playerSeats, 0, playerSeats1, 0, playingNum);
            System.arraycopy(firstCards, 0, firstCards1, 0, playingNum);
            System.arraycopy(secondCards, 0, secondCards1, 0, playingNum);
            logger.debug("playerSeats1:" + Arrays.toString(playerSeats1));
            logger.debug("firstCards1:" + Arrays.toString(firstCards1));
            logger.debug("secondCards1:" + Arrays.toString(secondCards1));
            Object[][] objs = {
                {130, playerSeats1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                {131, firstCards1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
                {132, secondCards1, I366ClientPickUtil.TYPE_INT_1_ARRAY},
            };
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_AHEAD_SHOW_CARDS);
            PublisherUtil.sendByUserId(room, bytes, userId);
        }
    }

    /**
     * 边池信息数据
     *
     * @return
     */
    public Integer[] getPoolInfo(boolean nextRound) {
        ArrayList<PoolChip> poolList = getPoolList(nextRound);
        Integer[] poolArray = new Integer[poolList.size()];
        String str = "pool info:";
        for (int i = 0; i < poolList.size(); i++) {
            PoolChip subPool = poolList.get(i);
            poolArray[i] = subPool.getTotalChips();
            str += subPool.getTotalChips() + " ";
        }
        logger.debug(str);

        return poolArray;
    }

    /**
     * 获取分池列表
     *
     * @param nextRound
     * @return
     */
    public ArrayList<PoolChip> getPoolList(boolean nextRound) {
        // logger.debug("nextRound:" + (nextRound == true ? 1 : 0));
        ArrayList<Integer[]> betChipsArray = new ArrayList<Integer[]>();
        for (RoomPersion p : room.getRoomPersions()) {
            if (p != null) {
                Integer[] userInfo = new Integer[3];
                userInfo[0] = p.getUserId();
                if (!nextRound) {
                    int mangzhu = 0;
                    if (room.getRoomStatus() == 3) {
                        int qianzhu = room.getQianzhu();
                        if (p.getSize() == room.getManzhuNumber()) {
                            mangzhu = room.getManzhu() + qianzhu;
                        } else if (p.getSize() == room.getDamanzhuNumber()) {
                            mangzhu = room.getDamanzhu() + qianzhu;
                        } else if (p.getSize() == room.getStraddleNumber()) {
                            mangzhu = p.getStraddleChip() + qianzhu;
                        } else if (room.getBuMangPlay().containsKey(p.getUserId())) {
                            mangzhu = room.getBuMangPlay().get(p.getUserId()) + qianzhu;
                        }
                    }
                    userInfo[1] = p.getBetChouma() - (p.getAnteCount() + mangzhu);
                } else {
                    userInfo[1] = p.getBetChouma();
                }
                userInfo[2] = p.getStatus() > 0 ? 1 : 0;
                betChipsArray.add(userInfo);
            }
        }

        Map<Integer, StandUpUserInfo> standUps = room.getStandUpUserInfo();
        if (!standUps.isEmpty()) {
            for (Integer userId : standUps.keySet()) {
                StandUpUserInfo standUpUserInfo = standUps.get(userId);
                Integer[] userInfo = new Integer[3];
                userInfo[0] = userId;
                userInfo[1] = standUpUserInfo.getLeftChip();
                userInfo[2] = 0;
                betChipsArray.add(userInfo);
            }
        }

        PoolInfo poolInfo = new PoolInfo();
        return poolInfo.getPoolList(betChipsArray);
    }

    /**
     * 本轮结束发送分池数据
     */
    public void sendPoolInfo() {
        Object[][] obj = {
                {130, getPoolInfo(true), I366ClientPickUtil.TYPE_INT_4_ARRAY}
        };
        byte[] bytes3 = I366ClientPickUtil.packAll(obj, Constant.REQ_REQUEST_POOLINFO);
        PublisherUtil.send(room, bytes3);
    }


    /**
     * 房间解散
     */
    public void forceStopRoom(RoomFinishCode roomFinishCode) {
        room.roomProcedure.forceCloseRoom = true;
        if (room.getRoomStatus() <= 1) {
            room.roomProcedure.timesup(roomFinishCode);
        }
    }

    /**
     * 玩家花钻石翻底牌
     *
     * @param count  底牌个数
     * @param userId 玩家id
     * @return
     */
    public int showCardsByUser(int count, int userId) {
        return showCardsByUser2(count, userId);
    }

    /**
     * 玩家花钻石 看赢家手牌
     *
     * @param count  底牌个数
     * @param userId 玩家id
     * @return
     */
    public int showCardsByWinUser(int count, int userId) {
        return showCardsBWinyUser2(count, userId);
    }

    /**
     * 更新房间游戏时长
     *
     * @param time
     * @return
     */
    public boolean addTime(Integer time) {
        if (time % 30 == 0 && time > 0 && time / 30 <= 4) {
            RoomDao roomDao = new RoomDaoImpl();
            if (roomDao.updateRoomMaxPlayTime(room.getRoomId(), time)) {
                room.setMaxPlayTime(room.getMaxPlayTime() + time);

                /**
                 * 停掉之前的结束延时任务并且重新开启一个新的牌局结束任务
                 */
                for (String taskId : room.roomProcedure.delayTaskMap.keySet()) {
                    Task dispatchTask = (Task) room.roomProcedure.delayTaskMap.get(taskId);

                    if (TaskConstant.TASK_ROOM_TIMESUP == dispatchTask.getTaskId()) {
                        dispatchTask.getTaskFuture().cancel(true);//停掉该线程任务
                        dispatchTask.setValid(false);
                    }
                }

                room.setNewGameOverTask();

                RoomSearchDao roomSearchDao = new RoomSearchDaoImpl();
                int updateFlag = roomSearchDao.delayRoomPlayTime(room.getRoomId(), time * 60);

                logger.debug("delayRoomPlayTime updateFlag, " + updateFlag);

                return true;
            }

        }
        return false;
    }


    /**
     * 新建一个某玩家的离座留座延时任务
     *
     * @param userId
     */
    public int newOccupySeatTask(int userId, long times) {

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        if (null != roomPlayer) {
            Map<Integer, Object> map = new HashMap<Integer, Object>();
            map.put(1, userId);
            map.put(2, roomPlayer.getSeat());
            Task task = new Task(TaskConstant.TASK_OCCUPY_SEAT, map, room.getRoomId(), room.getRoomPath(), userId); // 20241118 add user id
            logger.debug("new a task10018=" + task.getId() + " to occupy seat right now");
            // 设置该玩家最新的有效任务id，让该玩家的其他10018任务失效
            roomPlayer.setValidTask10018Id(task.getId());
            roomPlayer.setSeatStatus(3);
            roomPlayer.setOccupyTime((int) (System.currentTimeMillis() / 1000));

            WorkThreadService.submitDelayTask(room.getRoomId(), task, times);

            // 将占座玩家的信息保存到对应room
            room.getOccupySeatPlayers().put(userId, roomPlayer);
        } else {
            return -1;
        }

        return 0;
    }

    /**
     * 取消玩家的留桌离桌状态
     *
     * @param userId
     * @param status 0 站起状态 1 坐下打牌状态 2 即将留座状态 4占座状态
     * @return 0 成功，1失败
     */
    public int cancelOccupySeat(int userId, int status) {

        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        if (null != roomPlayer) {
            logger.debug("cancelOccupySeat userid={}, set seat staus={}", userId, status);
            roomPlayer.setSeatStatus(status);
            if (null != room.getOccupySeatPlayers()) {
                room.getOccupySeatPlayers().remove(userId);
            }
            return 0;
        }
        return 1;
    }

    /**
     * 通知房间里所有人，某个玩家不再占座了
     *
     * @param userId
     */
    public void notifyOccupyUserStand(int action, int userId) {
        RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
        // 判断该玩家是否在留座离桌状态
        if (null != roomPlayer) {
            int seat = roomPlayer.getSeatSize();
            int nowChip = roomPlayer.getNowChipWhenOccupySeat();
            int totalChip = roomPlayer.getChouma();
            logger.debug("notifyOccupyUserStand do not occupy seat,userid={},seat={},nowChouma={}", userId, seat, nowChip);
            if (null != room.getAudMap().get(userId)) {// 占座玩家还在房间时，将其变成观察者
                room.getDealer().addWatcher(userId);
            }
            //更新玩家座位号 此时roomPersion已经为null
            roomPlayer.setSeat(-1);
            roomPlayer.setSeatSize(-1);

            RoomPersion roomPersion = room.getAudMap().get(userId);
            if (roomPersion != null) {
                roomPersion.setType(1);  //要将该用户的状态设置为站起状态
                roomPersion.setSize(-1);
            }

            //  通知别的玩家有人起来了
            Object[][] objs = {
                    {60, seat, I366ClientPickUtil.TYPE_INT_1},
                    {130, userId, I366ClientPickUtil.TYPE_INT_4}};
            byte[] bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_LEAVE);
            PublisherUtil.send(room, bytes, userId);

            // 通知本人站起成功
            Object[][] objs2 = {
                {60, 0, I366ClientPickUtil.TYPE_INT_1},
                {61, action, I366ClientPickUtil.TYPE_INT_1},
                {130, nowChip, I366ClientPickUtil.TYPE_INT_4},
                {131, totalChip, I366ClientPickUtil.TYPE_INT_4}
            };
            bytes = I366ClientPickUtil.packAll(objs2, Constant.REQ_GAME_SEND_SEAT_ACTION);
            PublisherUtil.sendByUserId(room, bytes, userId);

            int handTotal = room.getDealer().getHandCnt(userId); //玩家已经打的手数
            room.getRoomSeatChangeService().checkSeatChangeTpye(room.getRoomId(), userId, room.getStage(), handTotal, RoomSeatChangeCode.STANDUP_ROOM);

        }
    }

    /**
     * 设置攻击者
     *
     * @param roomPersion
     */
    public void setAttacker(RoomPersion roomPersion, int anteChip) {
        if (roomPersion.getBetChouma() >= room.getMaxChouma()) { // 判断攻击者(CBET适用)
            room.setAttackId(roomPersion.getSize());
            logger.debug("set attack rp size:" + roomPersion.getSize());
            // 判断盖牌攻击者(河牌触发)
            if (room.getRoomStatus() == 6) {
                room.setMuckAttackId(roomPersion.getSize());
                logger.debug("set muck attack rp size:" + roomPersion.getSize());
            }

            if (room.getIsfirstAfNormal() == -1) { //每一轮第一个激进的玩家
                if (!AiRuleTemplate.isAiUser(roomPersion.getUserId())) {
                    room.setIsfirstAfNormal(1);
                    room.setFirstAfBetChip(anteChip);
                } else {
                    room.setIsfirstAfNormal(0);
                }
            }
        }
    }

    /**
     * 单独处理那些中途离开的玩家
     */
    public void processLeftUsers() {
        RoomPlayer roomPlayer = null;
        RoomPersion persion = null;

        for (Integer userId : room.getRoomPlayers().keySet()) {
            roomPlayer = room.getRoomPlayers().get(userId);
            if (null != roomPlayer && roomPlayer.hasLeft()) {
                int seatId = roomPlayer.getSeat();
                if (seatId > -1 && seatId < room.getPlayerCount()) {
                    persion = room.getRoomPersions()[seatId];
                    if (persion == null || persion.getOnlinerType() == -1) {
                        persion = room.getDdRoomPersions()[seatId];
                    }
                    if (null != persion) {
                        likai2(persion, LeaveRoomCode.NEXT_GAME_LEAVE); // 调用之前的离开逻辑
                    } else {
                        logger.debug("user=" + userId + " on seat=" + seatId + " has left early");
                    }
                    roomPlayer.setHasLeft(false);
                    roomPlayer.setSeat(-1);
                }
            }
        }
        // 更改RoomPlayerList
        String roomPlayerList = processRoomPlayerList();
        logger.debug("RoomPlayerList,RoomId={}, UserId={}, RoomPath={}", room.getRoomId(), roomPlayerList, room.getRoomPath());
        RedisService.getRedisService().modifyRoomPlayerList(room.getRoomId(), room.getRoomPath(), roomPlayerList);
    }

    public boolean checkAdmin(Integer clubId, Integer userId) {
        return roomDao.checkAdminByClubIdAndUserId(clubId, userId);
    }

    public String processRoomPlayerList(){
        Map<Integer, Map<String, Integer>> list = new HashMap<Integer, Map<String, Integer>>();
        for(Integer userId : room.getRoomPlayers().keySet()) {
            Map<String, Integer> p = new HashMap<String, Integer>();
            RoomPlayer player = room.getRoomPlayers().get(userId);
            p.put("seat", player.getSeat());
            p.put("seatStatus", player.getSeatStatus());
            list.put(userId,p);
        }
        String json = JSON.toJSONString(list);

        return json;
    }

    public void settleAheadLeave() {
        List<Player> players = room.getRoomPlayers().values().stream()
                .filter(rp -> rp.getAheadLeave() == 1 && !rp.isSettlement())
                .map(rp -> room.getDealer().getPlayers().get(rp.getUserId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (players.isEmpty()) {
            logger.debug("no players aheadLeave");
            return;
        }

        for (Player player : players) {
            int userId = player.getUserId();

            RoomPersion rp = room.getAudMap().get(userId);
            if (rp != null && rp.getRequestChip() > 0) { //玩家有未计算的带入需要加上
                RoomRequest.bindUserChip(rp, rp.getRequestChip(), room);
                rp.setRequestChip(0);
            }

            int bringIn = player.getBringIn();
            int pl = player.getEarn();
            int feeLocked = room.getFeeManager().calculateFeeLocked(pl);

            logger.info("提前结算数据,数据详情：userid:{},pl:{},bringIn:{},feeLocked:{}", userId, pl, bringIn, feeLocked);

            room.getTiqianjiesunSet().add(userId);

            try (Connection conn = DBUtil.getConnection()) {
                int sum = pl + bringIn - feeLocked;
                if (room.getClubRoomType() == 0) { // 大厅
                    room.getRoomService().updateUserInfo(userId, ChipsCode.USER_AHEAD_LEAVE);  //更新玩家信息
                } else {
                    LockedActuator.withLock(() -> {
                        saveAheadSettlement(room.getRoomId(), player.getClubId(), userId, sum, conn);
                    }, RedisDistributedLockGenerator.generate(
                            RedisLockConfigCode.ROOM_SETTLEMENT_USER,
                            RedisLockKeyGenerator.generateUserKey(userId)
                    ));
                }
                RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);
                roomPlayer.setSettlement(true);  //设置为已经结算过
                roomPlayer.setSettledSum(sum);
                roomPlayer.setFeeLocked(feeLocked);
                RedisService.getRedisService().addRoomSettlementSet(userId, room.getRoomId());
            } catch (SQLException ex) {
                logger.error("settle ahead leave failed: " + ex.getMessage(), ex);
            }

            // 发送渠道币类处理消息
            BusinessMessageSender.channelCoinsRecycling(room.getRoomId(), userId, player.getClubId(), player.getTribeId());
        }
    }

    private void saveAheadSettlement(int roomId, int clubId, int userId, int sum, Connection conn) throws SQLException {
        if (sum != 0) {
            String txUuid = UUID.randomUUID().toString();
            UserBalanceAuditLog bringOutLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .userId(userId)
                    .source(UserBalanceAuditLog.Source.ROOM.getValue())
                    .operatorId(-1)
                    .roomId(roomId)
                    .clubId(clubId != 0 ? clubId : null)
                    .build();

            bringOutLog.setType(UserBalanceAuditLog.Type.BRING_OUT.getValue());
            bringOutLog.setDescription(String.format("%d %s", roomId, UserBalanceAuditLog.Type.BRING_OUT.getDesc()));
            bringOutLog.setBalanceChange(sum);

            UserInfoDao userInfoDao = new UserInfoDaoImp();
            if(room.getTribeRoomType() == 0){ // 俱乐部房　私人房
                int balanceBefore = userInfoDao.queryUserGold(conn, userId);
                userInfoDao.updateUserGold(conn, userId, sum);
                bringOutLog.setBalanceType(UserBalanceAuditLog.BalanceType.GOLD.getValue());
                bringOutLog.setBalanceBefore(balanceBefore);
            }else { // 聯盟房
                int balanceBefore = userInfoDao.queryUserTribeChip(conn, clubId, userId);
                userInfoDao.updateUserTribeChip(conn, userId, clubId, sum);
                bringOutLog.setTribeId(room.getTribeId());
                bringOutLog.setBalanceType(UserBalanceAuditLog.BalanceType.TRIBE_CHIP.getValue());
                bringOutLog.setBalanceBefore(balanceBefore);
            }
            IUserBalanceAuditDao auditDao = new UserBalanceAuditDaoImpl();
            auditDao.addUserBalanceAuditLog(bringOutLog);
        }
    }

    public void correctBlindPosition(Room room) {
        // 只有出现 room.manzhuNumber = room.damanzhuNumber = room.zhuangjiaNumber的情况才需要纠正
        if (room.manzhuNumber == room.damanzhuNumber && room.damanzhuNumber == room.zhuangjiaNumber) {
            logger.debug("before：room.manzhuNumber=" + room.manzhuNumber + " room.damanzhuNumber=" + room.damanzhuNumber + " room.zhuangjiaNumber=" + room.zhuangjiaNumber);
            // 当大小盲位置相同时，则根据当前的根据当前大盲找到小盲位置room.getRoomPersions()
            logger.debug("room.getRoomPersions().length=" + room.getRoomPersions().length);
            logger.debug("room.getRoomPersions()=" + JSON.toJSONString(room.getRoomPersions()));
            for (int i = 0; i < room.getRoomPersions().length; i++) {
                if (room.getRoomPersions()[i] != null && room.getRoomPersions()[i].isCanPlay() && i != room.manzhuNumber) {
                    room.damanzhuNumber = i;
                    break;
                }
            }
            logger.debug("after：room.manzhuNumber=" + room.manzhuNumber + " room.damanzhuNumber=" + room.damanzhuNumber + " room.zhuangjiaNumber=" + room.zhuangjiaNumber);
        }
    }
}

