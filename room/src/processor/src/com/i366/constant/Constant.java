
package com.i366.constant;

import java.util.HashMap;

/**
 * 常量类
 */
public class Constant {

	public static final String IP_LIMIT_TAKE_EFFICACY = "0";//ip限制生效
	public static final String IP_LIMIT_LOSE_EFFICACY = "1";//ip限制失效

	public static final int ROOM_PATH_NORMAL = 61;		// 普通局
    public static final int ROOM_PATH_THANOFF = 62;		// 必下场
    public static final int ROOM_PATH_AOF = 63;			// AOF

    public static final int REQ_GAME_ROOM_XITIAO = 53;  //97

	public static final int REQ_GAME_ENTER_ROOM = 12;  //进入房间
	public static final int REQ_GAME_SEND_SEAT_ACTION = 18;  //坐下
	public static final int REQ_GAME_SEND_ACTION = 19;    //加注
	public static final int REQ_GAME_RECV_ACTION = 20;  //49最小下注额
	public static final int REQ_GAME_RECV_LEAVE = 22;   //50
	public static final int REQ_GAME_RECV_SEAT_DOWN = 23;  //51
	public static final int REQ_GAME_RECV_CARDS = 24;    //52发牌后第一个操作的人
	public static final int REQ_GAME_RECV_WINNER = 25;   //53
	public static final int REQ_GAME_RECV_READYTIME = 26;  //55
	public static final int REQ_GAME_RECV_START_INFOR = 27; //56游戏开始时
	public static final int REQ_GAME_RECV_ADD_CHIPS = 28;  //57 带入操作
	public static final int REQ_GAME_GET_PRIVILEGE=29;    //查看是否为特殊用户和剩余牌
	public static final int REQ_GAME_ENTER_PENDING = 30;       // 进入房间前等待状态 66
	public static final int REQ_GAME_START_ROOM = 32;          // 房主开始游戏(拍桌页) 67
	public static final int REQ_GAME_ENDING = 33;              // 牌局(房间)时间到 68
	public static final int REQ_GAME_CARRY_CONTROL_SWITCH = 34;	// 控制带入开关 69
	public static final int REQ_GAME_SET_PRIVILEGE=35;         //换牌操作
	public static final int REQ_GAME_CARRY_RANGE_CHANGE = 40;  // 带入范围修改 71
	public static final int REQ_GAME_AHEAD_SHOW_CARDS = 42;    // 桌面都allin了，提前展示用户手牌，然后再展示底牌 72
	public static final int REQ_GAME_SHOW_CARDS = 43;           // 亮手牌牌 73
	public static final int REQ_GAME_SHOW_CARD_FOR_GAME_END = 44;//比牌后亮牌触发，收到其他玩家的亮牌协议
	public static final int REQ_GAME_START_STRADDLE = 66;     // 开局straddle信息 156
	public static final int REQ_REQUEST_STRADDLE = 67;        // straddle请求 157
	public static final int REQ_REQUEST_AUTO_OP = 68;         // 托管请求 158
	public static final int REQ_REQUEST_FORCE_STOP = 69;      // 解散房间（强制结束房间） 159
	public static final int REQ_CHECK_FOR_PERMISSION = 72;    // 查询对某玩家是否有踢出权限 165
	public static final int REQ_GAME_ROOM_PROGRESS = 80;      // 房间进度更新 182
	public static final int REQ_GAME_REPORT = 82;             // 即时战绩接口 185
	public static final int REQ_GAME_USER_DELAY = 86;         // 玩家操作延时 186
	public static final int REQ_GAME_ROOM_PAUSE = 87;         // 房间暂停 187
	public static final int REQ_GAME_PREV_GAME_REVIEW = 90;   // 上局回顾 191
	public static final int REQ_GAME_PREV_GAME_COLLECT = 118;  // 上局回顾收藏 217
	public static final int REQ_GAME_PLAY_TYPE = 122;		   // 补盲or过庄请求 222
	public static final int REQ_OPERATE_LEFT_TIME = 123;	   // 玩家剩余操作时间 123
	public static final int REQ_INSURANCE_OPEN = 140;		   // 保险触发 400
	public static final int REQ_INSURANCE_OPERATE = 141;	   // 保险操作 401
	public static final int REQ_INSURANCE_RESULT = 142;	   	   // 保险赔付结果 402
	public static final int REQ_INSURANCE_ADD_TIME = 143;	   // 保险加时 403
	public static final int REQ_INSURANCE_OUTS_EXCEEDED = 144;	   // 保险无合法outs 404
    public static final int REQ_REQUEST_BROADCAST = 268;       // 牌局内广播 668
    public static final int REQ_JACKPOT_BROADCAST = 269;       // 牌局内击中jackpot广播 269
    public static final int REQ_JACKPOT_CHANGE = 78;           // 牌局内jackpot更新 78

	public static final int REQ_SHOW_CARDS_BY_USER = 148;      // 花钻石看底牌 410
	public static final int REQ_SHOW_CARDS_BY_WIN_USER = 149;      // 花钻石看玩家赢家的手牌
	public static final int REQ_NOTIFY_CLEAR_TABLE = 151;      // 通知玩家情况牌桌数据 411
	public static final int REQ_OCCUPY_SEAT = 152;             // 离桌留座  412
	public static final int REQ_ROOM_ADD_TIME = 153;              // 房间延时操作 413
	public static final int REQ_ROOM_STRADDLE_SWITCH = 154;    // straddle开关 414
    public static final int REQ_ROOM_MUCK_SWITCH = 155;    // muck开关  415
	public static final int REQ_WAITE_SEAT = 156;             // 请求带入等待  412
    public static final int REQ_RES_JACKPOT = 718;           // res jackPot击中通知

	public static final int REQ_REQUEST_POOLINFO = 120;		   // 分池数据 220

	public static final int REQ_ROOM_VOICE_SWITCH = 160;			//观众语音开关

	public static final long STATUS_2_DC = 0 * 1000; 		// 准备开始倒计时为7秒

	public static final int BIPAI_TIME = 7500;         	// 比牌时间
	public static final int BIGCARD_SHOW_TIME = 0;  	// 大牌展示时间

	public static final int ROOM_MAX_AUD = 100;			// 房间最多人数
	public static final int ROOM_MAX_PLAYER = 9;		// 房间最多玩家数（座位数）

	public static final int PAUSE_TIME = 600 * 1000; 	// 房间一次暂停时间
	public static final int EXTRA_TIME = 2000;			// 房间当前操作玩家额外时间（包括动画展示等非等待操作时间，起缓冲用）
	public static final int FLOP_TIME = 800;			// 翻牌动画时间
	public static final int INSURE_FLOP_TIME = 1500;    // 保险翻牌动画时间
	public static final int REAL_OPERATE_TIME = 15000;	// 玩家实际最长操作时间（在不断网情况下）
	public static final int MAX_OPERATE_TIME = 35;		// 玩家最大操作时间（包括断网时间），即最大忍耐时间(s)
	public static final int FIRST_OPERATE_DELAY_TIME = 3;		// 每手第一个操作的玩家延时时间(s)

	public static final int MAX_TIMEOUT_CHECK_TIME = 4;    // 最大允许未操作次数，弃牌站起
	public static final int MAX_TIMEOUT_FOLD_TIME = 2;     // 最大允许超时fold次数，站起

	public static final int INSURANCE_OPERATE_TIME = 30 * 1000;	// 保险默认操作时间30s
	public static final int INSURANCE_DELAY_OPERATE_TIME = 35 * 1000;	// 保险超时操作时间35s 考虑网络延时情况

	public static final long MAX_PENDING_TIME = 3000;              // 花钻石看底牌的最长可操作时间5S
	public static final long MAX_OCCUPY_SEAT_TIME = 180 * 1000;    // 离桌留座最长时间180s
	public static final double MIN_GPS_DISTANCE = 700.0;      // 最小的GPS距离限制为700m

    public static final int MAX_AUDIENCE = 19;      // 即时战绩接口显示的观众人数

    public static final long ONE_DAY_SECONDS = 60 *60 * 24;//一天的秒数，用于超过24小时，自动解散房间时使用

	public static final int PUMPING_MODE=4;

	public static final int JK_PUMP=1;

	public static final int INSURANCE_REBATE=2;
	//有效池抽取
	public static final  int BOTTOM_POOL_PUMP=0;
	//赢取玩家抽取
	public static final  int WIN_USER_PUMP=1;
	//牌局服务费类型
	public static final  int CHIP_FEE=0;
	//保险抽成费用
	public static final  int INSURANCE_FEE=1;
	//彩金服务费
	public static final  int JP_FEE=2;
	/**
	 * 站起/踢出/坐下state常量1 "座位已被别的玩家坐下";2:"有相同IP玩家";3:"游戏即将结束";4:"信用额度不足"; 	5:"正在游戏中，无法站起";6:$"未知错误[6]"; 7:
	 *  "有GPS位置相近玩家";8:$"未知错误[8]";9:"强制站起/提出玩家失败"; 10:"强制站起/提出玩家成功";" 11: "你将在这手牌结束后提前离桌";12: "坐下失败，已提前离桌";
	 *  13: 账号已经被冻结; 14: 不允许以另一俱乐部身份參與; 15: 餘额不足无法入座；16 用户限制坐下; 17 俱乐部限制坐下; 18 俱乐部虧损限制
	 */
	public static final  int SEAT_OCCUPIED=1;
	public static final  int SAME_IP=2;
	public static final  int GAME_OVER=3;
	public static final  int INSUFFICIENT_CREDIT_LINE=4;
	public static final  int PLAY_NO_STAND_UP=5;
	public static final  int UP_TO_DATE_ERROR=6;
	public static final  int SAME_GPS=7;
	public static final  int UP_TO_DATE_ERROR1=8;
	public static final  int FORCE_FAIL=9;
	public static final  int FORCE_SUCCESS=10;
	public static final  int LEAVE_THE_TABLE_EARLY=11;
	public static final  int SITTING_DOWN_FAILED=12;
	public static final  int ACCOUNT_FROZEN=13;
	public static final  int SAME_CLUB_ONLY=14;
	public static final  int INSUFFICIENT_BALANCE=15;
	// 个人限制坐下
	public static final  int USER_LIMIT_SIT=16;
	// 俱乐部限制坐下
	public static final  int CLUB_LIMIT_SIT=17;
	// 俱乐部虧损限制
	public static final  int CLUB_LOSS_LIMIT_EXCEEDED=18;

	// 用户层级限制
	public static final  int USER_TIER_LIMIT = 20;

    /**
	 * 花金豆看底牌的消费表
	 */
	public final static HashMap<Integer, Integer> USER_SHOW_CARDS_FEE = new HashMap<Integer, Integer>() {
        {
    	    put(1, 40);   put(2, 60);   put(5, 100);   put(10, 120);
    	    put(20, 140); put(25, 140); put(50, 140); put(100, 160);
            put(200, 180); put(300, 200); put(500, 240); put(1000, 300);
        }
    };

}