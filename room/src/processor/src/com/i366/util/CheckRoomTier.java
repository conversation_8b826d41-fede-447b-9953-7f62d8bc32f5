package com.i366.util;


import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.constant.EAiMode;
import com.dzpk.commission.repositories.mysql.RoomTierDao;
import com.dzpk.commission.repositories.mysql.impl.RoomTierDaoImpl;
import com.dzpk.commission.repositories.mysql.model.UserTribeRoomTier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * CheckRoomTier
 *
 * <AUTHOR>
 * @since 2025/6/4
 */
@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckRoomTier {

    private int roomId;
    private int tribeId;
    private int roomTierId;
    private int clubId;
    private int userId;
    private int roomOwner;
    private boolean isPrivilege;
    private boolean isTribeRoom;


    /**
     * 检查用户是否有权限进入房间
     * @return true: 有权限进入房间; false: 没有权限进入房间
     */
    public boolean check() {
        EAiMode eAiMode = AiRoomManager.getModeOfPlayer(roomId,userId);
        if (eAiMode != null) {
            // 如果不为空则为AI玩家
            return  true;
        }
        // 如果是房主，则不需要检查房间等级
        if (this.roomOwner == this.userId) {
            return true;
        }
        // 如果是特权用户，则不需要检查房间等级
//        if (this.isPrivilege) {
//            return true;
//        }
        // 如果不是联盟房间，则不需要检查房间等级
        if (!isTribeRoom) {
            return true;
        }
        RoomTierDao roomTierDao = new RoomTierDaoImpl();
        UserTribeRoomTier userTribeRoomTier = roomTierDao.getUserTribeRoomTier(this.tribeId, this.userId);
        log.info("CheckRoomTier 用户 {} 进入房间 {}, 用户房间等级信息: {}", this.userId, this.roomId, userTribeRoomTier);
        if (null == userTribeRoomTier) {
            // 如果没有找到用户的房间等级信息，则提示没有权限进入房间
            return false;
        }
        Integer roomTierValue = roomTierDao.getRoomTierValue(this.roomId);
        log.info("CheckRoomTier 房间 {} 的等级信息: {}", this.roomId, roomTierValue);
        if (null == roomTierValue) {
            // 如果没有找到房间的等级信息，则提示没有权限进入房间
            return false;
        }
        // 当前用户等级小于房间登记才能进入
        return userTribeRoomTier.getValue() <= roomTierValue;
    }
}
