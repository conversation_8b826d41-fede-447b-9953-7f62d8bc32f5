package com.dzpk.crazypoker.appmessage.rabbitmq.receiver;

import com.alibaba.fastjson.JSON;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.AppMessage;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.AppNotice;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.config.AppMessageRabbitMqKeys;
import com.dzpk.crazypoker.appmessage.service.AppMessageService;
import com.dzpk.crazypoker.util.JSONObjectUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * AppMessageReceiver
 *
 * <AUTHOR>
 * @date 2024/11/10
 */
@Slf4j
@Component
public class AppMessageReceiver {

    @Resource
    AppMessageService appMessageService;

    @Resource
    ObjectMapper objectMapper;

    @RabbitListener(
            concurrency = "10",
            bindings = @QueueBinding(
                    value = @Queue(
                            value = AppMessageRabbitMqKeys.Queue.MESSAGE,
                            durable = "true"
                    ),
                    exchange = @Exchange(
                            value = AppMessageRabbitMqKeys.Exchange.MESSAGE,
                            type = ExchangeTypes.TOPIC
                    ),
                    key = AppMessageRabbitMqKeys.RoutingKey.MESSAGE
            ))
    public void handleMessage(
            @Payload JsonNode jsonNode,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        try {
            AppMessage appMessage = objectMapper.treeToValue(jsonNode, AppMessage.class);
            // 处理消息
            Boolean sendResult = appMessageService.sendMessage(appMessage);
            if (sendResult) {
                // 手动确认消息
                if (channel.isOpen()) {
                    channel.basicAck(deliveryTag, false);
                } else {
                    log.error("Channel is closed, cannot acknowledge message with deliveryTag: {}", deliveryTag);
                }
            } else {
                log.error("Failed to send message: {}", JSON.toJSONString(appMessage));
                // 直接拒绝消息
                channel.basicNack(deliveryTag, false, false);
            }
        } catch (Exception e) {
            log.error("Failed to process message: {}", jsonNode, e);
            try {
                // 拒绝消息并将其重新入队
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ex) {
                log.error("Failed to nack message", ex);
            }
        }
    }


    @RabbitListener(
            concurrency = "10",
            bindings = @QueueBinding(
                    value = @Queue(
                            value = AppMessageRabbitMqKeys.Queue.NOTICE,
                            durable = "true"
                    ),
                    exchange = @Exchange(
                            value = AppMessageRabbitMqKeys.Exchange.NOTICE,
                            type = ExchangeTypes.TOPIC
                    ),
                    key = AppMessageRabbitMqKeys.RoutingKey.NOTICE
            ))
    public void handleNotice(
            @Payload JsonNode jsonNode,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        String jsonNotice = jsonNode.toString();
        try {
            // 先按照json格式解析
            AppNotice appNotice = objectMapper.treeToValue(jsonNode, AppNotice.class);
            // 处理消息
            Boolean sendResult = appMessageService.sendNotice(appNotice);
            if (sendResult) {
                // 手动确认消息
                channel.basicAck(deliveryTag, false);
            } else {
                log.error("Failed to send notice: {}", JSON.toJSONString(appNotice));
                // 直接拒绝消息
                channel.basicNack(deliveryTag, false, false);
            }
        } catch (Exception e) {
            log.error("Failed to process notice: {}", jsonNotice, e);
            try {
                // 拒绝消息
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ex) {
                log.error("Failed to nack notice", ex);
            }
        }
    }


}
